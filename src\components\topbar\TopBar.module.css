/* استخدام متغيرات CSS من الملف الرئيسي */
@import '../../styles/variables.css';

.headerContainer {
  position: sticky;
  top: 0;
  z-index: 1000;
  width: 100%;
}

.topBar {
  width: 100%;
  height: 55px;
  display: flex;
  align-items: flex-start;
  justify-content: flex-start; /* جعل جميع الأزرار في أقصى اليسار */
  padding: 0.1rem 0.4rem 0;
  background: var(--current-bg-primary);
  border-bottom: 1px solid var(--current-border-primary);
  color: var(--current-text-primary);
  transition: all var(--transition-normal);
  backdrop-filter: var(--current-backdrop-blur);
  position: relative;
}

.liquid {
  background: rgba(255, 255, 255, 0.08); /* شفافية خفيفة */
  backdrop-filter: blur(5px) saturate(180%);
  -webkit-backdrop-filter: blur(5px) saturate(180%);
  border: 1px solid rgba(255, 255, 255, 0.12); /* حد خفيف */
  transition: all 0.3s ease;
  overflow: hidden;
  position: relative;
}
.liquid::after {
  content: "";
  position: absolute;
  top: -50%;
  left: -50%;
  width: 200%;
  height: 200%;
  background: linear-gradient(
    130deg,
    rgba(255, 255, 255, 0.15) 0%,
    rgba(255, 255, 255, 0.05) 50%,
    rgba(255, 255, 255, 0.15) 100%
  );
  transform: rotate(0deg);
  animation: shineMove 6s linear infinite;
  pointer-events: none;
}




.leftSection {
  display: flex;
  align-items: flex-start;
  gap: 0.5rem;
  padding-left: 0;
}

.rightSection {
  display: flex;
  align-items: flex-start;
  gap: 0.5rem;
  padding-top: 0.5rem;
  padding-right: 0;
}

.iconWrapper {
  position: relative;
}

.iconButton {
  background-color: var(--btn-icon-bg);
  width: var(--btn-icon-size);
  height: var(--btn-icon-size);
  border-radius: var(--radius-full);
  display: flex;
  align-items: center;
  justify-content: center;
  color: var(--current-text-secondary);
  cursor: pointer;
  padding: var(--spacing-sm);
  margin: 0;
  border: var(--btn-icon-border);
  transition: var(--btn-transition);
  backdrop-filter: var(--current-backdrop-blur);

}


.tooltip {
  position: absolute;
  bottom: -35px;
  left: 50%;
  transform: translateX(-50%);
  background: rgba(0, 0, 0, 0.8);
  color: white;
  padding: 0.3rem 0.6rem;
  border-radius: 4px;
  font-size: 0.7rem;
  white-space: nowrap;
  opacity: 0;
  pointer-events: none;
  transition: opacity 0.3s ease;
  z-index: 1000;
}

.iconWrapper:hover .tooltip {
  opacity: 1;
}

.profileDropdown {
  position: fixed;
  top: 0;
  right: 0;
  width: 100%;
  height: 100%;
  background-color: rgba(0, 0, 0, 0.5);
  z-index: 1050;
  display: flex;
  justify-content: center;
  align-items: center;
  animation: fadeIn 0.3s ease;
  backdrop-filter: blur(4px);
}

@keyframes fadeIn {
  from { opacity: 0; }
  to { opacity: 1; }
}

.menuContainer {
  display: flex;
  flex-direction: column;
  gap: 8px;
  padding: 20px;
  max-width: 300px;
  width: 100%;
}

.dropdownItem {
  width: 100%;
  min-height: 60px;
  display: flex;
  align-items: center;
  justify-content: space-between;
  gap: 0.75rem;
  padding: 1rem 1.5rem;
  background: var(--current-bg-primary);
  border: 1px solid var(--current-border-primary);
  color: var(--current-text-primary);
  text-align: right;
  cursor: pointer;
  font-size: 1rem;
  transition: all 0.3s ease;
  font-weight: 600;
  box-shadow: var(--current-shadow-medium);
  animation: slideUp 0.4s ease forwards;
  animation-delay: calc(var(--item-index) * 0.1s);
  opacity: 0;
  border-radius: 9999px;
  backdrop-filter: var(--current-backdrop-blur);
  position: relative;
}

.dropdownItem:hover {
  background-color: var(--current-bg-secondary);
  color: var(--primary-color);
  transform: translateY(-2px);
  box-shadow: var(--current-shadow-heavy);
  border-color: var(--primary-color);
}

.dropdownItem svg {
  color: #4c68c0;
  width: 20px;
  height: 20px;
}

@keyframes slideUp {
  from { transform: translateY(50px); opacity: 0; }
  to { transform: translateY(0); opacity: 1; }
}

.userPhoto {
  width: 100%;
  height: 100%;
  border-radius: 50%;
  object-fit: cover;
  display: block;
}

.userInitials {
  width: 100%;
  height: 100%;
  display: flex;
  align-items: center;
  justify-content: center;
  font-weight: bold;
  font-size: 1.2rem;
  background: linear-gradient(135deg, var(--neutral-700) 0%, var(--neutral-800) 100%);
  color: white;
  border-radius: 50%;
}

.addCaseWrapper {
  /* تم إزالة الـ positioning المطلق ليتبع ترتيب العناصر الطبيعي */
}



