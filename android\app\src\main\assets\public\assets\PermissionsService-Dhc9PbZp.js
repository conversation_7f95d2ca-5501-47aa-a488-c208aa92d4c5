import{d,b as c,g as f,u as $,e as m,q as w,w as p,f as O,m as L,h as b}from"./index-HUlFBKIW.js";const _="activeAccount",P="localCases_",h="localCase_",D="localNotifications_",i=()=>localStorage.getItem(_)||"online",J=s=>{if(s!=="online"&&s!=="local"){console.error("قيمة غير صالحة للحساب النشط:",s);return}localStorage.setItem(_,s),console.log("تم تغيير الحساب النشط إلى:",s)},l=s=>{const e=localStorage.getItem(`${P}${s}`);return e?JSON.parse(e):[]},S=(s,e)=>{localStorage.setItem(`${P}${s}`,JSON.stringify(e))},C=(s,e)=>{const t=localStorage.getItem(`${h}${s}_${e}`);if(t){const o=JSON.parse(t);return o.id||(o.id=e),o}return null},y=(s,e,t)=>{localStorage.setItem(`${h}${s}_${e}`,JSON.stringify(t))},A=(s,e,t)=>{const o=l(s),r=o.findIndex(a=>a.id===e);r!==-1?o[r]={...t,id:e}:o.push({...t,id:e}),S(s,o)},x=(s,e)=>{localStorage.removeItem(`${h}${s}_${e}`);const o=l(s).filter(r=>r.id!==e);S(s,o)},F=(s,e)=>{localStorage.setItem(`${D}${s}`,JSON.stringify(e))},q=async s=>{if(i()==="online")try{const t=m(c,"cases"),o=w(t,p("userId","==",s));return(await O(o)).docs.map(n=>({id:n.id,...n.data()})).filter(n=>!n.isHidden)}catch(t){return console.error("خطأ في جلب بيانات القضايا من Firestore:",t),[]}else{console.log("🔍 جلب القضايا من التخزين المحلي للمستخدم:",s);const t=l(s);console.log("📊 إجمالي القضايا ا��محلية قبل الفلترة:",t.length);const o=t.filter(r=>!r.isHidden);return console.log("✅ القضايا المفلترة:",o.length),o}},E=async s=>{if(i()==="online")try{const t=m(c,"cases"),o=w(t,p("userId","==",s));return(await O(o)).docs.map(a=>({id:a.id,...a.data()}))}catch(t){return console.error("خطأ في جلب جميع بيانات القضايا من Firestore:",t),[]}else return l(s)},U=async(s,e)=>{if(i()==="online")try{const o=d(c,"cases",e),r=await f(o);return r.exists()&&r.data().userId===s?{id:r.id,...r.data()}:null}catch(o){return console.error("خطأ في جلب بيانات القضية من Firestore:",o),null}else return C(s,e)},B=async(s,e)=>{if(i()==="online")try{const o=m(c,"cases");return{id:(await L(o,{...e,userId:s})).id,...e}}catch(o){throw console.error("خطأ في إضافة القضية إلى Firestore:",o),o}else{const o=`local_${Date.now()}`,r={...e,id:o,userId:s};console.log("💾 حفظ قضية محلية جديدة:"),console.log("🆔 معرف القضية:",o),console.log("👤 معرف المستخدم:",s),console.log("📄 بيانات القضية:",r),y(s,o,r),A(s,o,r);const a=l(s);return console.log("✅ إجمالي القضايا بعد الحفظ:",a.length),console.log("🔍 القضية الجديدة محفوظة؟",a.some(n=>n.id===o)),r}},N=async(s,e,t)=>{if(i()==="online")try{const r=d(c,"cases",e),a=await f(r);let n=t.createdBy;return!n&&a.exists()&&(n=a.data().createdBy),n||(n=s),await $(r,{...t,createdBy:n}),{id:e,...t,createdBy:n}}catch(r){throw console.error("خطأ في تحديث القضية في Firestore:",r),r}else{const r=C(s,e);if(!r)throw new Error("القضية غير موجودة في التخزين المحلي");const a={...r,...t};return y(s,e,a),A(s,e,a),a}},K=async(s,e,t)=>{if(i()==="online")try{const r=b(c),a=d(c,"cases",e),n=await f(a);if(n.exists()){const R=n.data();if(!Object.keys(t).some(v=>{const g=R[v],u=t[v];return Array.isArray(g)&&Array.isArray(u)?JSON.stringify(g)!==JSON.stringify(u):g!==u})){console.log("لا توجد تغييرات للحفظ، تخطي العملية");return}}return r.update(a,{...t,updatedAt:new Date().toISOString()}),await r.commit(),console.log("✅ تم تحديث القضية بنجاح باستخدام العمليات المجمعة"),{id:e,...t}}catch(r){throw console.error("خطأ في تحديث القضية في Firestore:",r),r}else return N(s,e,t)},k=Object.freeze(Object.defineProperty({__proto__:null,addCase:B,deleteLocalCase:x,getActiveAccount:i,getAllCases:E,getCase:U,getCases:q,getLocalCase:C,getLocalCases:l,saveLocalCase:y,saveLocalCases:S,saveLocalNotifications:F,setActiveAccount:J,updateCase:N,updateCaseOptimized:K,updateLocalCasesList:A},Symbol.toStringTag,{value:"Module"}));class M{constructor(){this.storageKey="user_permissions"}savePermissions(e){try{localStorage.setItem(this.storageKey,JSON.stringify(e))}catch(t){console.error("خطأ في حفظ الصلاحيات:",t)}}getPermissions(){try{const e=localStorage.getItem(this.storageKey);if(e)return JSON.parse(e)}catch(e){console.error("خطأ في جلب الصلاحيات:",e)}return{admin:{deleteData:!0,addData:!0,addCases:!0,viewNotifications:!0,manageMembers:!0,assignTasks:!0},editor:{deleteData:!1,addData:!0,addCases:!0,viewNotifications:!0,manageMembers:!1,assignTasks:!0},member:{deleteData:!1,addData:!1,addCases:!1,viewNotifications:!0,manageMembers:!1,assignTasks:!1}}}getRolePermissions(e){return this.getPermissions()[e]||{}}hasPermission(e,t){return this.getRolePermissions(e)[t]||!1}updatePermission(e,t,o){const r=this.getPermissions();r[e]&&(r[e][t]=o,this.savePermissions(r))}getCurrentUserRole(e){try{return"admin"}catch(t){return console.error("خطأ في جلب دور المستخدم:",t),"admin"}}setUserRole(e,t){try{const o=localStorage.getItem("user_roles");let r={};o&&(r=JSON.parse(o)),r[e]=t,localStorage.setItem("user_roles",JSON.stringify(r))}catch(o){console.error("خطأ في حفظ دور المستخدم:",o)}}currentUserHasPermission(e,t){const o=this.getCurrentUserRole(e);return this.hasPermission(o,t)}}const H=new M;export{k as S,N as a,B as b,q as c,F as d,U as e,x as f,i as g,H as p,J as s,K as u};
