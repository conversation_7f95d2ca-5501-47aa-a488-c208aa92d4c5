import{c as P,r as f,j as t,L as q,d as B,b as x,g as M,u as A,e as H,q as R,w as K,f as J,h as _}from"./index-HUlFBKIW.js";import{F as L,a as N,b as W,c as D,d as G,e as Q,f as X,g as Y,h as Z,i as U,j as aa,k as I}from"./index-DhfUDJs-.js";import{T as k}from"./TopBar-rd4Lqjij.js";import{s as a}from"./ProfilePage.module-DwB6uXPZ.js";import{g as ta,s as w}from"./PermissionsService-Dhc9PbZp.js";import{S as e}from"./sweetalert2.esm.all-DWO__QVt.js";import"./iconBase-BPj5F03O.js";const pa=({currentUser:s})=>{P();const[g,C]=f.useState(null),[F,y]=f.useState(!0),[sa,u]=f.useState(null),[$,j]=f.useState({name:"",email:"",phone:""}),[la,b]=f.useState(null),[d,r]=f.useState(ta()),[h,v]=f.useState(null);f.useEffect(()=>{(async()=>{if(!s||!s.uid){u("المستخدم غير مسجل الدخول. يرجى تسجيل الدخول مرة أخرى."),y(!1);return}y(!0),u(null);try{const n=B(x,"users",s.uid),c=await M(n);if(c.exists()){const p=c.data();C({...p,name:p.name||p.username||(s==null?void 0:s.displayName)||(s==null?void 0:s.email)||""}),d==="online"&&j({name:p.name||p.username||(s==null?void 0:s.displayName)||(s==null?void 0:s.email)||"",email:p.email||"",phone:p.phone||""})}else console.log("لم يتم العثور على بيانات المستخدم في Firestore.")}catch(n){console.error("خطأ في جلب بيانات المستخدم من Firestore:",n.message)}try{const n=localStorage.getItem("localUserData_"+s.uid);if(n){const c=JSON.parse(n);v({...c,name:c.name||c.username||(s==null?void 0:s.displayName)||(s==null?void 0:s.email)||""}),d==="local"&&j({name:c.name||c.username||(s==null?void 0:s.displayName)||(s==null?void 0:s.email)||"",email:c.email||"",phone:c.phone||""})}else console.log("لم يتم العثور على بيانات محلية للمستخدم.")}catch(n){console.error("خطأ في قراءة البيانات المحلية:",n.message)}y(!1)})();const m=localStorage.getItem("activeAccount");m&&w(m);const i=()=>{!navigator.onLine&&d==="online"&&alert("أنت الآن غير متصل بالإنترنت. قد تواجه مشكلات في الوصول إلى بيانات الحساب الأونلاين. يمكنك التبديل إلى الحساب المحلي للاستمرار في العمل دون اتصال.")};return window.addEventListener("online",i),window.addEventListener("offline",i),()=>{window.removeEventListener("online",i),window.removeEventListener("offline",i)}},[s,d]);const S=async(l,m)=>{if(!(s!=null&&s.uid)){u("المستخدم غير مسجل الدخول.");return}if(l==="name"&&!m){u("الاسم مطلوب.");return}try{u(null);const i={[l]:m};if(d==="online"){const n=B(x,"users",s.uid);await A(n,{...i,lastUpdateTime:new Date().toISOString()}),C(c=>({...c,...i,lastUpdateTime:new Date().toISOString()})),e.fire({title:"تم الحفظ!",text:"تم حفظ البيانات بنجاح. ستتم مزامنتها مع الحساب السحابي عند توفر الاتصال بالإنترنت.",icon:"success",customClass:{popup:a["swal2-popup"],title:a["swal2-title"],htmlContainer:a["swal2-html-container"],confirmButton:a["swal2-confirm"]},buttonsStyling:!1})}else{const n={...h||{},uid:s.uid,...i,lastUpdatedAt:new Date().toISOString()};localStorage.setItem("localUserData_"+s.uid,JSON.stringify(n)),v(n),e.fire({title:"تم الحفظ!",text:"تم حفظ البيانات بنجاح في الحساب المحلي.",icon:"success",customClass:{popup:a["swal2-popup"],title:a["swal2-title"],htmlContainer:a["swal2-html-container"],confirmButton:a["swal2-confirm"]},buttonsStyling:!1})}b(null)}catch(i){u("خطأ في تحديث تحديث البيانات: "+i.message),d==="online"&&!navigator.onLine&&e.fire({title:"خطأ في الحفظ",text:"حدث خطأ في حفظ البيانات في الحساب الأونلاين. هل ترغب في التبديل إلى الحساب المحلي؟",icon:"error",showCancelButton:!0,confirmButtonText:"نعم، التبديل",cancelButtonText:"إلغاء"}).then(n=>{n.isConfirmed&&E()})}},E=()=>{w("local"),r("local"),window.location.reload()},V=async()=>{const{isConfirmed:l}=await e.fire({title:"إنشاء حساب أوفلاين جديد",html:`
        <div style="text-align: right;">
          <p>نوفر لكم إمكانية إنشاء <strong>حساب واحد فقط من كل نوع</strong>:</p>
          <ul style="margin: 0 0 10px 24px; padding: 0; list-style-position: inside; direction: rtl; text-align: right;">
            <li style="margin-bottom: 4px;">حساب <strong>سحابي (أونلاين)</strong> يُخزّن البيانات على قاعدة بيانات سحابية للوصول إليها من أي مكان.</li>
            <li>حساب <strong>محلي (أوفلاين)</strong> يُخزّن البيانات على الجهاز للاستخدام بدون اتصال بالإنترنت.</li>
          </ul>
          <p>يمكنكم أيضًا <strong>حذف أي حساب</strong> في أي وقت لضمان التحكم الكامل في بياناتكم.</p>
          <div style="background-color: #fff3cd; border: 1px solid #ffeaa7; border-radius: 8px; padding: 15px; margin-top: 15px; text-align: right;">
            <div style="font-weight: bold; color: #856404; margin-bottom: 8px;">⚠️ ملاحظة مهمة:</div>
            <p style="margin: 0; color: #856404; font-size: 0.95rem;">
              سيتم إنشاء حساب فارغ جديد. يمكنك نقل البيانات لاحقاً من الحساب الأونلاين إذا أردت.
            </p>
          </div>
          <p style="margin-top: 20px; font-weight: 500;">هل تريد المتابعة وإنشاء الحساب الأوفلاين؟</p>
        </div>
      `,icon:"info",showCancelButton:!0,confirmButtonText:"نعم، إنشاء الحساب",cancelButtonText:"إلغاء",customClass:{popup:a["swal2-popup"],title:a["swal2-title"],htmlContainer:a["swal2-html-container"],confirmButton:a["swal2-confirm"],cancelButton:a["swal2-cancel"],actions:a["swal2-actions"]},buttonsStyling:!1});if(l){if(!(s!=null&&s.uid))return;const m={uid:s.uid,name:s.displayName||"",email:s.email||"",phone:"",cases:[],createdAt:new Date().toISOString(),lastUpdatedAt:new Date().toISOString()};localStorage.setItem("localUserData_"+s.uid,JSON.stringify(m)),v(m),w("local"),r("local"),e.fire({title:"تم الإنشاء!",text:"تم إنشاء حساب أوفلاين جديد بنجاح والتبديل إليه.",icon:"success",customClass:{popup:a["swal2-popup"],title:a["swal2-title"],htmlContainer:a["swal2-html-container"],confirmButton:a["swal2-confirm"]},buttonsStyling:!1}),window.location.reload()}},T=async l=>{const{value:m}=await e.fire({title:`تأكيد حذف الحساب ${l==="online"?"الأونلاين":"المحلي"}`,html:`
        <div style="text-align: right;">
          <p style="margin-bottom: 15px;">هذا الإجراء لا يمكن التراجع عنه. لتأكيد الحذف، يرجى إدخال كلمة المرور الخاصة بك:</p>
          <input
            type="password"
            id="password-input"
            class="swal2-input"
            placeholder="كلمة المرور"
            style="direction: ltr; text-align: left;"
          />
        </div>
      `,icon:"warning",showCancelButton:!0,confirmButtonText:"تأكيد الحذف",cancelButtonText:"إلغاء",focusConfirm:!1,preConfirm:()=>{const i=e.getPopup().querySelector("#password-input").value;return i?i.length<6?(e.showValidationMessage("كلمة المرور يجب أن تكون 6 أحرف على الأقل"),!1):i:(e.showValidationMessage("يرجى إدخال كلمة المرور"),!1)},customClass:{popup:a["swal2-popup"],title:a["swal2-title"],htmlContainer:a["swal2-html-container"],confirmButton:a["swal2-confirm"],cancelButton:a["swal2-cancel"],actions:a["swal2-actions"]},buttonsStyling:!1});if(m){if(l==="online")try{const i=H(x,"cases"),n=R(i,K("userId","==",s.uid)),c=await J(n),p=_(x);c.forEach(O=>{p.delete(O.ref)});const z=B(x,"users",s.uid);await A(z,{isDeleted:!0,deletedAt:new Date().toISOString()}),C(null),e.fire({title:"تم الحذف!",text:"تم حذف الحساب الأونلاين وجميع القضايا المرتبطة به بنجاح.",icon:"success",customClass:{popup:a["swal2-popup"],title:a["swal2-title"],htmlContainer:a["swal2-html-container"],confirmButton:a["swal2-confirm"]},buttonsStyling:!1})}catch(i){console.error("خطأ في حذف البيانات:",i),e.fire("خطأ","حدث خطأ أثناء حذف البيانات. يرجى المحاولة مرة أخرى.","error")}else if(l==="local")try{Object.keys(localStorage).filter(c=>c.includes(s.uid)).forEach(c=>localStorage.removeItem(c)),v(null),d==="local"&&(w("online"),r("online")),e.fire({title:"تم الحذف!",text:"تم حذف الحساب المحلي وجميع البيانات المرتبطة به بنجاح.",icon:"success",customClass:{popup:a["swal2-popup"],title:a["swal2-title"],htmlContainer:a["swal2-html-container"],confirmButton:a["swal2-confirm"]},buttonsStyling:!1})}catch(i){console.error("خطأ في حذف البيانات المحلية:",i),e.fire("خطأ","حدث خطأ أثناء حذف البيانات. يرجى المحاولة مرة أخرى.","error")}}};if(F)return t.jsxs("div",{className:a.pageWrapper,children:[t.jsx(k,{currentUser:s}),t.jsx(q,{message:"جاري تحميل الملف الشخصي..."})]});const o=d==="online"?g:h;return t.jsxs(t.Fragment,{children:[t.jsx(k,{currentUser:s}),t.jsx("div",{className:a.pageWrapper,children:t.jsxs("div",{className:a.content,children:[t.jsxs("div",{className:a.profileHeader,children:[t.jsx("h1",{className:a.userName,children:(o==null?void 0:o.name)||(s==null?void 0:s.displayName)||(s==null?void 0:s.email)||"مستخدم"}),t.jsx("p",{className:a.welcomeMessage,children:"مرحباً بك في ملفك الشخصي!"})]}),t.jsxs("div",{className:a.sectionCard,children:[t.jsxs("div",{className:a.sectionHeader,children:[t.jsx(L,{size:20,color:"var(--primary-dark-blue)"}),t.jsx("h3",{className:a.sectionTitle,children:"المعلومات الشخصية"})]}),t.jsxs("div",{className:a.infoItem,children:[t.jsxs("span",{className:a.infoLabel,children:[t.jsx(L,{size:16})," الاسم:"]}),t.jsxs("span",{className:a.infoValue,children:[(o==null?void 0:o.name)||"غير محدد",t.jsx("button",{className:a.editButton,onClick:()=>{b("name"),e.fire({title:"تعديل الاسم",input:"text",inputValue:$.name,showCancelButton:!0,confirmButtonText:"حفظ",cancelButtonText:"إلغاء",inputValidator:l=>{if(!l)return"الاسم مطلوب!"},customClass:{popup:a["swal2-popup"],title:a["swal2-title"],htmlContainer:a["swal2-html-container"],input:a["swal2-input"],confirmButton:a["swal2-confirm"],cancelButton:a["swal2-cancel"],actions:a["swal2-actions"]},buttonsStyling:!1}).then(l=>{l.isConfirmed&&(j(m=>({...m,name:l.value})),S("name",l.value))})},children:t.jsx(N,{})})]})]}),t.jsxs("div",{className:a.infoItem,children:[t.jsxs("span",{className:a.infoLabel,children:[t.jsx(W,{size:16})," البريد الإلكتروني:"]}),t.jsxs("span",{className:a.infoValue,children:[(o==null?void 0:o.email)||(s==null?void 0:s.email)||"غير محدد",t.jsx("button",{className:a.editButton,onClick:()=>{e.fire({title:"معلومات البريد الإلكتروني",html:`
                      <div style="text-align: right;">
                        <p> لا يمكنك تغيير هذا العنوان. <strong>${(o==null?void 0:o.email)||(s==null?void 0:s.email)||"غير محدد"}</strong> يتم استخدام بريدك الإلكتروني لتسجيل الدخول وإرسال إشعارات مهمة متعلقة بحسابك. إذا كنت بحاجة إلى تغيير بريدك الإلكتروني، يرجى التواصل مع الدعم الفني.</p>
                      </div>
                    `,icon:"info",confirmButtonText:"حسناً",customClass:{popup:a["swal2-popup"],title:a["swal2-title"],htmlContainer:a["swal2-html-container"],confirmButton:a["swal2-confirm"]},buttonsStyling:!1})},children:t.jsx(N,{})})]})]}),t.jsxs("div",{className:a.infoItem,children:[t.jsxs("span",{className:a.infoLabel,children:[t.jsx(D,{size:16})," رقم الهاتف:"]}),t.jsxs("span",{className:a.infoValue,children:[(o==null?void 0:o.phone)||"غير محدد",t.jsx("button",{className:a.editButton,onClick:()=>{b("phone"),e.fire({title:"تعديل رقم الهاتف",input:"text",inputValue:(o==null?void 0:o.phone)||"",showCancelButton:!0,confirmButtonText:"حفظ",cancelButtonText:"إلغاء",customClass:{popup:a["swal2-popup"],title:a["swal2-title"],htmlContainer:a["swal2-html-container"],input:a["swal2-input"],confirmButton:a["swal2-confirm"],cancelButton:a["swal2-cancel"],actions:a["swal2-actions"]},buttonsStyling:!1}).then(l=>{l.isConfirmed&&(j(m=>({...m,phone:l.value})),S("phone",l.value))})},children:t.jsx(N,{})})]})]})]}),t.jsxs("div",{className:a.sectionCard,children:[t.jsxs("div",{className:a.sectionHeader,children:[t.jsx(G,{size:20,color:"var(--primary-dark-blue)"}),t.jsx("h3",{className:a.sectionTitle,children:"إدارة الحسابات"})]}),t.jsxs("div",{className:a.accountCardsContainer,children:[t.jsxs("div",{className:`${a.accountCard} ${d==="online"?a.active:""}`,children:[t.jsx(Q,{className:a.accountIcon}),t.jsx("h4",{className:a.accountType,children:"الحساب السحابي (أونلاين)"}),t.jsx("p",{className:a.accountEmail,children:(g==null?void 0:g.email)||(s==null?void 0:s.email)||"غير محدد"}),t.jsxs("div",{className:a.accountActions,children:[d!=="online"&&t.jsx("button",{className:`${a.actionButton} ${a.switchButton}`,onClick:()=>{w("online"),r("online"),e.fire("تم التبديل!","تم التبديل إلى الحساب السحابي بنجاح.","success")},children:"التبديل إليه"}),t.jsx("button",{className:`${a.actionButton} ${a.deleteButton}`,onClick:()=>T("online"),children:"حذف"})]})]}),h?t.jsxs("div",{className:`${a.accountCard} ${d==="local"?a.active:""}`,children:[t.jsx(X,{className:a.accountIcon}),t.jsx("h4",{className:a.accountType,children:"الحساب المحلي (أوفلاين)"}),t.jsx("p",{className:a.accountEmail,children:(h==null?void 0:h.email)||"غير محدد"}),t.jsxs("div",{className:a.accountActions,children:[d!=="local"&&t.jsx("button",{className:`${a.actionButton} ${a.switchButton}`,onClick:()=>{w("local"),r("local"),e.fire("تم التبديل!","تم التبديل إلى الحساب المحلي بنجاح.","success")},children:"التبديل إليه"}),t.jsx("button",{className:`${a.actionButton} ${a.deleteButton}`,onClick:()=>T("local"),children:"حذف"})]})]}):t.jsxs("div",{className:`${a.accountCard} ${a.addAccountCard}`,children:[t.jsx(Y,{size:40,color:"var(--primary-dark-blue)"}),t.jsx("h4",{className:a.accountType,children:"إنشاء حساب محلي جديد"}),t.jsx("button",{className:`${a.actionButton} ${a.addButton}`,onClick:V,children:"إنشاء"})]})]})]}),t.jsxs("div",{className:a.sectionCard,children:[t.jsxs("div",{className:a.sectionHeader,children:[t.jsx(Z,{size:20,color:"var(--primary-dark-blue)"}),t.jsx("h3",{className:a.sectionTitle,children:"الأمان"})]}),t.jsxs("div",{className:a.infoItem,children:[t.jsxs("span",{className:a.infoLabel,children:[t.jsx(U,{size:16})," كلمة المرور:"]}),t.jsxs("span",{className:a.infoValue,children:["••••••••",t.jsx("button",{className:a.editButton,onClick:()=>{e.fire({title:"تغيير كلمة المرور",html:`
                      <div style="text-align: right;">
                        
                        <div style="margin-bottom: 15px;">
                          <label for="swal-input1" class="swal2-label">كلمة المرور الجديدة:</label>
                          <input id="swal-input1" class="swal2-input" type="password" placeholder="كلمة المرور الجديدة" style="direction: ltr; text-align: left;" />
                        </div>
                        <div>
                          <label for="swal-input2" class="swal2-label">تأكيد كلمة المرور الجديدة:</label>
                          <input id="swal-input2" class="swal2-input" type="password" placeholder="تأكيد كلمة المرور الجديدة" style="direction: ltr; text-align: left;" />
                        </div>
                        <p style="margin-top: 20px; font-size: 0.9rem; color: var(--neutral-600);">استخدم 8 أحرف على الأقل مع مزيج من الأحرف الكبيرة والصغيرة والأرقام والرموز لإنشاء كلمة مرور قوية. تجنب استخدام معلومات شخصية يمكن تخمينها بسهولة.</p>
                      </div>
                    `,icon:"info",showCancelButton:!0,confirmButtonText:"حفظ",cancelButtonText:"إلغاء",focusConfirm:!1,preConfirm:()=>{const l=e.getPopup().querySelector("#swal-input1").value,m=e.getPopup().querySelector("#swal-input2").value;return!l||!m?(e.showValidationMessage("يرجى إدخال كلتا كلمتي المرور"),!1):l!==m?(e.showValidationMessage("كلمتا المرور غير متطابقتين"),!1):l.length<8?(e.showValidationMessage("كلمة المرور يجب أن تكون 8 أحرف على الأقل"),!1):{newPassword:l}},customClass:{popup:a["swal2-popup"],title:a["swal2-title"],htmlContainer:a["swal2-html-container"],input:a["swal2-input"],confirmButton:a["swal2-confirm"],cancelButton:a["swal2-cancel"],actions:a["swal2-actions"]},buttonsStyling:!1}).then(l=>{l.isConfirmed&&e.fire({title:"تم الحفظ!",text:"تم تحديث كلمة المرور بنجاح (هذا مجرد عرض توضيحي).",icon:"success",customClass:{popup:a["swal2-popup"],title:a["swal2-title"],htmlContainer:a["swal2-html-container"],confirmButton:a["swal2-confirm"]},buttonsStyling:!1})})},children:t.jsx(N,{})})]})]})]}),t.jsxs("div",{className:a.sectionCard,children:[t.jsxs("div",{className:a.sectionHeader,children:[t.jsx(aa,{size:20,color:"var(--primary-dark-blue)"}),t.jsx("h3",{className:a.sectionTitle,children:"الدفع والاشتراكات"})]}),t.jsxs("div",{className:a.infoItem,children:[t.jsxs("span",{className:a.infoLabel,children:[t.jsx(I,{size:16})," طريقة الدفع:"]}),t.jsx("span",{className:a.infoValue,children:"لا توجد طرق دفع مسجلة"})]}),t.jsxs("div",{className:a.infoItem,children:[t.jsxs("span",{className:a.infoLabel,children:[t.jsx(I,{size:16})," الاشتراك الحالي:"]}),t.jsx("span",{className:a.infoValue,children:"الخطة المجانية"})]})]})]})})]})};export{pa as default};
