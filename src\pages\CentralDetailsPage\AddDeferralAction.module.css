/* أنماط واجهة إضافة التأجيلات والإجراءات فقط */
@import '../../styles/variables.css';

.addReportForm {
  background: var(--page-background);
  border-radius: 20px;
  padding: 28px;
  box-shadow: 
    0 20px 60px var(--shadow-medium),
    0 10px 30px var(--shadow-light);
  border: none;
  margin: 20px auto;
  width: 100%;
  max-width: 900px;
  transition: all var(--transition-normal);
  position: relative;
  overflow: hidden;
}

.addReportForm:hover {
  box-shadow: 
    0 25px 70px var(--shadow-medium),
    0 15px 40px var(--shadow-light);
  transform: translateY(-4px);
}


.dateReasonSection {
  display: flex;
  flex-wrap: wrap;
  gap: var(--spacing-lg);
  margin: 16px 0;
  align-items: flex-start;
  width: 100%;
  justify-content: space-between;
}

.verticalSection {
  display: flex;
  flex-direction: column;
  gap: var(--spacing-lg);
  flex: 1;
  min-width: 200px;
  max-width: 48%;
}

.verticalSection .dateField {
  flex: none;
  min-width: 100%;
  max-width: 100%;
}

.dateField {
  flex: 1;
  min-width: 200px;
  max-width: 48%;
}

.dateField .descriptionTextarea {
  resize: vertical;
  min-height: 120px;
  line-height: 1.5;
  width: 100%;
  padding: 12px 16px;
  border: 2px solid #e2e8f0;
  border-radius: 12px;
  font-size: 1rem;
  background: #fff;
  transition: border 0.2s;
  box-shadow: 
    0 20px 60px var(--shadow-medium),
    0 10px 30px var(--shadow-light);
}

.dateField .descriptionTextarea:focus {
  outline: none;
  border-color: var(--secondary-color);
  box-shadow: 0 0 0 3px var(--secondary-color);
  transform: none;
}

.dateField .descriptionTextarea::placeholder {
  color: #9ca3af;
  font-style: italic;
}
.dateField label {
  font-weight: 600;
  color: var(--primary-color);
  margin-bottom: var(--spacing-sm);
  display: block;
  font-size: 1.1rem;
  position: relative;
}
.dateInput {
  width: 100%;
  height: 48px;
  background-color: #ffffff;
  border-radius: 12px;
  padding: 0 16px;
  border: 2px solid #ffffff;
  font-size: 1rem;
  color: #1a202c;
  transition: all 0.2s ease;
  outline: none;
  box-shadow: 
    0 20px 60px var(--shadow-medium),
    0 10px 30px var(--shadow-light);
}
.dateInput:focus {
  border-color: var(--secondary-color);
  box-shadow: 0 0 0 3px var(--secondary-color);
  transform: none;
}
.reasonSection {
  flex: 2;
  min-width: 250px;
  max-width: 48%;
}
.reasonSection label {
  font-weight: 600;
  color: #1e40af;
  margin-bottom: 8px;
  display: block;
  font-size: 1.1rem;
  position: relative;
}

.descriptionSection {
  margin: 24px 0;
  width: 100%;
}
.descriptionTextarea {
  width: 100%;
  padding: 12px 16px;
  border: 1px solid #cbd5e1;
  border-radius: 8px;
  font-size: 1rem;
  background: #ffffff;
  color: #1f2937;
  transition: all 0.2s ease;
  font-weight: 500;
  box-shadow: 0 1px 3px rgba(0, 0, 0, 0.05);
  resize: vertical;
  min-height: 80px;
  font-family: inherit;
  line-height: 1.5;
}
.descriptionTextarea:focus {
  outline: none;
  border-color: #3b82f6;
  background: #ffffff;
  box-shadow: 0 0 0 3px rgba(59, 130, 246, 0.1);
  transform: translateY(-1px);
}
.descriptionTextarea::placeholder {
  color: #9ca3af;
  font-style: italic;
}
.selectedReasons {
  padding: 0 16px;
  margin-bottom: 20px;
  border: 2px solid #ffffff;
  border-radius: 12px;
  background: #ffffff;
  min-height: 48px;
  display: flex;
  align-items: center;
  font-size: 1rem;
  color: #1a202c;
  box-shadow: 
    0 20px 60px var(--shadow-medium),
    0 10px 30px var(--shadow-light);
  width: 100%;
  font-weight: 600;
}
.selectedReasons:focus-within {
  border-color: var(--secondary-color);
  box-shadow: 0 0 0 3px var(--secondary-color);
}
.noSelection {
  color: #4a8fa3;
  font-style: italic;
  font-weight: 400;
}
.reasonButtons {
  display: flex;
  flex-wrap: wrap;
  gap: 12px;
  justify-content: center;
  margin-top: 8px;
}
.reasonButton {
  padding: var(--btn-padding-sm);
  border-radius: var(--btn-border-radius);
  cursor: pointer;
  transition: var(--btn-transition);
  font-size: var(--btn-font-size);
  font-weight: var(--btn-font-weight);
  text-align: center;
  width: fit-content;
  white-space: nowrap;
  background-color: var(--neutral-700); /* Dark gray background */
  color: var(--white); /* White text color */
  border: 1px solid var(--neutral-700); /* Dark gray border */
  box-shadow: 
    0 20px 60px var(--shadow-medium),
    0 10px 30px var(--shadow-light);
  position: relative;
  overflow: hidden;
}

.reasonButton:hover {
  transform: var(--transform-hover-up);
  box-shadow: 
    0 25px 70px var(--shadow-medium),
    0 15px 40px var(--shadow-light);
  background-color: var(--neutral-800); /* Even darker gray on hover */
  color: var(--white); /* White text color on hover */
}

.reasonButton.selected {
  background: var(--primary-color);
  color: var(--white);
  border: 1px solid var(--primary-color);
  box-shadow: var(--box-shadow-primary);
}

.reasonButton.selected:hover {
  background: var(--primary-dark);
  box-shadow: var(--box-shadow-lg);
}
.reportFormButtons {
  display: flex;
  gap: 20px;
  justify-content: center;
  margin-top: 30px;
  padding-top: 20px;
  flex-wrap: nowrap;
  align-items: center;
}
.saveButton {
  background: linear-gradient(135deg, var(--primary-medium-blue) 0%, var(--primary-dark-blue) 100%);
  color: var(--white);
  border: none;
  padding: 16px 28px;
  font-weight: 600;
  font-size: 16px;
  display: flex;
  align-items: center;
  gap: 12px;
  box-shadow: 
    0 20px 60px var(--shadow-medium),
    0 10px 30px var(--shadow-light);
  flex: 1;
  justify-content: center;
  min-width: 200px;
  min-height: 56px;
  border-radius: 12px;
  position: relative;
  overflow: hidden;
  transition: all 0.3s ease;
}
.saveButton:hover {
  transform: translateY(-2px);
  box-shadow: 
    0 25px 70px var(--shadow-medium),
    0 15px 40px var(--shadow-light);
}
.addActionForm {
  background: var(--page-background);
  border-radius: 20px;
  padding: 24px;
  box-shadow: 
    0 20px 60px var(--shadow-medium),
    0 10px 30px var(--shadow-light);
  border: none;
  margin: 0 auto;
  width: 100%;
  max-width: 900px;
  transition: all var(--transition-normal);
  position: relative;
  overflow: hidden;
}



.formHeader {
  display: flex;
  align-items: center;
  gap: 12px;
  margin: 0 0 20px 0;
  padding: 0 0 16px 0;
  border-bottom: 1px solid #e2e8f0;
  flex-direction: column;
}

.formTitle {
  font-size: 1.5rem;
  font-weight: 700;
  color: #1e40af;
  margin: 0;
  text-shadow: 0 1px 2px rgba(0, 0, 0, 0.05);
}

.formSubtitle {
  font-size: 0.95rem;
  color: #64748b;
  margin: 0;
  font-weight: 500;
  line-height: 1.5;
}

.errorAlert {
  background: rgba(220, 38, 38, 0.08);
  border-right: 4px solid #dc2626;
  border-radius: 8px;
  padding: 14px 18px;
  margin-bottom: 20px;
  color: #dc2626;
  font-weight: 600;
  font-size: 0.95rem;
  display: flex;
  align-items: center;
  gap: 10px;
  box-shadow: var(--box-shadow-danger);
}

.errorAlert::before {
  content: '⚠️';
  font-size: 18px;
}

.actionField {
  margin-bottom: 16px;
}

.actionField label {
  font-weight: 600;
  color: #1e40af;
  margin-bottom: 8px;
  display: block;
  font-size: 0.95rem;
  position: relative;
}

.actionInput {
  width: 100%;
  height: 48px;
  background-color: #ffffff;
  border-radius: 12px;
  padding: 0 16px;
  border: 2px solid #ffffff;
  font-size: 1rem;
  color: #1a202c;
  transition: all 0.2s ease;
  outline: none;
  box-shadow: 
    0 20px 60px var(--shadow-medium),
    0 10px 30px var(--shadow-light);
}

.actionInput:focus {
  outline: none;
  border-color: #3b82f6;
  background: #ffffff;
  box-shadow: 0 0 0 2px rgba(59, 130, 246, 0.1);
}

.actionInput:disabled {
  background: rgba(1, 72, 113, 0.05);
  color: #4a8fa3;
  cursor: not-allowed;
  border-color: rgba(1, 72, 113, 0.2);
}

.actionFormButtons {
  display: flex;
  gap: 12px;
  justify-content: center;
  margin-top: 20px;
  padding-top: 16px;
  flex-wrap: nowrap;
  align-items: center;
}

.addActionButton {
  padding: var(--padding-button);
  background: linear-gradient(135deg, var(--primary-medium-blue) 0%, var(--primary-dark-blue) 100%); /* Same as saveButton */
  color: white;
  border: none;
  border-radius: 8px;
  font-weight: 600;
  cursor: pointer;
  transition: var(--transition-all);
  font-size: 0.95rem;
  display: flex;
  align-items: center;
  gap: 10px;
  box-shadow: 
    0 20px 60px var(--shadow-medium),
    0 10px 30px var(--shadow-light);
  flex: 1;
  justify-content: center;
  min-width: 200px; /* Ensure consistent width */
  min-height: 56px; /* Ensure consistent height */
  position: relative;
  overflow: hidden;
}

.addActionButton:hover {
  background: var(--primary-dark-blue); /* Darker on hover, similar to saveButton */
  transform: translateY(-2px);
  box-shadow: 
    0 25px 70px var(--shadow-medium),
    0 15px 40px var(--shadow-light);
}

.addActionButton:disabled {
  background: rgba(1, 72, 113, 0.3);
  cursor: not-allowed;
  transform: none;
  box-shadow: none;
}

.cancelButton {
  padding: var(--btn-padding);
  background-color: var(--neutral-300); /* Changed to a more visible gray */
  color: var(--neutral-800); /* Changed to a darker text color */
  border: none; /* Removed border */
  border-radius: var(--btn-border-radius);
  font-weight: var(--btn-font-weight);
  cursor: pointer;
  transition: var(--btn-transition);
  font-size: var(--btn-font-size);
  display: flex;
  align-items: center;
  justify-content: center;
  gap: var(--btn-gap);
  flex: 1;
  min-width: 200px;
  min-height: 56px; /* Changed to match saveButton */
  box-shadow: 
    0 20px 60px var(--shadow-medium),
    0 10px 30px var(--shadow-light);
}

.cancelButton:hover {
  background-color: var(--neutral-400); /* Darker on hover */
  transform: var(--transform-hover-up);
  box-shadow: 
    0 25px 70px var(--shadow-medium),
    0 15px 40px var(--shadow-light);
}
.buttonIcon {
  margin-left: 8px;
  font-size: 18px;
  vertical-align: middle;
}

@media (max-width: 768px) {
  .addReportForm,
  .addActionForm {
    padding: 24px;
    max-width: 100%;
    width: 100%;
    margin: 20px 0;
    box-sizing: border-box;
  }
  .dateReasonSection {
    gap: 16px;
    flex-direction: column;
  }
  .dateField,
  .reasonSection,
  .verticalSection {
    min-width: 100%;
    max-width: 100%;
    width: 100%;
  }
  .actionField {
    margin-bottom: 20px;
  }
  .reportFormButtons,
  .actionFormButtons {
    gap: 16px;
    flex-wrap: nowrap;
  }
  .addActionButton,
  .cancelButton,
  .saveButton {
    flex: 1;
    min-width: 0;
    max-width: none;
    padding: 14px 16px;
    font-size: 15px;
    height: 52px;
    min-height: 52px;
    font-weight: 600;
    display: flex;
    align-items: center;
    justify-content: center;
    gap: 10px;
      box-shadow: 
    0 25px 70px var(--shadow-medium),
    0 15px 40px var(--shadow-light); /* نفس shadow الكروت عند hover */
  }
}

@media (max-width: 480px) {
  .addReportForm,
  .addActionForm {
    padding: 16px;
    border-radius: 12px;
    width: 100%;
    max-width: 100%;
    margin: 16px 0;
    box-sizing: border-box;
  }
  
  .formHeader {
    margin-bottom: 12px;
    padding-bottom: 12px;
  }
  
  .formTitle {
    font-size: 1.3rem;
  }
  
  .formSubtitle {
    font-size: 0.85rem;
    text-align: center;
  }
  
  .dateInput,
  .descriptionTextarea,
  .selectedReasons {
    padding: 12px;
    font-size: 15px;
    border-width: 2px;
  }
  
  .actionInput {
    padding: 8px 10px;
    font-size: 0.9rem;
  }
  
  .reasonButtons {
    gap: 8px;
  }
  
  .reasonButton {
    padding: 8px 12px;
    font-size: 14px;
    border-width: 2px;
  }
  
  .reportFormButtons,
  .actionFormButtons {
    flex-direction: column;
    gap: 12px;
  }
  
  .addActionButton,
  .cancelButton,
  .saveButton {
    width: 100%;
    min-height: 48px;
    font-size: 14px;
  }


  .dateInput,
  .actionInput {
    padding: 12px;
    font-size: 14px;
  }
  .selectedReasons {
    padding: 12px;
    font-size: 14px;
    min-height: 48px;
  }
  .reasonButton {
    padding: 8px 12px;
    font-size: 13px;
    border-width: 2px;
  }
  .reportFormButtons,
  .actionFormButtons {
    gap: 12px;
    flex-wrap: nowrap;
  }
  .addActionButton,
  .cancelButton,
  .saveButton {
    flex: 1;
    min-width: 0;
    max-width: none;
    padding: 12px 8px;
    font-size: 14px;
    height: 48px;
    min-height: 48px;
    font-weight: 600;
    display: flex;
    align-items: center;
    justify-content: center;
    gap: 8px;
  }
}

/* أسلوب العلامات لتوقيت الإشعار */
.reminderTags {
  display: flex;
  gap: 12px;
  flex-wrap: wrap;
  margin-top: 8px;
}

.reminderTag {
  display: flex;
  align-items: center;
  gap: 8px;
  padding: 12px 16px;
  border: 2px solid #e2e8f0;
  border-radius: 12px;
  background: #ffffff;
  cursor: pointer;
  transition: all 0.3s ease;
  min-width: 140px;
  justify-content: center;
  font-weight: 500;
  font-size: 0.95rem;
  color: #64748b;
  box-shadow: 0 2px 4px rgba(0, 0, 0, 0.05);
  user-select: none;
  position: relative;
  overflow: hidden;
}

.reminderTag::before {
  content: '';
  position: absolute;
  top: 0;
  left: -100%;
  width: 100%;
  height: 100%;
  background: linear-gradient(90deg, transparent, rgba(59, 130, 246, 0.1), transparent);
  transition: left 0.6s ease;
}

.reminderTag:hover::before {
  left: 100%;
}

.reminderTag:hover {
  border-color: #3b82f6;
  color: #3b82f6;
  transform: translateY(-2px);
  box-shadow: 0 4px 12px rgba(59, 130, 246, 0.15);
}

.reminderTag.selectedTag {
  border-color: #3b82f6;
  background: rgba(59, 130, 246, 0.08);
  color: #1e40af;
  box-shadow: 0 4px 12px rgba(59, 130, 246, 0.2);
}

.reminderTag.selectedTag:hover {
  background: rgba(59, 130, 246, 0.12);
  transform: translateY(-1px);
}

.tagIcon {
  font-size: 1.1rem;
  flex-shrink: 0;
}

.tagText {
  font-weight: 600;
  white-space: nowrap;
}

.reminderTag.selectedTag .tagText {
  color: #1e40af;
  font-weight: 700;
}

.reminderTag.selectedTag .tagIcon {
  animation: pulse 2s infinite;
}

@keyframes pulse {
  0% { transform: scale(1); }
  50% { transform: scale(1.1); }
  100% { transform: scale(1); }
}

/* تحسينات للشاشات الصغيرة */
@media (max-width: 768px) {
  .reminderTags {
    flex-direction: column;
    gap: 10px;
  }
  
  .reminderTag {
    min-width: 100%;
    padding: 14px 16px;
    font-size: 1rem;
  }
  
  .tagIcon {
    font-size: 1.2rem;
  }
  
  .tagText {
    font-size: 1rem;
  }
}

@media (max-width: 480px) {
  .reminderTag {
    padding: 12px 14px;
    min-height: 50px;
    font-size: 0.95rem;
  }
  
  .tagIcon {
    font-size: 1.1rem;
  }
  
  .tagText {
    font-size: 0.95rem;
  }
}

/* SweetAlert2 Custom Styles */
.swal2-popup {
  background: var(--page-background) !important;
  border-radius: 20px !important;
  box-shadow: 
    0 20px 60px var(--shadow-medium),
    0 10px 30px var(--shadow-light) !important;
  border: none !important;
  direction: rtl !important;
  text-align: right !important;
  padding: 2rem !important; /* Increased padding */
  max-width: 550px !important; /* Increased max-width */
}

.swal2-title {
  color: var(--primary-color) !important;
  font-size: 1.5rem !important;
  font-weight: 700 !important;
  margin-bottom: 1.5rem !important; /* Increased margin-bottom */
}

.swal2-html-container {
  color: var(--neutral-700) !important;
  font-size: 1.1rem !important;
  line-height: 1.5 !important;
  margin-bottom: 1.5rem !important; /* Added margin-bottom */
}

.swal2-input {
  width: calc(100% - 24px) !important; /* Adjusted width for padding */
  padding: 12px !important; /* Increased padding */
  border: 1px solid var(--neutral-400) !important;
  border-radius: 6px !important;
  font-size: 1rem !important; /* Adjusted font-size */
  background: var(--page-background) !important;
  color: var(--neutral-800) !important;
  transition: all 0.2s ease !important;
  font-weight: 500 !important;
  box-shadow: 0 1px 3px rgba(0, 0, 0, 0.05) !important;
  direction: rtl !important;
  text-align: right !important;
  margin: 0 auto 1.5rem auto !important; /* Centered and added margin-bottom */
  display: block !important; /* Ensure it takes full width */
}

.swal2-input:focus {
  outline: none !important;
  border-color: var(--primary-color) !important;
  box-shadow: 0 0 0 2px rgba(59, 130, 246, 0.1) !important;
}

.swal2-confirm,
.swal2-cancel {
  padding: 12px 24px !important;
  border-radius: 12px !important;
  font-size: 1rem !important;
  font-weight: 600 !important;
  cursor: pointer !important;
  border: none !important;
  transition: all 0.2s ease !important;
  min-width: 100px !important; /* Increased min-width */
  margin: 0 8px !important; /* Adjusted margin */
}

.swal2-confirm {
  background: var(--primary-color) !important;
  color: white !important;
}

.swal2-confirm:hover {
  background: var(--primary-dark) !important;
  transform: translateY(-1px) !important;
}

.swal2-cancel {
  background: var(--neutral-200) !important;
  color: var(--neutral-700) !important;
}

.swal2-cancel:hover {
  background: var(--neutral-300) !important;
  transform: translateY(-1px) !important;
}

.swal2-actions {
  margin-top: 25px !important; /* Increased margin-top */
  display: flex !important;
  justify-content: center !important;
  gap: 15px !important; /* Added gap between buttons */
  flex-wrap: nowrap !important; /* Prevent wrapping */
}

/* Media Queries for SweetAlert2 */
@media (max-width: 600px) {
  .swal2-popup {
    padding: 1.5rem !important;
    max-width: 90% !important;
  }

  .swal2-title {
    font-size: 1.3rem !important;
    margin-bottom: 1rem !important;
  }

  .swal2-html-container {
    font-size: 1rem !important;
    margin-bottom: 1rem !important;
  }

  .swal2-input {
    padding: 10px !important;
    font-size: 0.95rem !important;
    margin-bottom: 1rem !important;
  }

  .swal2-confirm,
  .swal2-cancel {
    padding: 10px 20px !important;
    font-size: 0.9rem !important;
    min-width: unset !important; /* Allow buttons to shrink */
    flex: 1; /* Distribute space evenly */
  }

  .swal2-actions {
    flex-direction: row !important; /* Keep them in a row if possible */
    flex-wrap: wrap !important; /* Allow wrapping if needed */
    gap: 10px !important;
    margin-top: 20px !important;
  }
}

@media (max-width: 400px) {
  .swal2-actions {
    flex-direction: column !important; /* Stack buttons vertically on very small screens */
  }
  .swal2-confirm,
  .swal2-cancel {
    width: 100% !important; /* Full width when stacked */
    margin: 0 !important; /* Remove horizontal margin */
  }
  .swal2-actions > .swal2-confirm:not([hidden]) + .swal2-cancel:not([hidden]) {
    margin-top: 10px !important; /* Add vertical margin between stacked buttons */
  }
}
