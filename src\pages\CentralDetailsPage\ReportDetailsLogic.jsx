const generateUniqueId = (prefix, caseId) => `${caseId}-${prefix}-${Date.now()}`;

export const addDeferralLogic = (data, caseData, currentUser) => {
  const { reportDate, selectedReasons, deferralDescription } = data;
  if (!reportDate || selectedReasons.length === 0) {
    throw new Error('التاريخ وسبب التأجيل حقول مطلوبة.');
  }

  const newDeferral = {
    id: generateUniqueId('deferral', caseData.id),
    date: reportDate,
    reasons: selectedReasons,
    description: deferralDescription || '',
    createdAt: new Date().toISOString(),
    userId: currentUser.uid,
  };

  const updatedDeferrals = [newDeferral, ...(caseData.deferrals || [])];
  return { deferrals: updatedDeferrals };
};

export const addActionLogic = (data, caseData, currentUser) => {
  const { description, deadline, reminderType } = data;
  if (!description || !deadline || !reminderType) {
    throw new Error('يرجى ملء جميع الحقول المطلوبة للإجراء.');
  }

  const newAction = {
    id: generateUniqueId('action', caseData.id),
    description,
    deadline,
    reminderType,
    createdAt: new Date().toISOString(),
    userId: currentUser.uid,
  };

  const updatedActions = [newAction, ...(caseData.actions || [])];
  return { actions: updatedActions };
};

export const completeTaskLogic = (item, caseData, currentUser) => {
  let updatedDeferrals = [...(caseData.deferrals || [])];
  let updatedActions = [...(caseData.actions || [])];
  let historyEvent;

  if (item.type === 'deferral') {
    const deferralToComplete = item.data;
    const isJudgment = deferralToComplete.reasons.some(r => r.includes('للحكم'));
    if (isJudgment) {
      return { type: 'judgment', data: deferralToComplete };
    }
    
    updatedDeferrals = updatedDeferrals.filter(d => d.id !== deferralToComplete.id);
    historyEvent = {
      type: 'completed_deferral',
      description: `تم حضور جلسة: ${deferralToComplete.reasons.join(', ')}`,
      originalDate: deferralToComplete.date,
    };

  } else if (item.type === 'action') {
    const actionToComplete = item.data;
    updatedActions = updatedActions.filter(a => a.id !== actionToComplete.id);
    historyEvent = {
      type: 'completed_action',
      description: `تم إنجاز إجراء: ${actionToComplete.description}`,
      originalDeadline: actionToComplete.deadline,
    };
  }

  const commonHistoryData = {
    id: generateUniqueId('history', caseData.id),
    completedAt: new Date().toISOString(),
    userId: currentUser.uid,
  };

  const updatedHistory = [{ ...historyEvent, ...commonHistoryData }, ...(caseData.history || [])];
  
  return {
    type: 'completed',
    payload: {
      deferrals: updatedDeferrals,
      actions: updatedActions,
      history: updatedHistory,
    }
  };
};

export const deleteTaskLogic = (item, caseData) => {
  if (item.type === 'deferral') {
    const updatedDeferrals = (caseData.deferrals || []).filter(d => d.id !== item.data.id);
    return { deferrals: updatedDeferrals };
  } else if (item.type === 'action') {
    const updatedActions = (caseData.actions || []).filter(a => a.id !== item.data.id);
    return { actions: updatedActions };
  }
  return {};
};

export const saveJudgmentLogic = (verdictData, judgmentDeferral, caseData, currentUser) => {
    const { verdict, verdictDetails } = verdictData;
    if (!verdict || !verdictDetails) {
        throw new Error("بيانات الحكم غير مكتملة.");
    }

    const verdictEvent = {
        id: generateUniqueId('verdict', caseData.id),
        type: 'judgment_verdict',
        timestamp: new Date().toISOString(),
        verdict: verdict,
        description: verdictDetails,
        originalDate: judgmentDeferral.date,
        userId: currentUser.uid,
    };

    const updatedDeferrals = (caseData.deferrals || []).filter(d => d.id !== judgmentDeferral.id);
    const updatedHistory = [verdictEvent, ...(caseData.history || [])];

    return {
        deferrals: updatedDeferrals,
        history: updatedHistory,
    };
};
