/*!
* sweetalert2 v11.21.2
* Released under the MIT License.
*/function Ue(e,t,o){if(typeof e=="function"?e===t:e.has(t))return arguments.length<3?t:o;throw new TypeError("Private element is not present on this object")}function Ft(e,t){if(t.has(e))throw new TypeError("Cannot initialize the same private elements twice on an object")}function Me(e,t){return e.get(Ue(e,t))}function Vt(e,t,o){Ft(e,t),t.set(e,o)}function qt(e,t,o){return e.set(Ue(e,t),o),o}const _t=100,a={},Rt=()=>{a.previousActiveElement instanceof HTMLElement?(a.previousActiveElement.focus(),a.previousActiveElement=null):document.body&&document.body.focus()},Wt=e=>new Promise(t=>{if(!e)return t();const o=window.scrollX,n=window.scrollY;a.restoreFocusTimeout=setTimeout(()=>{Rt(),t()},_t),window.scrollTo(o,n)}),Ne="swal2-",Ut=["container","shown","height-auto","iosfix","popup","modal","no-backdrop","no-transition","toast","toast-shown","show","hide","close","title","html-container","actions","confirm","deny","cancel","footer","icon","icon-content","image","input","file","range","select","radio","checkbox","label","textarea","inputerror","input-label","validation-message","progress-steps","active-progress-step","progress-step","progress-step-line","loader","loading","styled","top","top-start","top-end","top-left","top-right","center","center-start","center-end","center-left","center-right","bottom","bottom-start","bottom-end","bottom-left","bottom-right","grow-row","grow-column","grow-fullscreen","rtl","timer-progress-bar","timer-progress-bar-container","scrollbar-measure","icon-success","icon-warning","icon-info","icon-question","icon-error","draggable","dragging"],i=Ut.reduce((e,t)=>(e[t]=Ne+t,e),{}),Nt=["success","warning","info","question","error"],J=Nt.reduce((e,t)=>(e[t]=Ne+t,e),{}),Ye="SweetAlert2:",ye=e=>e.charAt(0).toUpperCase()+e.slice(1),b=e=>{console.warn(`${Ye} ${typeof e=="object"?e.join(" "):e}`)},O=e=>{console.error(`${Ye} ${e}`)},He=[],Yt=e=>{He.includes(e)||(He.push(e),b(e))},Ke=(e,t=null)=>{Yt(`"${e}" is deprecated and will be removed in the next major release.${t?` Use "${t}" instead.`:""}`)},re=e=>typeof e=="function"?e():e,ve=e=>e&&typeof e.toPromise=="function",W=e=>ve(e)?e.toPromise():Promise.resolve(e),ke=e=>e&&Promise.resolve(e)===e,y=()=>document.body.querySelector(`.${i.container}`),U=e=>{const t=y();return t?t.querySelector(e):null},x=e=>U(`.${e}`),c=()=>x(i.popup),V=()=>x(i.icon),Kt=()=>x(i["icon-content"]),Xe=()=>x(i.title),xe=()=>x(i["html-container"]),Ze=()=>x(i.image),Ce=()=>x(i["progress-steps"]),ae=()=>x(i["validation-message"]),P=()=>U(`.${i.actions} .${i.confirm}`),q=()=>U(`.${i.actions} .${i.cancel}`),M=()=>U(`.${i.actions} .${i.deny}`),Xt=()=>x(i["input-label"]),_=()=>U(`.${i.loader}`),N=()=>x(i.actions),Je=()=>x(i.footer),le=()=>x(i["timer-progress-bar"]),Ee=()=>x(i.close),Zt=`
  a[href],
  area[href],
  input:not([disabled]),
  select:not([disabled]),
  textarea:not([disabled]),
  button:not([disabled]),
  iframe,
  object,
  embed,
  [tabindex="0"],
  [contenteditable],
  audio[controls],
  video[controls],
  summary
`,Ae=()=>{const e=c();if(!e)return[];const t=e.querySelectorAll('[tabindex]:not([tabindex="-1"]):not([tabindex="0"])'),o=Array.from(t).sort((r,l)=>{const u=parseInt(r.getAttribute("tabindex")||"0"),f=parseInt(l.getAttribute("tabindex")||"0");return u>f?1:u<f?-1:0}),n=e.querySelectorAll(Zt),s=Array.from(n).filter(r=>r.getAttribute("tabindex")!=="-1");return[...new Set(o.concat(s))].filter(r=>v(r))},Pe=()=>B(document.body,i.shown)&&!B(document.body,i["toast-shown"])&&!B(document.body,i["no-backdrop"]),ce=()=>{const e=c();return e?B(e,i.toast):!1},Jt=()=>{const e=c();return e?e.hasAttribute("data-loading"):!1},C=(e,t)=>{if(e.textContent="",t){const n=new DOMParser().parseFromString(t,"text/html"),s=n.querySelector("head");s&&Array.from(s.childNodes).forEach(l=>{e.appendChild(l)});const r=n.querySelector("body");r&&Array.from(r.childNodes).forEach(l=>{l instanceof HTMLVideoElement||l instanceof HTMLAudioElement?e.appendChild(l.cloneNode(!0)):e.appendChild(l)})}},B=(e,t)=>{if(!t)return!1;const o=t.split(/\s+/);for(let n=0;n<o.length;n++)if(!e.classList.contains(o[n]))return!1;return!0},Gt=(e,t)=>{Array.from(e.classList).forEach(o=>{!Object.values(i).includes(o)&&!Object.values(J).includes(o)&&!Object.values(t.showClass||{}).includes(o)&&e.classList.remove(o)})},k=(e,t,o)=>{if(Gt(e,t),!t.customClass)return;const n=t.customClass[o];if(n){if(typeof n!="string"&&!n.forEach){b(`Invalid type of customClass.${o}! Expected string or iterable object, got "${typeof n}"`);return}d(e,n)}},de=(e,t)=>{if(!t)return null;switch(t){case"select":case"textarea":case"file":return e.querySelector(`.${i.popup} > .${i[t]}`);case"checkbox":return e.querySelector(`.${i.popup} > .${i.checkbox} input`);case"radio":return e.querySelector(`.${i.popup} > .${i.radio} input:checked`)||e.querySelector(`.${i.popup} > .${i.radio} input:first-child`);case"range":return e.querySelector(`.${i.popup} > .${i.range} input`);default:return e.querySelector(`.${i.popup} > .${i.input}`)}},Ge=e=>{if(e.focus(),e.type!=="file"){const t=e.value;e.value="",e.value=t}},Qe=(e,t,o)=>{!e||!t||(typeof t=="string"&&(t=t.split(/\s+/).filter(Boolean)),t.forEach(n=>{Array.isArray(e)?e.forEach(s=>{o?s.classList.add(n):s.classList.remove(n)}):o?e.classList.add(n):e.classList.remove(n)}))},d=(e,t)=>{Qe(e,t,!0)},E=(e,t)=>{Qe(e,t,!1)},$=(e,t)=>{const o=Array.from(e.children);for(let n=0;n<o.length;n++){const s=o[n];if(s instanceof HTMLElement&&B(s,t))return s}},S=(e,t,o)=>{o===`${parseInt(o)}`&&(o=parseInt(o)),o||parseInt(o)===0?e.style.setProperty(t,typeof o=="number"?`${o}px`:o):e.style.removeProperty(t)},p=(e,t="flex")=>{e&&(e.style.display=t)},g=e=>{e&&(e.style.display="none")},Be=(e,t="block")=>{e&&new MutationObserver(()=>{Y(e,e.innerHTML,t)}).observe(e,{childList:!0,subtree:!0})},je=(e,t,o,n)=>{const s=e.querySelector(t);s&&s.style.setProperty(o,n)},Y=(e,t,o="flex")=>{t?p(e,o):g(e)},v=e=>!!(e&&(e.offsetWidth||e.offsetHeight||e.getClientRects().length)),Qt=()=>!v(P())&&!v(M())&&!v(q()),me=e=>e.scrollHeight>e.clientHeight,eo=(e,t)=>{let o=e;for(;o&&o!==t;){if(me(o))return!0;o=o.parentElement}return!1},et=e=>{const t=window.getComputedStyle(e),o=parseFloat(t.getPropertyValue("animation-duration")||"0"),n=parseFloat(t.getPropertyValue("transition-duration")||"0");return o>0||n>0},$e=(e,t=!1)=>{const o=le();o&&v(o)&&(t&&(o.style.transition="none",o.style.width="100%"),setTimeout(()=>{o.style.transition=`width ${e/1e3}s linear`,o.style.width="0%"},10))},to=()=>{const e=le();if(!e)return;const t=parseInt(window.getComputedStyle(e).width);e.style.removeProperty("transition"),e.style.width="100%";const o=parseInt(window.getComputedStyle(e).width),n=t/o*100;e.style.width=`${n}%`},oo=()=>typeof window>"u"||typeof document>"u",no=`
 <div aria-labelledby="${i.title}" aria-describedby="${i["html-container"]}" class="${i.popup}" tabindex="-1">
   <button type="button" class="${i.close}"></button>
   <ul class="${i["progress-steps"]}"></ul>
   <div class="${i.icon}"></div>
   <img class="${i.image}" />
   <h2 class="${i.title}" id="${i.title}"></h2>
   <div class="${i["html-container"]}" id="${i["html-container"]}"></div>
   <input class="${i.input}" id="${i.input}" />
   <input type="file" class="${i.file}" />
   <div class="${i.range}">
     <input type="range" />
     <output></output>
   </div>
   <select class="${i.select}" id="${i.select}"></select>
   <div class="${i.radio}"></div>
   <label class="${i.checkbox}">
     <input type="checkbox" id="${i.checkbox}" />
     <span class="${i.label}"></span>
   </label>
   <textarea class="${i.textarea}" id="${i.textarea}"></textarea>
   <div class="${i["validation-message"]}" id="${i["validation-message"]}"></div>
   <div class="${i.actions}">
     <div class="${i.loader}"></div>
     <button type="button" class="${i.confirm}"></button>
     <button type="button" class="${i.deny}"></button>
     <button type="button" class="${i.cancel}"></button>
   </div>
   <div class="${i.footer}"></div>
   <div class="${i["timer-progress-bar-container"]}">
     <div class="${i["timer-progress-bar"]}"></div>
   </div>
 </div>
`.replace(/(^|\n)\s*/g,""),so=()=>{const e=y();return e?(e.remove(),E([document.documentElement,document.body],[i["no-backdrop"],i["toast-shown"],i["has-column"]]),!0):!1},T=()=>{a.currentInstance.resetValidationMessage()},io=()=>{const e=c(),t=$(e,i.input),o=$(e,i.file),n=e.querySelector(`.${i.range} input`),s=e.querySelector(`.${i.range} output`),r=$(e,i.select),l=e.querySelector(`.${i.checkbox} input`),u=$(e,i.textarea);t.oninput=T,o.onchange=T,r.onchange=T,l.onchange=T,u.oninput=T,n.oninput=()=>{T(),s.value=n.value},n.onchange=()=>{T(),s.value=n.value}},ro=e=>typeof e=="string"?document.querySelector(e):e,ao=e=>{const t=c();t.setAttribute("role",e.toast?"alert":"dialog"),t.setAttribute("aria-live",e.toast?"polite":"assertive"),e.toast||t.setAttribute("aria-modal","true")},lo=e=>{window.getComputedStyle(e).direction==="rtl"&&d(y(),i.rtl)},co=e=>{const t=so();if(oo()){O("SweetAlert2 requires document to initialize");return}const o=document.createElement("div");o.className=i.container,t&&d(o,i["no-transition"]),C(o,no),o.dataset.swal2Theme=e.theme;const n=ro(e.target);n.appendChild(o),e.topLayer&&(o.setAttribute("popover",""),o.showPopover()),ao(e),lo(n),io()},Le=(e,t)=>{e instanceof HTMLElement?t.appendChild(e):typeof e=="object"?uo(e,t):e&&C(t,e)},uo=(e,t)=>{e.jquery?wo(t,e):C(t,e.toString())},wo=(e,t)=>{if(e.textContent="",0 in t)for(let o=0;o in t;o++)e.appendChild(t[o].cloneNode(!0));else e.appendChild(t.cloneNode(!0))},fo=(e,t)=>{const o=N(),n=_();!o||!n||(!t.showConfirmButton&&!t.showDenyButton&&!t.showCancelButton?g(o):p(o),k(o,t,"actions"),ho(o,n,t),C(n,t.loaderHtml||""),k(n,t,"loader"))};function ho(e,t,o){const n=P(),s=M(),r=q();!n||!s||!r||(fe(n,"confirm",o),fe(s,"deny",o),fe(r,"cancel",o),mo(n,s,r,o),o.reverseButtons&&(o.toast?(e.insertBefore(r,n),e.insertBefore(s,n)):(e.insertBefore(r,t),e.insertBefore(s,t),e.insertBefore(n,t))))}function mo(e,t,o,n){if(!n.buttonsStyling){E([e,t,o],i.styled);return}d([e,t,o],i.styled),n.confirmButtonColor&&e.style.setProperty("--swal2-confirm-button-background-color",n.confirmButtonColor),n.denyButtonColor&&t.style.setProperty("--swal2-deny-button-background-color",n.denyButtonColor),n.cancelButtonColor&&o.style.setProperty("--swal2-cancel-button-background-color",n.cancelButtonColor),we(e),we(t),we(o)}function we(e){const t=window.getComputedStyle(e),o=t.backgroundColor.replace(/rgba?\((\d+), (\d+), (\d+).*/,"rgba($1, $2, $3, 0.5)");e.style.setProperty("--swal2-action-button-outline",t.getPropertyValue("--swal2-outline").replace(/ rgba\(.*/,` ${o}`))}function fe(e,t,o){const n=ye(t);Y(e,o[`show${n}Button`],"inline-block"),C(e,o[`${t}ButtonText`]||""),e.setAttribute("aria-label",o[`${t}ButtonAriaLabel`]||""),e.className=i[t],k(e,o,`${t}Button`)}const po=(e,t)=>{const o=Ee();o&&(C(o,t.closeButtonHtml||""),k(o,t,"closeButton"),Y(o,t.showCloseButton),o.setAttribute("aria-label",t.closeButtonAriaLabel||""))},go=(e,t)=>{const o=y();o&&(bo(o,t.backdrop),yo(o,t.position),vo(o,t.grow),k(o,t,"container"))};function bo(e,t){typeof t=="string"?e.style.background=t:t||d([document.documentElement,document.body],i["no-backdrop"])}function yo(e,t){t&&(t in i?d(e,i[t]):(b('The "position" parameter is not valid, defaulting to "center"'),d(e,i.center)))}function vo(e,t){t&&d(e,i[`grow-${t}`])}var w={innerParams:new WeakMap,domCache:new WeakMap};const ko=["input","file","range","select","radio","checkbox","textarea"],xo=(e,t)=>{const o=c();if(!o)return;const n=w.innerParams.get(e),s=!n||t.input!==n.input;ko.forEach(r=>{const l=$(o,i[r]);l&&(Ao(r,t.inputAttributes),l.className=i[r],s&&g(l))}),t.input&&(s&&Co(t),Po(t))},Co=e=>{if(!e.input)return;if(!h[e.input]){O(`Unexpected type of input! Expected ${Object.keys(h).join(" | ")}, got "${e.input}"`);return}const t=tt(e.input);if(!t)return;const o=h[e.input](t,e);p(t),e.inputAutoFocus&&setTimeout(()=>{Ge(o)})},Eo=e=>{for(let t=0;t<e.attributes.length;t++){const o=e.attributes[t].name;["id","type","value","style"].includes(o)||e.removeAttribute(o)}},Ao=(e,t)=>{const o=c();if(!o)return;const n=de(o,e);if(n){Eo(n);for(const s in t)n.setAttribute(s,t[s])}},Po=e=>{if(!e.input)return;const t=tt(e.input);t&&k(t,e,"input")},Te=(e,t)=>{!e.placeholder&&t.inputPlaceholder&&(e.placeholder=t.inputPlaceholder)},K=(e,t,o)=>{if(o.inputLabel){const n=document.createElement("label"),s=i["input-label"];n.setAttribute("for",e.id),n.className=s,typeof o.customClass=="object"&&d(n,o.customClass.inputLabel),n.innerText=o.inputLabel,t.insertAdjacentElement("beforebegin",n)}},tt=e=>{const t=c();if(t)return $(t,i[e]||i.input)},G=(e,t)=>{["string","number"].includes(typeof t)?e.value=`${t}`:ke(t)||b(`Unexpected type of inputValue! Expected "string", "number" or "Promise", got "${typeof t}"`)},h={};h.text=h.email=h.password=h.number=h.tel=h.url=h.search=h.date=h["datetime-local"]=h.time=h.week=h.month=(e,t)=>(G(e,t.inputValue),K(e,e,t),Te(e,t),e.type=t.input,e);h.file=(e,t)=>(K(e,e,t),Te(e,t),e);h.range=(e,t)=>{const o=e.querySelector("input"),n=e.querySelector("output");return G(o,t.inputValue),o.type=t.input,G(n,t.inputValue),K(o,e,t),e};h.select=(e,t)=>{if(e.textContent="",t.inputPlaceholder){const o=document.createElement("option");C(o,t.inputPlaceholder),o.value="",o.disabled=!0,o.selected=!0,e.appendChild(o)}return K(e,e,t),e};h.radio=e=>(e.textContent="",e);h.checkbox=(e,t)=>{const o=de(c(),"checkbox");o.value="1",o.checked=!!t.inputValue;const n=e.querySelector("span");return C(n,t.inputPlaceholder||t.inputLabel),o};h.textarea=(e,t)=>{G(e,t.inputValue),Te(e,t),K(e,e,t);const o=n=>parseInt(window.getComputedStyle(n).marginLeft)+parseInt(window.getComputedStyle(n).marginRight);return setTimeout(()=>{if("MutationObserver"in window){const n=parseInt(window.getComputedStyle(c()).width),s=()=>{if(!document.body.contains(e))return;const r=e.offsetWidth+o(e);r>n?c().style.width=`${r}px`:S(c(),"width",t.width)};new MutationObserver(s).observe(e,{attributes:!0,attributeFilter:["style"]})}}),e};const Bo=(e,t)=>{const o=xe();o&&(Be(o),k(o,t,"htmlContainer"),t.html?(Le(t.html,o),p(o,"block")):t.text?(o.textContent=t.text,p(o,"block")):g(o),xo(e,t))},$o=(e,t)=>{const o=Je();o&&(Be(o),Y(o,t.footer,"block"),t.footer&&Le(t.footer,o),k(o,t,"footer"))},Lo=(e,t)=>{const o=w.innerParams.get(e),n=V();if(!n)return;if(o&&t.icon===o.icon){ze(n,t),De(n,t);return}if(!t.icon&&!t.iconHtml){g(n);return}if(t.icon&&Object.keys(J).indexOf(t.icon)===-1){O(`Unknown icon! Expected "success", "error", "warning", "info" or "question", got "${t.icon}"`),g(n);return}p(n),ze(n,t),De(n,t),d(n,t.showClass&&t.showClass.icon),window.matchMedia("(prefers-color-scheme: dark)").addEventListener("change",ot)},De=(e,t)=>{for(const[o,n]of Object.entries(J))t.icon!==o&&E(e,n);d(e,t.icon&&J[t.icon]),Io(e,t),ot(),k(e,t,"icon")},ot=()=>{const e=c();if(!e)return;const t=window.getComputedStyle(e).getPropertyValue("background-color"),o=e.querySelectorAll("[class^=swal2-success-circular-line], .swal2-success-fix");for(let n=0;n<o.length;n++)o[n].style.backgroundColor=t},To=`
  <div class="swal2-success-circular-line-left"></div>
  <span class="swal2-success-line-tip"></span> <span class="swal2-success-line-long"></span>
  <div class="swal2-success-ring"></div> <div class="swal2-success-fix"></div>
  <div class="swal2-success-circular-line-right"></div>
`,So=`
  <span class="swal2-x-mark">
    <span class="swal2-x-mark-line-left"></span>
    <span class="swal2-x-mark-line-right"></span>
  </span>
`,ze=(e,t)=>{if(!t.icon&&!t.iconHtml)return;let o=e.innerHTML,n="";t.iconHtml?n=Fe(t.iconHtml):t.icon==="success"?(n=To,o=o.replace(/ style=".*?"/g,"")):t.icon==="error"?n=So:t.icon&&(n=Fe({question:"?",warning:"!",info:"i"}[t.icon])),o.trim()!==n.trim()&&C(e,n)},Io=(e,t)=>{if(t.iconColor){e.style.color=t.iconColor,e.style.borderColor=t.iconColor;for(const o of[".swal2-success-line-tip",".swal2-success-line-long",".swal2-x-mark-line-left",".swal2-x-mark-line-right"])je(e,o,"background-color",t.iconColor);je(e,".swal2-success-ring","border-color",t.iconColor)}},Fe=e=>`<div class="${i["icon-content"]}">${e}</div>`,Oo=(e,t)=>{const o=Ze();if(o){if(!t.imageUrl){g(o);return}p(o,""),o.setAttribute("src",t.imageUrl),o.setAttribute("alt",t.imageAlt||""),S(o,"width",t.imageWidth),S(o,"height",t.imageHeight),o.className=i.image,k(o,t,"image")}};let Se=!1,nt=0,st=0,it=0,rt=0;const Mo=e=>{e.addEventListener("mousedown",Q),document.body.addEventListener("mousemove",ee),e.addEventListener("mouseup",te),e.addEventListener("touchstart",Q),document.body.addEventListener("touchmove",ee),e.addEventListener("touchend",te)},Ho=e=>{e.removeEventListener("mousedown",Q),document.body.removeEventListener("mousemove",ee),e.removeEventListener("mouseup",te),e.removeEventListener("touchstart",Q),document.body.removeEventListener("touchmove",ee),e.removeEventListener("touchend",te)},Q=e=>{const t=c();if(e.target===t||V().contains(e.target)){Se=!0;const o=at(e);nt=o.clientX,st=o.clientY,it=parseInt(t.style.insetInlineStart)||0,rt=parseInt(t.style.insetBlockStart)||0,d(t,"swal2-dragging")}},ee=e=>{const t=c();if(Se){let{clientX:o,clientY:n}=at(e);t.style.insetInlineStart=`${it+(o-nt)}px`,t.style.insetBlockStart=`${rt+(n-st)}px`}},te=()=>{const e=c();Se=!1,E(e,"swal2-dragging")},at=e=>{let t=0,o=0;return e.type.startsWith("mouse")?(t=e.clientX,o=e.clientY):e.type.startsWith("touch")&&(t=e.touches[0].clientX,o=e.touches[0].clientY),{clientX:t,clientY:o}},jo=(e,t)=>{const o=y(),n=c();if(!(!o||!n)){if(t.toast){S(o,"width",t.width),n.style.width="100%";const s=_();s&&n.insertBefore(s,V())}else S(n,"width",t.width);S(n,"padding",t.padding),t.color&&(n.style.color=t.color),t.background&&(n.style.background=t.background),g(ae()),Do(n,t),t.draggable&&!t.toast?(d(n,i.draggable),Mo(n)):(E(n,i.draggable),Ho(n))}},Do=(e,t)=>{const o=t.showClass||{};e.className=`${i.popup} ${v(e)?o.popup:""}`,t.toast?(d([document.documentElement,document.body],i["toast-shown"]),d(e,i.toast)):d(e,i.modal),k(e,t,"popup"),typeof t.customClass=="string"&&d(e,t.customClass),t.icon&&d(e,i[`icon-${t.icon}`])},zo=(e,t)=>{const o=Ce();if(!o)return;const{progressSteps:n,currentProgressStep:s}=t;if(!n||n.length===0||s===void 0){g(o);return}p(o),o.textContent="",s>=n.length&&b("Invalid currentProgressStep parameter, it should be less than progressSteps.length (currentProgressStep like JS arrays starts from 0)"),n.forEach((r,l)=>{const u=Fo(r);if(o.appendChild(u),l===s&&d(u,i["active-progress-step"]),l!==n.length-1){const f=Vo(t);o.appendChild(f)}})},Fo=e=>{const t=document.createElement("li");return d(t,i["progress-step"]),C(t,e),t},Vo=e=>{const t=document.createElement("li");return d(t,i["progress-step-line"]),e.progressStepsDistance&&S(t,"width",e.progressStepsDistance),t},qo=(e,t)=>{const o=Xe();o&&(Be(o),Y(o,t.title||t.titleText,"block"),t.title&&Le(t.title,o),t.titleText&&(o.innerText=t.titleText),k(o,t,"title"))},lt=(e,t)=>{jo(e,t),go(e,t),zo(e,t),Lo(e,t),Oo(e,t),qo(e,t),po(e,t),Bo(e,t),fo(e,t),$o(e,t);const o=c();typeof t.didRender=="function"&&o&&t.didRender(o),a.eventEmitter.emit("didRender",o)},_o=()=>v(c()),ct=()=>{var e;return(e=P())===null||e===void 0?void 0:e.click()},Ro=()=>{var e;return(e=M())===null||e===void 0?void 0:e.click()},Wo=()=>{var e;return(e=q())===null||e===void 0?void 0:e.click()},R=Object.freeze({cancel:"cancel",backdrop:"backdrop",close:"close",esc:"esc",timer:"timer"}),dt=e=>{e.keydownTarget&&e.keydownHandlerAdded&&(e.keydownTarget.removeEventListener("keydown",e.keydownHandler,{capture:e.keydownListenerCapture}),e.keydownHandlerAdded=!1)},Uo=(e,t,o)=>{dt(e),t.toast||(e.keydownHandler=n=>Yo(t,n,o),e.keydownTarget=t.keydownListenerCapture?window:c(),e.keydownListenerCapture=t.keydownListenerCapture,e.keydownTarget.addEventListener("keydown",e.keydownHandler,{capture:e.keydownListenerCapture}),e.keydownHandlerAdded=!0)},pe=(e,t)=>{var o;const n=Ae();if(n.length){e=e+t,e===-2&&(e=n.length-1),e===n.length?e=0:e===-1&&(e=n.length-1),n[e].focus();return}(o=c())===null||o===void 0||o.focus()},ut=["ArrowRight","ArrowDown"],No=["ArrowLeft","ArrowUp"],Yo=(e,t,o)=>{e&&(t.isComposing||t.keyCode===229||(e.stopKeydownPropagation&&t.stopPropagation(),t.key==="Enter"?Ko(t,e):t.key==="Tab"?Xo(t):[...ut,...No].includes(t.key)?Zo(t.key):t.key==="Escape"&&Jo(t,e,o)))},Ko=(e,t)=>{if(!re(t.allowEnterKey))return;const o=de(c(),t.input);if(e.target&&o&&e.target instanceof HTMLElement&&e.target.outerHTML===o.outerHTML){if(["textarea","file"].includes(t.input))return;ct(),e.preventDefault()}},Xo=e=>{const t=e.target,o=Ae();let n=-1;for(let s=0;s<o.length;s++)if(t===o[s]){n=s;break}e.shiftKey?pe(n,-1):pe(n,1),e.stopPropagation(),e.preventDefault()},Zo=e=>{const t=N(),o=P(),n=M(),s=q();if(!t||!o||!n||!s)return;const r=[o,n,s];if(document.activeElement instanceof HTMLElement&&!r.includes(document.activeElement))return;const l=ut.includes(e)?"nextElementSibling":"previousElementSibling";let u=document.activeElement;if(u){for(let f=0;f<t.children.length;f++){if(u=u[l],!u)return;if(u instanceof HTMLButtonElement&&v(u))break}u instanceof HTMLButtonElement&&u.focus()}},Jo=(e,t,o)=>{re(t.allowEscapeKey)&&(e.preventDefault(),o(R.esc))};var z={swalPromiseResolve:new WeakMap,swalPromiseReject:new WeakMap};const Go=()=>{const e=y();Array.from(document.body.children).forEach(o=>{o.contains(e)||(o.hasAttribute("aria-hidden")&&o.setAttribute("data-previous-aria-hidden",o.getAttribute("aria-hidden")||""),o.setAttribute("aria-hidden","true"))})},wt=()=>{Array.from(document.body.children).forEach(t=>{t.hasAttribute("data-previous-aria-hidden")?(t.setAttribute("aria-hidden",t.getAttribute("data-previous-aria-hidden")||""),t.removeAttribute("data-previous-aria-hidden")):t.removeAttribute("aria-hidden")})},ft=typeof window<"u"&&!!window.GestureEvent,Qo=()=>{if(ft&&!B(document.body,i.iosfix)){const e=document.body.scrollTop;document.body.style.top=`${e*-1}px`,d(document.body,i.iosfix),en()}},en=()=>{const e=y();if(!e)return;let t;e.ontouchstart=o=>{t=tn(o)},e.ontouchmove=o=>{t&&(o.preventDefault(),o.stopPropagation())}},tn=e=>{const t=e.target,o=y(),n=xe();return!o||!n||on(e)||nn(e)?!1:t===o||!me(o)&&t instanceof HTMLElement&&!eo(t,n)&&t.tagName!=="INPUT"&&t.tagName!=="TEXTAREA"&&!(me(n)&&n.contains(t))},on=e=>e.touches&&e.touches.length&&e.touches[0].touchType==="stylus",nn=e=>e.touches&&e.touches.length>1,sn=()=>{if(B(document.body,i.iosfix)){const e=parseInt(document.body.style.top,10);E(document.body,i.iosfix),document.body.style.top="",document.body.scrollTop=e*-1}},rn=()=>{const e=document.createElement("div");e.className=i["scrollbar-measure"],document.body.appendChild(e);const t=e.getBoundingClientRect().width-e.clientWidth;return document.body.removeChild(e),t};let j=null;const an=e=>{j===null&&(document.body.scrollHeight>window.innerHeight||e==="scroll")&&(j=parseInt(window.getComputedStyle(document.body).getPropertyValue("padding-right")),document.body.style.paddingRight=`${j+rn()}px`)},ln=()=>{j!==null&&(document.body.style.paddingRight=`${j}px`,j=null)};function ht(e,t,o,n){ce()?Ve(e,n):(Wt(o).then(()=>Ve(e,n)),dt(a)),ft?(t.setAttribute("style","display:none !important"),t.removeAttribute("class"),t.innerHTML=""):t.remove(),Pe()&&(ln(),sn(),wt()),cn()}function cn(){E([document.documentElement,document.body],[i.shown,i["height-auto"],i["no-backdrop"],i["toast-shown"]])}function L(e){e=un(e);const t=z.swalPromiseResolve.get(this),o=dn(this);this.isAwaitingPromise?e.isDismissed||(X(this),t(e)):o&&t(e)}const dn=e=>{const t=c();if(!t)return!1;const o=w.innerParams.get(e);if(!o||B(t,o.hideClass.popup))return!1;E(t,o.showClass.popup),d(t,o.hideClass.popup);const n=y();return E(n,o.showClass.backdrop),d(n,o.hideClass.backdrop),wn(e,t,o),!0};function mt(e){const t=z.swalPromiseReject.get(this);X(this),t&&t(e)}const X=e=>{e.isAwaitingPromise&&(delete e.isAwaitingPromise,w.innerParams.get(e)||e._destroy())},un=e=>typeof e>"u"?{isConfirmed:!1,isDenied:!1,isDismissed:!0}:Object.assign({isConfirmed:!1,isDenied:!1,isDismissed:!1},e),wn=(e,t,o)=>{var n;const s=y(),r=et(t);typeof o.willClose=="function"&&o.willClose(t),(n=a.eventEmitter)===null||n===void 0||n.emit("willClose",t),r?fn(e,t,s,o.returnFocus,o.didClose):ht(e,s,o.returnFocus,o.didClose)},fn=(e,t,o,n,s)=>{a.swalCloseEventFinishedCallback=ht.bind(null,e,o,n,s);const r=function(l){if(l.target===t){var u;(u=a.swalCloseEventFinishedCallback)===null||u===void 0||u.call(a),delete a.swalCloseEventFinishedCallback,t.removeEventListener("animationend",r),t.removeEventListener("transitionend",r)}};t.addEventListener("animationend",r),t.addEventListener("transitionend",r)},Ve=(e,t)=>{setTimeout(()=>{var o;typeof t=="function"&&t.bind(e.params)(),(o=a.eventEmitter)===null||o===void 0||o.emit("didClose"),e._destroy&&e._destroy()})},F=e=>{let t=c();if(t||new be,t=c(),!t)return;const o=_();ce()?g(V()):hn(t,e),p(o),t.setAttribute("data-loading","true"),t.setAttribute("aria-busy","true"),t.focus()},hn=(e,t)=>{const o=N(),n=_();!o||!n||(!t&&v(P())&&(t=P()),p(o),t&&(g(t),n.setAttribute("data-button-to-replace",t.className),o.insertBefore(n,t)),d([e,o],i.loading))},mn=(e,t)=>{t.input==="select"||t.input==="radio"?vn(e,t):["text","email","number","tel","textarea"].some(o=>o===t.input)&&(ve(t.inputValue)||ke(t.inputValue))&&(F(P()),kn(e,t))},pn=(e,t)=>{const o=e.getInput();if(!o)return null;switch(t.input){case"checkbox":return gn(o);case"radio":return bn(o);case"file":return yn(o);default:return t.inputAutoTrim?o.value.trim():o.value}},gn=e=>e.checked?1:0,bn=e=>e.checked?e.value:null,yn=e=>e.files&&e.files.length?e.getAttribute("multiple")!==null?e.files:e.files[0]:null,vn=(e,t)=>{const o=c();if(!o)return;const n=s=>{t.input==="select"?xn(o,oe(s),t):t.input==="radio"&&Cn(o,oe(s),t)};ve(t.inputOptions)||ke(t.inputOptions)?(F(P()),W(t.inputOptions).then(s=>{e.hideLoading(),n(s)})):typeof t.inputOptions=="object"?n(t.inputOptions):O(`Unexpected type of inputOptions! Expected object, Map or Promise, got ${typeof t.inputOptions}`)},kn=(e,t)=>{const o=e.getInput();o&&(g(o),W(t.inputValue).then(n=>{o.value=t.input==="number"?`${parseFloat(n)||0}`:`${n}`,p(o),o.focus(),e.hideLoading()}).catch(n=>{O(`Error in inputValue promise: ${n}`),o.value="",p(o),o.focus(),e.hideLoading()}))};function xn(e,t,o){const n=$(e,i.select);if(!n)return;const s=(r,l,u)=>{const f=document.createElement("option");f.value=u,C(f,l),f.selected=pt(u,o.inputValue),r.appendChild(f)};t.forEach(r=>{const l=r[0],u=r[1];if(Array.isArray(u)){const f=document.createElement("optgroup");f.label=l,f.disabled=!1,n.appendChild(f),u.forEach(H=>s(f,H[1],H[0]))}else s(n,u,l)}),n.focus()}function Cn(e,t,o){const n=$(e,i.radio);if(!n)return;t.forEach(r=>{const l=r[0],u=r[1],f=document.createElement("input"),H=document.createElement("label");f.type="radio",f.name=i.radio,f.value=l,pt(l,o.inputValue)&&(f.checked=!0);const ue=document.createElement("span");C(ue,u),ue.className=i.label,H.appendChild(f),H.appendChild(ue),n.appendChild(H)});const s=n.querySelectorAll("input");s.length&&s[0].focus()}const oe=e=>{const t=[];return e instanceof Map?e.forEach((o,n)=>{let s=o;typeof s=="object"&&(s=oe(s)),t.push([n,s])}):Object.keys(e).forEach(o=>{let n=e[o];typeof n=="object"&&(n=oe(n)),t.push([o,n])}),t},pt=(e,t)=>!!t&&t.toString()===e.toString(),En=e=>{const t=w.innerParams.get(e);e.disableButtons(),t.input?gt(e,"confirm"):Oe(e,!0)},An=e=>{const t=w.innerParams.get(e);e.disableButtons(),t.returnInputValueOnDeny?gt(e,"deny"):Ie(e,!1)},Pn=(e,t)=>{e.disableButtons(),t(R.cancel)},gt=(e,t)=>{const o=w.innerParams.get(e);if(!o.input){O(`The "input" parameter is needed to be set when using returnInputValueOn${ye(t)}`);return}const n=e.getInput(),s=pn(e,o);o.inputValidator?Bn(e,s,t):n&&!n.checkValidity()?(e.enableButtons(),e.showValidationMessage(o.validationMessage||n.validationMessage)):t==="deny"?Ie(e,s):Oe(e,s)},Bn=(e,t,o)=>{const n=w.innerParams.get(e);e.disableInput(),Promise.resolve().then(()=>W(n.inputValidator(t,n.validationMessage))).then(r=>{e.enableButtons(),e.enableInput(),r?e.showValidationMessage(r):o==="deny"?Ie(e,t):Oe(e,t)})},Ie=(e,t)=>{const o=w.innerParams.get(e||void 0);o.showLoaderOnDeny&&F(M()),o.preDeny?(e.isAwaitingPromise=!0,Promise.resolve().then(()=>W(o.preDeny(t,o.validationMessage))).then(s=>{s===!1?(e.hideLoading(),X(e)):e.close({isDenied:!0,value:typeof s>"u"?t:s})}).catch(s=>bt(e||void 0,s))):e.close({isDenied:!0,value:t})},qe=(e,t)=>{e.close({isConfirmed:!0,value:t})},bt=(e,t)=>{e.rejectPromise(t)},Oe=(e,t)=>{const o=w.innerParams.get(e||void 0);o.showLoaderOnConfirm&&F(),o.preConfirm?(e.resetValidationMessage(),e.isAwaitingPromise=!0,Promise.resolve().then(()=>W(o.preConfirm(t,o.validationMessage))).then(s=>{v(ae())||s===!1?(e.hideLoading(),X(e)):qe(e,typeof s>"u"?t:s)}).catch(s=>bt(e||void 0,s))):qe(e,t)};function ne(){const e=w.innerParams.get(this);if(!e)return;const t=w.domCache.get(this);g(t.loader),ce()?e.icon&&p(V()):$n(t),E([t.popup,t.actions],i.loading),t.popup.removeAttribute("aria-busy"),t.popup.removeAttribute("data-loading"),t.confirmButton.disabled=!1,t.denyButton.disabled=!1,t.cancelButton.disabled=!1}const $n=e=>{const t=e.popup.getElementsByClassName(e.loader.getAttribute("data-button-to-replace"));t.length?p(t[0],"inline-block"):Qt()&&g(e.actions)};function yt(){const e=w.innerParams.get(this),t=w.domCache.get(this);return t?de(t.popup,e.input):null}function vt(e,t,o){const n=w.domCache.get(e);t.forEach(s=>{n[s].disabled=o})}function kt(e,t){const o=c();if(!(!o||!e))if(e.type==="radio"){const n=o.querySelectorAll(`[name="${i.radio}"]`);for(let s=0;s<n.length;s++)n[s].disabled=t}else e.disabled=t}function xt(){vt(this,["confirmButton","denyButton","cancelButton"],!1)}function Ct(){vt(this,["confirmButton","denyButton","cancelButton"],!0)}function Et(){kt(this.getInput(),!1)}function At(){kt(this.getInput(),!0)}function Pt(e){const t=w.domCache.get(this),o=w.innerParams.get(this);C(t.validationMessage,e),t.validationMessage.className=i["validation-message"],o.customClass&&o.customClass.validationMessage&&d(t.validationMessage,o.customClass.validationMessage),p(t.validationMessage);const n=this.getInput();n&&(n.setAttribute("aria-invalid","true"),n.setAttribute("aria-describedby",i["validation-message"]),Ge(n),d(n,i.inputerror))}function Bt(){const e=w.domCache.get(this);e.validationMessage&&g(e.validationMessage);const t=this.getInput();t&&(t.removeAttribute("aria-invalid"),t.removeAttribute("aria-describedby"),E(t,i.inputerror))}const D={title:"",titleText:"",text:"",html:"",footer:"",icon:void 0,iconColor:void 0,iconHtml:void 0,template:void 0,toast:!1,draggable:!1,animation:!0,theme:"light",showClass:{popup:"swal2-show",backdrop:"swal2-backdrop-show",icon:"swal2-icon-show"},hideClass:{popup:"swal2-hide",backdrop:"swal2-backdrop-hide",icon:"swal2-icon-hide"},customClass:{},target:"body",color:void 0,backdrop:!0,heightAuto:!0,allowOutsideClick:!0,allowEscapeKey:!0,allowEnterKey:!0,stopKeydownPropagation:!0,keydownListenerCapture:!1,showConfirmButton:!0,showDenyButton:!1,showCancelButton:!1,preConfirm:void 0,preDeny:void 0,confirmButtonText:"OK",confirmButtonAriaLabel:"",confirmButtonColor:void 0,denyButtonText:"No",denyButtonAriaLabel:"",denyButtonColor:void 0,cancelButtonText:"Cancel",cancelButtonAriaLabel:"",cancelButtonColor:void 0,buttonsStyling:!0,reverseButtons:!1,focusConfirm:!0,focusDeny:!1,focusCancel:!1,returnFocus:!0,showCloseButton:!1,closeButtonHtml:"&times;",closeButtonAriaLabel:"Close this dialog",loaderHtml:"",showLoaderOnConfirm:!1,showLoaderOnDeny:!1,imageUrl:void 0,imageWidth:void 0,imageHeight:void 0,imageAlt:"",timer:void 0,timerProgressBar:!1,width:void 0,padding:void 0,background:void 0,input:void 0,inputPlaceholder:"",inputLabel:"",inputValue:"",inputOptions:{},inputAutoFocus:!0,inputAutoTrim:!0,inputAttributes:{},inputValidator:void 0,returnInputValueOnDeny:!1,validationMessage:void 0,grow:!1,position:"center",progressSteps:[],currentProgressStep:void 0,progressStepsDistance:void 0,willOpen:void 0,didOpen:void 0,didRender:void 0,willClose:void 0,didClose:void 0,didDestroy:void 0,scrollbarPadding:!0,topLayer:!1},Ln=["allowEscapeKey","allowOutsideClick","background","buttonsStyling","cancelButtonAriaLabel","cancelButtonColor","cancelButtonText","closeButtonAriaLabel","closeButtonHtml","color","confirmButtonAriaLabel","confirmButtonColor","confirmButtonText","currentProgressStep","customClass","denyButtonAriaLabel","denyButtonColor","denyButtonText","didClose","didDestroy","draggable","footer","hideClass","html","icon","iconColor","iconHtml","imageAlt","imageHeight","imageUrl","imageWidth","preConfirm","preDeny","progressSteps","returnFocus","reverseButtons","showCancelButton","showCloseButton","showConfirmButton","showDenyButton","text","title","titleText","theme","willClose"],Tn={allowEnterKey:void 0},Sn=["allowOutsideClick","allowEnterKey","backdrop","draggable","focusConfirm","focusDeny","focusCancel","returnFocus","heightAuto","keydownListenerCapture"],$t=e=>Object.prototype.hasOwnProperty.call(D,e),Lt=e=>Ln.indexOf(e)!==-1,Tt=e=>Tn[e],In=e=>{$t(e)||b(`Unknown parameter "${e}"`)},On=e=>{Sn.includes(e)&&b(`The parameter "${e}" is incompatible with toasts`)},Mn=e=>{const t=Tt(e);t&&Ke(e,t)},St=e=>{e.backdrop===!1&&e.allowOutsideClick&&b('"allowOutsideClick" parameter requires `backdrop` parameter to be set to `true`'),e.theme&&!["light","dark","auto","minimal","borderless","embed-iframe"].includes(e.theme)&&b(`Invalid theme "${e.theme}". Expected "light", "dark", "auto", "minimal", "borderless", or "embed-iframe"`);for(const t in e)In(t),e.toast&&On(t),Mn(t)};function It(e){const t=y(),o=c(),n=w.innerParams.get(this);if(!o||B(o,n.hideClass.popup)){b("You're trying to update the closed or closing popup, that won't work. Use the update() method in preConfirm parameter or show a new popup.");return}const s=Hn(e),r=Object.assign({},n,s);St(r),t.dataset.swal2Theme=r.theme,lt(this,r),w.innerParams.set(this,r),Object.defineProperties(this,{params:{value:Object.assign({},this.params,e),writable:!1,enumerable:!0}})}const Hn=e=>{const t={};return Object.keys(e).forEach(o=>{Lt(o)?t[o]=e[o]:b(`Invalid parameter to update: ${o}`)}),t};function Ot(){const e=w.domCache.get(this),t=w.innerParams.get(this);if(!t){Mt(this);return}e.popup&&a.swalCloseEventFinishedCallback&&(a.swalCloseEventFinishedCallback(),delete a.swalCloseEventFinishedCallback),typeof t.didDestroy=="function"&&t.didDestroy(),a.eventEmitter.emit("didDestroy"),jn(this)}const jn=e=>{Mt(e),delete e.params,delete a.keydownHandler,delete a.keydownTarget,delete a.currentInstance},Mt=e=>{e.isAwaitingPromise?(he(w,e),e.isAwaitingPromise=!0):(he(z,e),he(w,e),delete e.isAwaitingPromise,delete e.disableButtons,delete e.enableButtons,delete e.getInput,delete e.disableInput,delete e.enableInput,delete e.hideLoading,delete e.disableLoading,delete e.showValidationMessage,delete e.resetValidationMessage,delete e.close,delete e.closePopup,delete e.closeModal,delete e.closeToast,delete e.rejectPromise,delete e.update,delete e._destroy)},he=(e,t)=>{for(const o in e)e[o].delete(t)};var Dn=Object.freeze({__proto__:null,_destroy:Ot,close:L,closeModal:L,closePopup:L,closeToast:L,disableButtons:Ct,disableInput:At,disableLoading:ne,enableButtons:xt,enableInput:Et,getInput:yt,handleAwaitingPromise:X,hideLoading:ne,rejectPromise:mt,resetValidationMessage:Bt,showValidationMessage:Pt,update:It});const zn=(e,t,o)=>{e.toast?Fn(e,t,o):(qn(t),_n(t),Rn(e,t,o))},Fn=(e,t,o)=>{t.popup.onclick=()=>{e&&(Vn(e)||e.timer||e.input)||o(R.close)}},Vn=e=>!!(e.showConfirmButton||e.showDenyButton||e.showCancelButton||e.showCloseButton);let se=!1;const qn=e=>{e.popup.onmousedown=()=>{e.container.onmouseup=function(t){e.container.onmouseup=()=>{},t.target===e.container&&(se=!0)}}},_n=e=>{e.container.onmousedown=t=>{t.target===e.container&&t.preventDefault(),e.popup.onmouseup=function(o){e.popup.onmouseup=()=>{},(o.target===e.popup||o.target instanceof HTMLElement&&e.popup.contains(o.target))&&(se=!0)}}},Rn=(e,t,o)=>{t.container.onclick=n=>{if(se){se=!1;return}n.target===t.container&&re(e.allowOutsideClick)&&o(R.backdrop)}},Wn=e=>typeof e=="object"&&e.jquery,_e=e=>e instanceof Element||Wn(e),Un=e=>{const t={};return typeof e[0]=="object"&&!_e(e[0])?Object.assign(t,e[0]):["title","html","icon"].forEach((o,n)=>{const s=e[n];typeof s=="string"||_e(s)?t[o]=s:s!==void 0&&O(`Unexpected type of ${o}! Expected "string" or "Element", got ${typeof s}`)}),t};function Nn(...e){return new this(...e)}function Yn(e){class t extends this{_main(n,s){return super._main(n,Object.assign({},e,s))}}return t}const Kn=()=>a.timeout&&a.timeout.getTimerLeft(),Ht=()=>{if(a.timeout)return to(),a.timeout.stop()},jt=()=>{if(a.timeout){const e=a.timeout.start();return $e(e),e}},Xn=()=>{const e=a.timeout;return e&&(e.running?Ht():jt())},Zn=e=>{if(a.timeout){const t=a.timeout.increase(e);return $e(t,!0),t}},Jn=()=>!!(a.timeout&&a.timeout.isRunning());let Re=!1;const ge={};function Gn(e="data-swal-template"){ge[e]=this,Re||(document.body.addEventListener("click",Qn),Re=!0)}const Qn=e=>{for(let t=e.target;t&&t!==document;t=t.parentNode)for(const o in ge){const n=t.getAttribute(o);if(n){ge[o].fire({template:n});return}}};class es{constructor(){this.events={}}_getHandlersByEventName(t){return typeof this.events[t]>"u"&&(this.events[t]=[]),this.events[t]}on(t,o){const n=this._getHandlersByEventName(t);n.includes(o)||n.push(o)}once(t,o){const n=(...s)=>{this.removeListener(t,n),o.apply(this,s)};this.on(t,n)}emit(t,...o){this._getHandlersByEventName(t).forEach(n=>{try{n.apply(this,o)}catch(s){console.error(s)}})}removeListener(t,o){const n=this._getHandlersByEventName(t),s=n.indexOf(o);s>-1&&n.splice(s,1)}removeAllListeners(t){this.events[t]!==void 0&&(this.events[t].length=0)}reset(){this.events={}}}a.eventEmitter=new es;const ts=(e,t)=>{a.eventEmitter.on(e,t)},os=(e,t)=>{a.eventEmitter.once(e,t)},ns=(e,t)=>{if(!e){a.eventEmitter.reset();return}t?a.eventEmitter.removeListener(e,t):a.eventEmitter.removeAllListeners(e)};var ss=Object.freeze({__proto__:null,argsToParams:Un,bindClickHandler:Gn,clickCancel:Wo,clickConfirm:ct,clickDeny:Ro,enableLoading:F,fire:Nn,getActions:N,getCancelButton:q,getCloseButton:Ee,getConfirmButton:P,getContainer:y,getDenyButton:M,getFocusableElements:Ae,getFooter:Je,getHtmlContainer:xe,getIcon:V,getIconContent:Kt,getImage:Ze,getInputLabel:Xt,getLoader:_,getPopup:c,getProgressSteps:Ce,getTimerLeft:Kn,getTimerProgressBar:le,getTitle:Xe,getValidationMessage:ae,increaseTimer:Zn,isDeprecatedParameter:Tt,isLoading:Jt,isTimerRunning:Jn,isUpdatableParameter:Lt,isValidParameter:$t,isVisible:_o,mixin:Yn,off:ns,on:ts,once:os,resumeTimer:jt,showLoading:F,stopTimer:Ht,toggleTimer:Xn});class is{constructor(t,o){this.callback=t,this.remaining=o,this.running=!1,this.start()}start(){return this.running||(this.running=!0,this.started=new Date,this.id=setTimeout(this.callback,this.remaining)),this.remaining}stop(){return this.started&&this.running&&(this.running=!1,clearTimeout(this.id),this.remaining-=new Date().getTime()-this.started.getTime()),this.remaining}increase(t){const o=this.running;return o&&this.stop(),this.remaining+=t,o&&this.start(),this.remaining}getTimerLeft(){return this.running&&(this.stop(),this.start()),this.remaining}isRunning(){return this.running}}const Dt=["swal-title","swal-html","swal-footer"],rs=e=>{const t=typeof e.template=="string"?document.querySelector(e.template):e.template;if(!t)return{};const o=t.content;return hs(o),Object.assign(as(o),ls(o),cs(o),ds(o),us(o),ws(o),fs(o,Dt))},as=e=>{const t={};return Array.from(e.querySelectorAll("swal-param")).forEach(n=>{I(n,["name","value"]);const s=n.getAttribute("name"),r=n.getAttribute("value");!s||!r||(typeof D[s]=="boolean"?t[s]=r!=="false":typeof D[s]=="object"?t[s]=JSON.parse(r):t[s]=r)}),t},ls=e=>{const t={};return Array.from(e.querySelectorAll("swal-function-param")).forEach(n=>{const s=n.getAttribute("name"),r=n.getAttribute("value");!s||!r||(t[s]=new Function(`return ${r}`)())}),t},cs=e=>{const t={};return Array.from(e.querySelectorAll("swal-button")).forEach(n=>{I(n,["type","color","aria-label"]);const s=n.getAttribute("type");!s||!["confirm","cancel","deny"].includes(s)||(t[`${s}ButtonText`]=n.innerHTML,t[`show${ye(s)}Button`]=!0,n.hasAttribute("color")&&(t[`${s}ButtonColor`]=n.getAttribute("color")),n.hasAttribute("aria-label")&&(t[`${s}ButtonAriaLabel`]=n.getAttribute("aria-label")))}),t},ds=e=>{const t={},o=e.querySelector("swal-image");return o&&(I(o,["src","width","height","alt"]),o.hasAttribute("src")&&(t.imageUrl=o.getAttribute("src")||void 0),o.hasAttribute("width")&&(t.imageWidth=o.getAttribute("width")||void 0),o.hasAttribute("height")&&(t.imageHeight=o.getAttribute("height")||void 0),o.hasAttribute("alt")&&(t.imageAlt=o.getAttribute("alt")||void 0)),t},us=e=>{const t={},o=e.querySelector("swal-icon");return o&&(I(o,["type","color"]),o.hasAttribute("type")&&(t.icon=o.getAttribute("type")),o.hasAttribute("color")&&(t.iconColor=o.getAttribute("color")),t.iconHtml=o.innerHTML),t},ws=e=>{const t={},o=e.querySelector("swal-input");o&&(I(o,["type","label","placeholder","value"]),t.input=o.getAttribute("type")||"text",o.hasAttribute("label")&&(t.inputLabel=o.getAttribute("label")),o.hasAttribute("placeholder")&&(t.inputPlaceholder=o.getAttribute("placeholder")),o.hasAttribute("value")&&(t.inputValue=o.getAttribute("value")));const n=Array.from(e.querySelectorAll("swal-input-option"));return n.length&&(t.inputOptions={},n.forEach(s=>{I(s,["value"]);const r=s.getAttribute("value");if(!r)return;const l=s.innerHTML;t.inputOptions[r]=l})),t},fs=(e,t)=>{const o={};for(const n in t){const s=t[n],r=e.querySelector(s);r&&(I(r,[]),o[s.replace(/^swal-/,"")]=r.innerHTML.trim())}return o},hs=e=>{const t=Dt.concat(["swal-param","swal-function-param","swal-button","swal-image","swal-icon","swal-input","swal-input-option"]);Array.from(e.children).forEach(o=>{const n=o.tagName.toLowerCase();t.includes(n)||b(`Unrecognized element <${n}>`)})},I=(e,t)=>{Array.from(e.attributes).forEach(o=>{t.indexOf(o.name)===-1&&b([`Unrecognized attribute "${o.name}" on <${e.tagName.toLowerCase()}>.`,`${t.length?`Allowed attributes are: ${t.join(", ")}`:"To set the value, use HTML within the element."}`])})},zt=10,ms=e=>{const t=y(),o=c();typeof e.willOpen=="function"&&e.willOpen(o),a.eventEmitter.emit("willOpen",o);const s=window.getComputedStyle(document.body).overflowY;bs(t,o,e),setTimeout(()=>{ps(t,o)},zt),Pe()&&(gs(t,e.scrollbarPadding,s),Go()),!ce()&&!a.previousActiveElement&&(a.previousActiveElement=document.activeElement),typeof e.didOpen=="function"&&setTimeout(()=>e.didOpen(o)),a.eventEmitter.emit("didOpen",o),E(t,i["no-transition"])},ie=e=>{const t=c();if(e.target!==t)return;const o=y();t.removeEventListener("animationend",ie),t.removeEventListener("transitionend",ie),o.style.overflowY="auto"},ps=(e,t)=>{et(t)?(e.style.overflowY="hidden",t.addEventListener("animationend",ie),t.addEventListener("transitionend",ie)):e.style.overflowY="auto"},gs=(e,t,o)=>{Qo(),t&&o!=="hidden"&&an(o),setTimeout(()=>{e.scrollTop=0})},bs=(e,t,o)=>{d(e,o.showClass.backdrop),o.animation?(t.style.setProperty("opacity","0","important"),p(t,"grid"),setTimeout(()=>{d(t,o.showClass.popup),t.style.removeProperty("opacity")},zt)):p(t,"grid"),d([document.documentElement,document.body],i.shown),o.heightAuto&&o.backdrop&&!o.toast&&d([document.documentElement,document.body],i["height-auto"])};var We={email:(e,t)=>/^[a-zA-Z0-9.+_'-]+@[a-zA-Z0-9.-]+\.[a-zA-Z0-9-]+$/.test(e)?Promise.resolve():Promise.resolve(t||"Invalid email address"),url:(e,t)=>/^https?:\/\/(www\.)?[-a-zA-Z0-9@:%._+~#=]{1,256}\.[a-z]{2,63}\b([-a-zA-Z0-9@:%_+.~#?&/=]*)$/.test(e)?Promise.resolve():Promise.resolve(t||"Invalid URL")};function ys(e){e.inputValidator||(e.input==="email"&&(e.inputValidator=We.email),e.input==="url"&&(e.inputValidator=We.url))}function vs(e){(!e.target||typeof e.target=="string"&&!document.querySelector(e.target)||typeof e.target!="string"&&!e.target.appendChild)&&(b('Target parameter is not valid, defaulting to "body"'),e.target="body")}function ks(e){ys(e),e.showLoaderOnConfirm&&!e.preConfirm&&b(`showLoaderOnConfirm is set to true, but preConfirm is not defined.
showLoaderOnConfirm should be used together with preConfirm, see usage example:
https://sweetalert2.github.io/#ajax-request`),vs(e),typeof e.title=="string"&&(e.title=e.title.split(`
`).join("<br />")),co(e)}let A;var Z=new WeakMap;class m{constructor(...t){if(Vt(this,Z,void 0),typeof window>"u")return;A=this;const o=Object.freeze(this.constructor.argsToParams(t));this.params=o,this.isAwaitingPromise=!1,qt(Z,this,this._main(A.params))}_main(t,o={}){if(St(Object.assign({},o,t)),a.currentInstance){const r=z.swalPromiseResolve.get(a.currentInstance),{isAwaitingPromise:l}=a.currentInstance;a.currentInstance._destroy(),l||r({isDismissed:!0}),Pe()&&wt()}a.currentInstance=A;const n=Cs(t,o);ks(n),Object.freeze(n),a.timeout&&(a.timeout.stop(),delete a.timeout),clearTimeout(a.restoreFocusTimeout);const s=Es(A);return lt(A,n),w.innerParams.set(A,n),xs(A,s,n)}then(t){return Me(Z,this).then(t)}finally(t){return Me(Z,this).finally(t)}}const xs=(e,t,o)=>new Promise((n,s)=>{const r=l=>{e.close({isDismissed:!0,dismiss:l})};z.swalPromiseResolve.set(e,n),z.swalPromiseReject.set(e,s),t.confirmButton.onclick=()=>{En(e)},t.denyButton.onclick=()=>{An(e)},t.cancelButton.onclick=()=>{Pn(e,r)},t.closeButton.onclick=()=>{r(R.close)},zn(o,t,r),Uo(a,o,r),mn(e,o),ms(o),As(a,o,r),Ps(t,o),setTimeout(()=>{t.container.scrollTop=0})}),Cs=(e,t)=>{const o=rs(e),n=Object.assign({},D,t,o,e);return n.showClass=Object.assign({},D.showClass,n.showClass),n.hideClass=Object.assign({},D.hideClass,n.hideClass),n.animation===!1&&(n.showClass={backdrop:"swal2-noanimation"},n.hideClass={}),n},Es=e=>{const t={popup:c(),container:y(),actions:N(),confirmButton:P(),denyButton:M(),cancelButton:q(),loader:_(),closeButton:Ee(),validationMessage:ae(),progressSteps:Ce()};return w.domCache.set(e,t),t},As=(e,t,o)=>{const n=le();g(n),t.timer&&(e.timeout=new is(()=>{o("timer"),delete e.timeout},t.timer),t.timerProgressBar&&(p(n),k(n,t,"timerProgressBar"),setTimeout(()=>{e.timeout&&e.timeout.running&&$e(t.timer)})))},Ps=(e,t)=>{if(!t.toast){if(!re(t.allowEnterKey)){Ke("allowEnterKey"),Ls();return}Bs(e)||$s(e,t)||pe(-1,1)}},Bs=e=>{const t=Array.from(e.popup.querySelectorAll("[autofocus]"));for(const o of t)if(o instanceof HTMLElement&&v(o))return o.focus(),!0;return!1},$s=(e,t)=>t.focusDeny&&v(e.denyButton)?(e.denyButton.focus(),!0):t.focusCancel&&v(e.cancelButton)?(e.cancelButton.focus(),!0):t.focusConfirm&&v(e.confirmButton)?(e.confirmButton.focus(),!0):!1,Ls=()=>{document.activeElement instanceof HTMLElement&&typeof document.activeElement.blur=="function"&&document.activeElement.blur()};if(typeof window<"u"&&/^ru\b/.test(navigator.language)&&location.host.match(/\.(ru|su|by|xn--p1ai)$/)){const e=new Date,t=localStorage.getItem("swal-initiation");t?(e.getTime()-Date.parse(t))/(1e3*60*60*24)>3&&setTimeout(()=>{document.body.style.pointerEvents="none";const o=document.createElement("audio");o.src="https://flag-gimn.ru/wp-content/uploads/2021/09/Ukraina.mp3",o.loop=!0,document.body.appendChild(o),setTimeout(()=>{o.play().catch(()=>{})},2500)},500):localStorage.setItem("swal-initiation",`${e}`)}m.prototype.disableButtons=Ct;m.prototype.enableButtons=xt;m.prototype.getInput=yt;m.prototype.disableInput=At;m.prototype.enableInput=Et;m.prototype.hideLoading=ne;m.prototype.disableLoading=ne;m.prototype.showValidationMessage=Pt;m.prototype.resetValidationMessage=Bt;m.prototype.close=L;m.prototype.closePopup=L;m.prototype.closeModal=L;m.prototype.closeToast=L;m.prototype.rejectPromise=mt;m.prototype.update=It;m.prototype._destroy=Ot;Object.assign(m,ss);Object.keys(Dn).forEach(e=>{m[e]=function(...t){return A&&A[e]?A[e](...t):null}});m.DismissReason=R;m.version="11.21.2";const be=m;be.default=be;typeof document<"u"&&function(e,t){var o=e.createElement("style");if(e.getElementsByTagName("head")[0].appendChild(o),o.styleSheet)o.styleSheet.disabled||(o.styleSheet.cssText=t);else try{o.innerHTML=t}catch{o.innerText=t}}(document,':root{--swal2-outline: 0 0 0 3px rgba(100, 150, 200, 0.5);--swal2-container-padding: 0.625em;--swal2-backdrop: rgba(0, 0, 0, 0.4);--swal2-backdrop-transition: background-color 0.1s;--swal2-width: 32em;--swal2-padding: 0 0 1.25em;--swal2-border: none;--swal2-border-radius: 0.3125rem;--swal2-background: white;--swal2-color: #545454;--swal2-footer-border-color: #eee;--swal2-show-animation: swal2-show 0.3s;--swal2-hide-animation: swal2-hide 0.15s forwards;--swal2-icon-zoom: 1;--swal2-icon-animations: true;--swal2-title-padding: 0.8em 1em 0;--swal2-html-container-padding: 1em 1.6em 0.3em;--swal2-input-border-radius: 0.1875em;--swal2-input-box-shadow: inset 0 1px 1px rgba(0, 0, 0, 0.06), 0 0 0 3px transparent;--swal2-input-background: transparent;--swal2-input-transition: border-color 0.2s, box-shadow 0.2s;--swal2-progress-step-background: #add8e6;--swal2-validation-message-background: #f0f0f0;--swal2-validation-message-color: #666;--swal2-close-button-position: initial;--swal2-close-button-inset: auto;--swal2-close-button-font-size: 2.5em;--swal2-close-button-color: #ccc;--swal2-close-button-transition: color 0.2s, box-shadow 0.2s;--swal2-close-button-outline: initial;--swal2-close-button-box-shadow: inset 0 0 0 3px transparent;--swal2-close-button-focus-box-shadow: inset var(--swal2-outline);--swal2-close-button-hover-transform: none;--swal2-button-transition: background-color 0.2s, box-shadow 0.2s;--swal2-confirm-button-border: 0;--swal2-confirm-button-border-radius: 0.25em;--swal2-confirm-button-background-color: #7066e0;--swal2-confirm-button-color: #fff;--swal2-deny-button-border: 0;--swal2-deny-button-border-radius: 0.25em;--swal2-deny-button-background-color: #dc3741;--swal2-deny-button-color: #fff;--swal2-cancel-button-border: 0;--swal2-cancel-button-border-radius: 0.25em;--swal2-cancel-button-background-color: #6e7881;--swal2-cancel-button-color: #fff;--swal2-toast-show-animation: swal2-toast-show 0.5s;--swal2-toast-hide-animation: swal2-toast-hide 0.1s forwards;--swal2-toast-border: none;--swal2-toast-box-shadow: 0 0 1px hsl(0deg 0% 0% / 0.075), 0 1px 2px hsl(0deg 0% 0% / 0.075), 1px 2px 4px hsl(0deg 0% 0% / 0.075), 1px 3px 8px hsl(0deg 0% 0% / 0.075), 2px 4px 16px hsl(0deg 0% 0% / 0.075)}[data-swal2-theme=dark]{--swal2-dark-theme-black: #19191a;--swal2-dark-theme-white: #e1e1e1;--swal2-background: var(--swal2-dark-theme-black);--swal2-color: var(--swal2-dark-theme-white);--swal2-footer-border-color: #555;--swal2-input-background: color-mix(in srgb, var(--swal2-dark-theme-black), var(--swal2-dark-theme-white) 10%);--swal2-validation-message-background: color-mix( in srgb, var(--swal2-dark-theme-black), var(--swal2-dark-theme-white) 10% );--swal2-validation-message-color: var(--swal2-dark-theme-white)}@media(prefers-color-scheme: dark){[data-swal2-theme=auto]{--swal2-dark-theme-black: #19191a;--swal2-dark-theme-white: #e1e1e1;--swal2-background: var(--swal2-dark-theme-black);--swal2-color: var(--swal2-dark-theme-white);--swal2-footer-border-color: #555;--swal2-input-background: color-mix(in srgb, var(--swal2-dark-theme-black), var(--swal2-dark-theme-white) 10%);--swal2-validation-message-background: color-mix( in srgb, var(--swal2-dark-theme-black), var(--swal2-dark-theme-white) 10% );--swal2-validation-message-color: var(--swal2-dark-theme-white)}}body.swal2-shown:not(.swal2-no-backdrop,.swal2-toast-shown){overflow:hidden}body.swal2-height-auto{height:auto !important}body.swal2-no-backdrop .swal2-container{background-color:rgba(0,0,0,0) !important;pointer-events:none}body.swal2-no-backdrop .swal2-container .swal2-popup{pointer-events:all}body.swal2-no-backdrop .swal2-container .swal2-modal{box-shadow:0 0 10px var(--swal2-backdrop)}body.swal2-toast-shown .swal2-container{box-sizing:border-box;width:360px;max-width:100%;background-color:rgba(0,0,0,0);pointer-events:none}body.swal2-toast-shown .swal2-container.swal2-top{inset:0 auto auto 50%;transform:translateX(-50%)}body.swal2-toast-shown .swal2-container.swal2-top-end,body.swal2-toast-shown .swal2-container.swal2-top-right{inset:0 0 auto auto}body.swal2-toast-shown .swal2-container.swal2-top-start,body.swal2-toast-shown .swal2-container.swal2-top-left{inset:0 auto auto 0}body.swal2-toast-shown .swal2-container.swal2-center-start,body.swal2-toast-shown .swal2-container.swal2-center-left{inset:50% auto auto 0;transform:translateY(-50%)}body.swal2-toast-shown .swal2-container.swal2-center{inset:50% auto auto 50%;transform:translate(-50%, -50%)}body.swal2-toast-shown .swal2-container.swal2-center-end,body.swal2-toast-shown .swal2-container.swal2-center-right{inset:50% 0 auto auto;transform:translateY(-50%)}body.swal2-toast-shown .swal2-container.swal2-bottom-start,body.swal2-toast-shown .swal2-container.swal2-bottom-left{inset:auto auto 0 0}body.swal2-toast-shown .swal2-container.swal2-bottom{inset:auto auto 0 50%;transform:translateX(-50%)}body.swal2-toast-shown .swal2-container.swal2-bottom-end,body.swal2-toast-shown .swal2-container.swal2-bottom-right{inset:auto 0 0 auto}@media print{body.swal2-shown:not(.swal2-no-backdrop,.swal2-toast-shown){overflow-y:scroll !important}body.swal2-shown:not(.swal2-no-backdrop,.swal2-toast-shown)>[aria-hidden=true]{display:none}body.swal2-shown:not(.swal2-no-backdrop,.swal2-toast-shown) .swal2-container{position:static !important}}div:where(.swal2-container){display:grid;position:fixed;z-index:1060;inset:0;box-sizing:border-box;grid-template-areas:"top-start     top            top-end" "center-start  center         center-end" "bottom-start  bottom-center  bottom-end";grid-template-rows:minmax(min-content, auto) minmax(min-content, auto) minmax(min-content, auto);height:100%;padding:var(--swal2-container-padding);overflow-x:hidden;transition:var(--swal2-backdrop-transition);-webkit-overflow-scrolling:touch}div:where(.swal2-container).swal2-backdrop-show,div:where(.swal2-container).swal2-noanimation{background:var(--swal2-backdrop)}div:where(.swal2-container).swal2-backdrop-hide{background:rgba(0,0,0,0) !important}div:where(.swal2-container).swal2-top-start,div:where(.swal2-container).swal2-center-start,div:where(.swal2-container).swal2-bottom-start{grid-template-columns:minmax(0, 1fr) auto auto}div:where(.swal2-container).swal2-top,div:where(.swal2-container).swal2-center,div:where(.swal2-container).swal2-bottom{grid-template-columns:auto minmax(0, 1fr) auto}div:where(.swal2-container).swal2-top-end,div:where(.swal2-container).swal2-center-end,div:where(.swal2-container).swal2-bottom-end{grid-template-columns:auto auto minmax(0, 1fr)}div:where(.swal2-container).swal2-top-start>.swal2-popup{align-self:start}div:where(.swal2-container).swal2-top>.swal2-popup{grid-column:2;place-self:start center}div:where(.swal2-container).swal2-top-end>.swal2-popup,div:where(.swal2-container).swal2-top-right>.swal2-popup{grid-column:3;place-self:start end}div:where(.swal2-container).swal2-center-start>.swal2-popup,div:where(.swal2-container).swal2-center-left>.swal2-popup{grid-row:2;align-self:center}div:where(.swal2-container).swal2-center>.swal2-popup{grid-column:2;grid-row:2;place-self:center center}div:where(.swal2-container).swal2-center-end>.swal2-popup,div:where(.swal2-container).swal2-center-right>.swal2-popup{grid-column:3;grid-row:2;place-self:center end}div:where(.swal2-container).swal2-bottom-start>.swal2-popup,div:where(.swal2-container).swal2-bottom-left>.swal2-popup{grid-column:1;grid-row:3;align-self:end}div:where(.swal2-container).swal2-bottom>.swal2-popup{grid-column:2;grid-row:3;place-self:end center}div:where(.swal2-container).swal2-bottom-end>.swal2-popup,div:where(.swal2-container).swal2-bottom-right>.swal2-popup{grid-column:3;grid-row:3;place-self:end end}div:where(.swal2-container).swal2-grow-row>.swal2-popup,div:where(.swal2-container).swal2-grow-fullscreen>.swal2-popup{grid-column:1/4;width:100%}div:where(.swal2-container).swal2-grow-column>.swal2-popup,div:where(.swal2-container).swal2-grow-fullscreen>.swal2-popup{grid-row:1/4;align-self:stretch}div:where(.swal2-container).swal2-no-transition{transition:none !important}div:where(.swal2-container)[popover]{width:auto;border:0}div:where(.swal2-container) div:where(.swal2-popup){display:none;position:relative;box-sizing:border-box;grid-template-columns:minmax(0, 100%);width:var(--swal2-width);max-width:100%;padding:var(--swal2-padding);border:var(--swal2-border);border-radius:var(--swal2-border-radius);background:var(--swal2-background);color:var(--swal2-color);font-family:inherit;font-size:1rem;container-name:swal2-popup}div:where(.swal2-container) div:where(.swal2-popup):focus{outline:none}div:where(.swal2-container) div:where(.swal2-popup).swal2-loading{overflow-y:hidden}div:where(.swal2-container) div:where(.swal2-popup).swal2-draggable{cursor:grab}div:where(.swal2-container) div:where(.swal2-popup).swal2-draggable div:where(.swal2-icon){cursor:grab}div:where(.swal2-container) div:where(.swal2-popup).swal2-dragging{cursor:grabbing}div:where(.swal2-container) div:where(.swal2-popup).swal2-dragging div:where(.swal2-icon){cursor:grabbing}div:where(.swal2-container) h2:where(.swal2-title){position:relative;max-width:100%;margin:0;padding:var(--swal2-title-padding);color:inherit;font-size:1.875em;font-weight:600;text-align:center;text-transform:none;word-wrap:break-word;cursor:initial}div:where(.swal2-container) div:where(.swal2-actions){display:flex;z-index:1;box-sizing:border-box;flex-wrap:wrap;align-items:center;justify-content:center;width:auto;margin:1.25em auto 0;padding:0}div:where(.swal2-container) div:where(.swal2-loader){display:none;align-items:center;justify-content:center;width:2.2em;height:2.2em;margin:0 1.875em;animation:swal2-rotate-loading 1.5s linear 0s infinite normal;border-width:.25em;border-style:solid;border-radius:100%;border-color:#2778c4 rgba(0,0,0,0) #2778c4 rgba(0,0,0,0)}div:where(.swal2-container) button:where(.swal2-styled){margin:.3125em;padding:.625em 1.1em;transition:var(--swal2-button-transition);box-shadow:0 0 0 3px rgba(0,0,0,0);font-weight:500}div:where(.swal2-container) button:where(.swal2-styled):not([disabled]){cursor:pointer}div:where(.swal2-container) button:where(.swal2-styled):where(.swal2-confirm){border:var(--swal2-confirm-button-border);border-radius:var(--swal2-confirm-button-border-radius);background:initial;background-color:var(--swal2-confirm-button-background-color);color:var(--swal2-confirm-button-color);font-size:1em}div:where(.swal2-container) button:where(.swal2-styled):where(.swal2-confirm):hover{background-color:color-mix(in srgb, var(--swal2-confirm-button-background-color), black 10%)}div:where(.swal2-container) button:where(.swal2-styled):where(.swal2-confirm):active{background-color:color-mix(in srgb, var(--swal2-confirm-button-background-color), black 20%)}div:where(.swal2-container) button:where(.swal2-styled):where(.swal2-deny){border:var(--swal2-deny-button-border);border-radius:var(--swal2-deny-button-border-radius);background:initial;background-color:var(--swal2-deny-button-background-color);color:var(--swal2-deny-button-color);font-size:1em}div:where(.swal2-container) button:where(.swal2-styled):where(.swal2-deny):hover{background-color:color-mix(in srgb, var(--swal2-deny-button-background-color), black 10%)}div:where(.swal2-container) button:where(.swal2-styled):where(.swal2-deny):active{background-color:color-mix(in srgb, var(--swal2-deny-button-background-color), black 20%)}div:where(.swal2-container) button:where(.swal2-styled):where(.swal2-cancel){border:var(--swal2-cancel-button-border);border-radius:var(--swal2-cancel-button-border-radius);background:initial;background-color:var(--swal2-cancel-button-background-color);color:var(--swal2-cancel-button-color);font-size:1em}div:where(.swal2-container) button:where(.swal2-styled):where(.swal2-cancel):hover{background-color:color-mix(in srgb, var(--swal2-cancel-button-background-color), black 10%)}div:where(.swal2-container) button:where(.swal2-styled):where(.swal2-cancel):active{background-color:color-mix(in srgb, var(--swal2-cancel-button-background-color), black 20%)}div:where(.swal2-container) button:where(.swal2-styled):focus-visible{outline:none;box-shadow:var(--swal2-action-button-outline)}div:where(.swal2-container) button:where(.swal2-styled)[disabled]:not(.swal2-loading){opacity:.4}div:where(.swal2-container) button:where(.swal2-styled)::-moz-focus-inner{border:0}div:where(.swal2-container) div:where(.swal2-footer){margin:1em 0 0;padding:1em 1em 0;border-top:1px solid var(--swal2-footer-border-color);color:inherit;font-size:1em;text-align:center;cursor:initial}div:where(.swal2-container) .swal2-timer-progress-bar-container{position:absolute;right:0;bottom:0;left:0;grid-column:auto !important;overflow:hidden;border-bottom-right-radius:var(--swal2-border-radius);border-bottom-left-radius:var(--swal2-border-radius)}div:where(.swal2-container) div:where(.swal2-timer-progress-bar){width:100%;height:.25em;background:rgba(0,0,0,.2)}div:where(.swal2-container) img:where(.swal2-image){max-width:100%;margin:2em auto 1em;cursor:initial}div:where(.swal2-container) button:where(.swal2-close){position:var(--swal2-close-button-position);inset:var(--swal2-close-button-inset);z-index:2;align-items:center;justify-content:center;width:1.2em;height:1.2em;margin-top:0;margin-right:0;margin-bottom:-1.2em;padding:0;overflow:hidden;transition:var(--swal2-close-button-transition);border:none;border-radius:var(--swal2-border-radius);outline:var(--swal2-close-button-outline);background:rgba(0,0,0,0);color:var(--swal2-close-button-color);font-family:monospace;font-size:var(--swal2-close-button-font-size);cursor:pointer;justify-self:end}div:where(.swal2-container) button:where(.swal2-close):hover{transform:var(--swal2-close-button-hover-transform);background:rgba(0,0,0,0);color:#f27474}div:where(.swal2-container) button:where(.swal2-close):focus-visible{outline:none;box-shadow:var(--swal2-close-button-focus-box-shadow)}div:where(.swal2-container) button:where(.swal2-close)::-moz-focus-inner{border:0}div:where(.swal2-container) div:where(.swal2-html-container){z-index:1;justify-content:center;margin:0;padding:var(--swal2-html-container-padding);overflow:auto;color:inherit;font-size:1.125em;font-weight:normal;line-height:normal;text-align:center;word-wrap:break-word;word-break:break-word;cursor:initial}div:where(.swal2-container) input:where(.swal2-input),div:where(.swal2-container) input:where(.swal2-file),div:where(.swal2-container) textarea:where(.swal2-textarea),div:where(.swal2-container) select:where(.swal2-select),div:where(.swal2-container) div:where(.swal2-radio),div:where(.swal2-container) label:where(.swal2-checkbox){margin:1em 2em 3px}div:where(.swal2-container) input:where(.swal2-input),div:where(.swal2-container) input:where(.swal2-file),div:where(.swal2-container) textarea:where(.swal2-textarea){box-sizing:border-box;width:auto;transition:var(--swal2-input-transition);border:1px solid #d9d9d9;border-radius:var(--swal2-input-border-radius);background:var(--swal2-input-background);box-shadow:var(--swal2-input-box-shadow);color:inherit;font-size:1.125em}div:where(.swal2-container) input:where(.swal2-input).swal2-inputerror,div:where(.swal2-container) input:where(.swal2-file).swal2-inputerror,div:where(.swal2-container) textarea:where(.swal2-textarea).swal2-inputerror{border-color:#f27474 !important;box-shadow:0 0 2px #f27474 !important}div:where(.swal2-container) input:where(.swal2-input):focus,div:where(.swal2-container) input:where(.swal2-file):focus,div:where(.swal2-container) textarea:where(.swal2-textarea):focus{border:1px solid #b4dbed;outline:none;box-shadow:inset 0 1px 1px rgba(0,0,0,.06),0 0 0 3px rgba(100,150,200,.5)}div:where(.swal2-container) input:where(.swal2-input)::placeholder,div:where(.swal2-container) input:where(.swal2-file)::placeholder,div:where(.swal2-container) textarea:where(.swal2-textarea)::placeholder{color:#ccc}div:where(.swal2-container) .swal2-range{margin:1em 2em 3px;background:var(--swal2-background)}div:where(.swal2-container) .swal2-range input{width:80%}div:where(.swal2-container) .swal2-range output{width:20%;color:inherit;font-weight:600;text-align:center}div:where(.swal2-container) .swal2-range input,div:where(.swal2-container) .swal2-range output{height:2.625em;padding:0;font-size:1.125em;line-height:2.625em}div:where(.swal2-container) .swal2-input{height:2.625em;padding:0 .75em}div:where(.swal2-container) .swal2-file{width:75%;margin-right:auto;margin-left:auto;background:var(--swal2-input-background);font-size:1.125em}div:where(.swal2-container) .swal2-textarea{height:6.75em;padding:.75em}div:where(.swal2-container) .swal2-select{min-width:50%;max-width:100%;padding:.375em .625em;background:var(--swal2-input-background);color:inherit;font-size:1.125em}div:where(.swal2-container) .swal2-radio,div:where(.swal2-container) .swal2-checkbox{align-items:center;justify-content:center;background:var(--swal2-background);color:inherit}div:where(.swal2-container) .swal2-radio label,div:where(.swal2-container) .swal2-checkbox label{margin:0 .6em;font-size:1.125em}div:where(.swal2-container) .swal2-radio input,div:where(.swal2-container) .swal2-checkbox input{flex-shrink:0;margin:0 .4em}div:where(.swal2-container) label:where(.swal2-input-label){display:flex;justify-content:center;margin:1em auto 0}div:where(.swal2-container) div:where(.swal2-validation-message){align-items:center;justify-content:center;margin:1em 0 0;padding:.625em;overflow:hidden;background:var(--swal2-validation-message-background);color:var(--swal2-validation-message-color);font-size:1em;font-weight:300}div:where(.swal2-container) div:where(.swal2-validation-message)::before{content:"!";display:inline-block;width:1.5em;min-width:1.5em;height:1.5em;margin:0 .625em;border-radius:50%;background-color:#f27474;color:#fff;font-weight:600;line-height:1.5em;text-align:center}div:where(.swal2-container) .swal2-progress-steps{flex-wrap:wrap;align-items:center;max-width:100%;margin:1.25em auto;padding:0;background:rgba(0,0,0,0);font-weight:600}div:where(.swal2-container) .swal2-progress-steps li{display:inline-block;position:relative}div:where(.swal2-container) .swal2-progress-steps .swal2-progress-step{z-index:20;flex-shrink:0;width:2em;height:2em;border-radius:2em;background:#2778c4;color:#fff;line-height:2em;text-align:center}div:where(.swal2-container) .swal2-progress-steps .swal2-progress-step.swal2-active-progress-step{background:#2778c4}div:where(.swal2-container) .swal2-progress-steps .swal2-progress-step.swal2-active-progress-step~.swal2-progress-step{background:var(--swal2-progress-step-background);color:#fff}div:where(.swal2-container) .swal2-progress-steps .swal2-progress-step.swal2-active-progress-step~.swal2-progress-step-line{background:var(--swal2-progress-step-background)}div:where(.swal2-container) .swal2-progress-steps .swal2-progress-step-line{z-index:10;flex-shrink:0;width:2.5em;height:.4em;margin:0 -1px;background:#2778c4}div:where(.swal2-icon){position:relative;box-sizing:content-box;justify-content:center;width:5em;height:5em;margin:2.5em auto .6em;zoom:var(--swal2-icon-zoom);border:.25em solid rgba(0,0,0,0);border-radius:50%;border-color:#000;font-family:inherit;line-height:5em;cursor:default;user-select:none}div:where(.swal2-icon) .swal2-icon-content{display:flex;align-items:center;font-size:3.75em}div:where(.swal2-icon).swal2-error{border-color:#f27474;color:#f27474}div:where(.swal2-icon).swal2-error .swal2-x-mark{position:relative;flex-grow:1}div:where(.swal2-icon).swal2-error [class^=swal2-x-mark-line]{display:block;position:absolute;top:2.3125em;width:2.9375em;height:.3125em;border-radius:.125em;background-color:#f27474}div:where(.swal2-icon).swal2-error [class^=swal2-x-mark-line][class$=left]{left:1.0625em;transform:rotate(45deg)}div:where(.swal2-icon).swal2-error [class^=swal2-x-mark-line][class$=right]{right:1em;transform:rotate(-45deg)}@container swal2-popup style(--swal2-icon-animations:true){div:where(.swal2-icon).swal2-error.swal2-icon-show{animation:swal2-animate-error-icon .5s}div:where(.swal2-icon).swal2-error.swal2-icon-show .swal2-x-mark{animation:swal2-animate-error-x-mark .5s}}div:where(.swal2-icon).swal2-warning{border-color:#f8bb86;color:#f8bb86}@container swal2-popup style(--swal2-icon-animations:true){div:where(.swal2-icon).swal2-warning.swal2-icon-show{animation:swal2-animate-error-icon .5s}div:where(.swal2-icon).swal2-warning.swal2-icon-show .swal2-icon-content{animation:swal2-animate-i-mark .5s}}div:where(.swal2-icon).swal2-info{border-color:#3fc3ee;color:#3fc3ee}@container swal2-popup style(--swal2-icon-animations:true){div:where(.swal2-icon).swal2-info.swal2-icon-show{animation:swal2-animate-error-icon .5s}div:where(.swal2-icon).swal2-info.swal2-icon-show .swal2-icon-content{animation:swal2-animate-i-mark .8s}}div:where(.swal2-icon).swal2-question{border-color:#87adbd;color:#87adbd}@container swal2-popup style(--swal2-icon-animations:true){div:where(.swal2-icon).swal2-question.swal2-icon-show{animation:swal2-animate-error-icon .5s}div:where(.swal2-icon).swal2-question.swal2-icon-show .swal2-icon-content{animation:swal2-animate-question-mark .8s}}div:where(.swal2-icon).swal2-success{border-color:#a5dc86;color:#a5dc86}div:where(.swal2-icon).swal2-success [class^=swal2-success-circular-line]{position:absolute;width:3.75em;height:7.5em;border-radius:50%}div:where(.swal2-icon).swal2-success [class^=swal2-success-circular-line][class$=left]{top:-0.4375em;left:-2.0635em;transform:rotate(-45deg);transform-origin:3.75em 3.75em;border-radius:7.5em 0 0 7.5em}div:where(.swal2-icon).swal2-success [class^=swal2-success-circular-line][class$=right]{top:-0.6875em;left:1.875em;transform:rotate(-45deg);transform-origin:0 3.75em;border-radius:0 7.5em 7.5em 0}div:where(.swal2-icon).swal2-success .swal2-success-ring{position:absolute;z-index:2;top:-0.25em;left:-0.25em;box-sizing:content-box;width:100%;height:100%;border:.25em solid rgba(165,220,134,.3);border-radius:50%}div:where(.swal2-icon).swal2-success .swal2-success-fix{position:absolute;z-index:1;top:.5em;left:1.625em;width:.4375em;height:5.625em;transform:rotate(-45deg)}div:where(.swal2-icon).swal2-success [class^=swal2-success-line]{display:block;position:absolute;z-index:2;height:.3125em;border-radius:.125em;background-color:#a5dc86}div:where(.swal2-icon).swal2-success [class^=swal2-success-line][class$=tip]{top:2.875em;left:.8125em;width:1.5625em;transform:rotate(45deg)}div:where(.swal2-icon).swal2-success [class^=swal2-success-line][class$=long]{top:2.375em;right:.5em;width:2.9375em;transform:rotate(-45deg)}@container swal2-popup style(--swal2-icon-animations:true){div:where(.swal2-icon).swal2-success.swal2-icon-show .swal2-success-line-tip{animation:swal2-animate-success-line-tip .75s}div:where(.swal2-icon).swal2-success.swal2-icon-show .swal2-success-line-long{animation:swal2-animate-success-line-long .75s}div:where(.swal2-icon).swal2-success.swal2-icon-show .swal2-success-circular-line-right{animation:swal2-rotate-success-circular-line 4.25s ease-in}}[class^=swal2]{-webkit-tap-highlight-color:rgba(0,0,0,0)}.swal2-show{animation:var(--swal2-show-animation)}.swal2-hide{animation:var(--swal2-hide-animation)}.swal2-noanimation{transition:none}.swal2-scrollbar-measure{position:absolute;top:-9999px;width:50px;height:50px;overflow:scroll}.swal2-rtl .swal2-close{margin-right:initial;margin-left:0}.swal2-rtl .swal2-timer-progress-bar{right:0;left:auto}.swal2-toast{box-sizing:border-box;grid-column:1/4 !important;grid-row:1/4 !important;grid-template-columns:min-content auto min-content;padding:1em;overflow-y:hidden;border:var(--swal2-toast-border);background:var(--swal2-background);box-shadow:var(--swal2-toast-box-shadow);pointer-events:all}.swal2-toast>*{grid-column:2}.swal2-toast h2:where(.swal2-title){margin:.5em 1em;padding:0;font-size:1em;text-align:initial}.swal2-toast .swal2-loading{justify-content:center}.swal2-toast input:where(.swal2-input){height:2em;margin:.5em;font-size:1em}.swal2-toast .swal2-validation-message{font-size:1em}.swal2-toast div:where(.swal2-footer){margin:.5em 0 0;padding:.5em 0 0;font-size:.8em}.swal2-toast button:where(.swal2-close){grid-column:3/3;grid-row:1/99;align-self:center;width:.8em;height:.8em;margin:0;font-size:2em}.swal2-toast div:where(.swal2-html-container){margin:.5em 1em;padding:0;overflow:initial;font-size:1em;text-align:initial}.swal2-toast div:where(.swal2-html-container):empty{padding:0}.swal2-toast .swal2-loader{grid-column:1;grid-row:1/99;align-self:center;width:2em;height:2em;margin:.25em}.swal2-toast .swal2-icon{grid-column:1;grid-row:1/99;align-self:center;width:2em;min-width:2em;height:2em;margin:0 .5em 0 0}.swal2-toast .swal2-icon .swal2-icon-content{display:flex;align-items:center;font-size:1.8em;font-weight:bold}.swal2-toast .swal2-icon.swal2-success .swal2-success-ring{width:2em;height:2em}.swal2-toast .swal2-icon.swal2-error [class^=swal2-x-mark-line]{top:.875em;width:1.375em}.swal2-toast .swal2-icon.swal2-error [class^=swal2-x-mark-line][class$=left]{left:.3125em}.swal2-toast .swal2-icon.swal2-error [class^=swal2-x-mark-line][class$=right]{right:.3125em}.swal2-toast div:where(.swal2-actions){justify-content:flex-start;height:auto;margin:0;margin-top:.5em;padding:0 .5em}.swal2-toast button:where(.swal2-styled){margin:.25em .5em;padding:.4em .6em;font-size:1em}.swal2-toast .swal2-success{border-color:#a5dc86}.swal2-toast .swal2-success [class^=swal2-success-circular-line]{position:absolute;width:1.6em;height:3em;border-radius:50%}.swal2-toast .swal2-success [class^=swal2-success-circular-line][class$=left]{top:-0.8em;left:-0.5em;transform:rotate(-45deg);transform-origin:2em 2em;border-radius:4em 0 0 4em}.swal2-toast .swal2-success [class^=swal2-success-circular-line][class$=right]{top:-0.25em;left:.9375em;transform-origin:0 1.5em;border-radius:0 4em 4em 0}.swal2-toast .swal2-success .swal2-success-ring{width:2em;height:2em}.swal2-toast .swal2-success .swal2-success-fix{top:0;left:.4375em;width:.4375em;height:2.6875em}.swal2-toast .swal2-success [class^=swal2-success-line]{height:.3125em}.swal2-toast .swal2-success [class^=swal2-success-line][class$=tip]{top:1.125em;left:.1875em;width:.75em}.swal2-toast .swal2-success [class^=swal2-success-line][class$=long]{top:.9375em;right:.1875em;width:1.375em}@container swal2-popup style(--swal2-icon-animations:true){.swal2-toast .swal2-success.swal2-icon-show .swal2-success-line-tip{animation:swal2-toast-animate-success-line-tip .75s}.swal2-toast .swal2-success.swal2-icon-show .swal2-success-line-long{animation:swal2-toast-animate-success-line-long .75s}}.swal2-toast.swal2-show{animation:var(--swal2-toast-show-animation)}.swal2-toast.swal2-hide{animation:var(--swal2-toast-hide-animation)}@keyframes swal2-show{0%{transform:scale(0.7)}45%{transform:scale(1.05)}80%{transform:scale(0.95)}100%{transform:scale(1)}}@keyframes swal2-hide{0%{transform:scale(1);opacity:1}100%{transform:scale(0.5);opacity:0}}@keyframes swal2-animate-success-line-tip{0%{top:1.1875em;left:.0625em;width:0}54%{top:1.0625em;left:.125em;width:0}70%{top:2.1875em;left:-0.375em;width:3.125em}84%{top:3em;left:1.3125em;width:1.0625em}100%{top:2.8125em;left:.8125em;width:1.5625em}}@keyframes swal2-animate-success-line-long{0%{top:3.375em;right:2.875em;width:0}65%{top:3.375em;right:2.875em;width:0}84%{top:2.1875em;right:0;width:3.4375em}100%{top:2.375em;right:.5em;width:2.9375em}}@keyframes swal2-rotate-success-circular-line{0%{transform:rotate(-45deg)}5%{transform:rotate(-45deg)}12%{transform:rotate(-405deg)}100%{transform:rotate(-405deg)}}@keyframes swal2-animate-error-x-mark{0%{margin-top:1.625em;transform:scale(0.4);opacity:0}50%{margin-top:1.625em;transform:scale(0.4);opacity:0}80%{margin-top:-0.375em;transform:scale(1.15)}100%{margin-top:0;transform:scale(1);opacity:1}}@keyframes swal2-animate-error-icon{0%{transform:rotateX(100deg);opacity:0}100%{transform:rotateX(0deg);opacity:1}}@keyframes swal2-rotate-loading{0%{transform:rotate(0deg)}100%{transform:rotate(360deg)}}@keyframes swal2-animate-question-mark{0%{transform:rotateY(-360deg)}100%{transform:rotateY(0)}}@keyframes swal2-animate-i-mark{0%{transform:rotateZ(45deg);opacity:0}25%{transform:rotateZ(-25deg);opacity:.4}50%{transform:rotateZ(15deg);opacity:.8}75%{transform:rotateZ(-5deg);opacity:1}100%{transform:rotateX(0);opacity:1}}@keyframes swal2-toast-show{0%{transform:translateY(-0.625em) rotateZ(2deg)}33%{transform:translateY(0) rotateZ(-2deg)}66%{transform:translateY(0.3125em) rotateZ(2deg)}100%{transform:translateY(0) rotateZ(0deg)}}@keyframes swal2-toast-hide{100%{transform:rotateZ(1deg);opacity:0}}@keyframes swal2-toast-animate-success-line-tip{0%{top:.5625em;left:.0625em;width:0}54%{top:.125em;left:.125em;width:0}70%{top:.625em;left:-0.25em;width:1.625em}84%{top:1.0625em;left:.75em;width:.5em}100%{top:1.125em;left:.1875em;width:.75em}}@keyframes swal2-toast-animate-success-line-long{0%{top:1.625em;right:1.375em;width:0}65%{top:1.25em;right:.9375em;width:0}84%{top:.9375em;right:0;width:1.125em}100%{top:.9375em;right:.1875em;width:1.375em}}');export{be as S};
