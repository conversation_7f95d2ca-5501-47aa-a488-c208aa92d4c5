import{a as g,d as _,b as O,u as C,_ as k}from"./index-HUlFBKIW.js";import{g as y,u as v,a as L}from"./PermissionsService-Dhc9PbZp.js";const E=(e,t)=>{if(!(e!=null&&e.id))throw new Error("بيانات القضية غير متوفرة أو معرف القضية مفقود");if(!(t!=null&&t.uid))throw new Error("بيانات المستخدم غير متوفرة أو المستخدم غير مسجل الدخول");return!0},m=e=>!e||!Array.isArray(e)?[]:e.filter(t=>t.type==="completed_action"||t.type==="completed_deferral"||t.type==="case_created"||t.type==="case_updated"||t.type==="expert_referral"||t.type==="status_transfer"||t.type==="degree_transfer"||t.type==="report_created"||t.type==="lawsuit_created"),J=async(e,t,l,n,f,u,h)=>{if(!n||!n.id){console.error("❌ خطأ: بيانات القضية غير متوفرة في handleAddAction"),alert("خطأ: بيانات القضية غير متوفرة");return}if(!g.currentUser||!g.currentUser.uid){console.error("❌ خطأ: بيانات المستخدم غير متوفرة في handleAddAction"),alert("خطأ: المستخدم غير مسجل الدخول");return}if(!e||!t||!l){alert("يرجى إدخال جميع بيانات الإجراء.");return}const d=n.id,r=g.currentUser.uid,p={id:`${d}-action-${Date.now()}`,description:e,deadline:t,reminderType:l,timestamp:new Date().toLocaleString(),isDeleted:!1,struckAt:null,linkedDeferralId:"",linkedActionId:"",userId:r,createdAt:new Date().toISOString()};try{const c=y();let i=[...n.actions||[]];if(i.push(p),c==="online"){const o=_(O,"cases",d);await C(o,{actions:i,updatedAt:new Date().toISOString()})}else{const o={...n,actions:i,updatedAt:new Date().toISOString()};await L(r,d,o)}u(i);const{notifyTaskCreated:a}=await k(async()=>{const{notifyTaskCreated:o}=await import("./CacheManager-c0VPvbsl.js");return{notifyTaskCreated:o}},[],import.meta.url);a(r),alert("تم إضافة التذكير بنجاح")}catch(c){console.error("خطأ في إضافة التذكير:",c),alert("فشل في إضافة التذكير. حاول مرة أخرى.")}},R=e=>{if(console.log("🔍 فحص نوع التأجيلة:",e),!e||!e.reasons)return console.log("❌ لا توجد أسباب للتأجيلة"),!1;const t=e.reasons.some(l=>l&&(l.includes("للحكم")||l.includes("حكم")||l.includes("قرار")));return console.log("🎯 هل هي جلسة حكم؟",t),console.log("📋 أسباب التأجيلة:",e.reasons),t},T=async(e,t,l,n,f,u)=>{var r,p;if(console.log("🚀 بدء handleCompleteDeferral مع المعاملات:",{deferralIndex:e,deferralsLength:t==null?void 0:t.length,caseId:n==null?void 0:n.id,hasOnJudgmentDetected:!!u}),!n||!n.id){console.error("❌ خطأ: بيانات القضية غير متوفرة في handleCompleteDeferral"),alert("خطأ: بيانات القضية غير متوفرة");return}if(!g.currentUser||!g.currentUser.uid){console.error("❌ خطأ: بيانات المستخدم غير متوفرة في handleCompleteDeferral"),alert("خطأ: المستخدم غير مسجل الدخول");return}const h=n.id,d=g.currentUser.uid;try{const c=y();let i=[...t],a=[...n.history||[]];if(console.log("🔍 محاولة العثور على التأجيلة بالفهرس:",e),console.log("📊 إجمالي التأجيلات المتاحة:",i.length),console.log("📋 قائمة التأجيلات:",i.map((o,s)=>({index:s,date:o.date,reasons:o.reasons}))),e>=0&&e<i.length){const o=i[e];console.log("✅ تم العثور على التأجيلة بنجاح"),console.log("📋 التأجيلة المكتملة:",JSON.stringify(o,null,2));const s=R(o);if(console.log("⚖️ هل هي جلسة حكم؟",s),console.log("🔧 دالة معالجة منطوق الأحكام متوفرة؟",!!u),s&&u)return console.log("🎯 تفعيل نافذة منطوق الأحكام..."),u({deferralIndex:e,deferrals:i,setDeferrals:l,caseItem:n,setHistory:f,originalDate:o.date}),console.log("✅ تم استدعاء onJudgmentDetected - إيقاف التنفيذ"),"judgment_detected";console.log("⏭️ متابعة العملية العادية (ليست جلسة حكم أو لا توجد دالة معالجة)");let D=`تم حضور جلسة: ${((r=o.reasons)==null?void 0:r.join("، "))||"غير محدد"}`;o.description&&o.description.trim()&&(D+=` - ${o.description}`);const A={deferralId:e,timestamp:new Date().toLocaleString(),action:D,type:"completed_deferral",userId:d,description:((p=o.reasons)==null?void 0:p.join("، "))||"غير محدد",deferralDescription:o.description||"",completedAt:o.date,originalDate:o.date,attendanceDate:new Date().toISOString()};return a.push(A),i.splice(e,1),l&&l(i),f&&f(a),await v(d,h,{deferrals:i,history:a}),console.log("✅ تم إكمال التأجيلة وتحديث قاعدة البيانات"),"completed"}else throw console.error("❌ لم يتم العثور على التأجيلة في القائمة"),console.error("📊 الفهرس المطلوب:",e,"طول القائمة:",i.length),new Error("لم يتم العثور على التأجيلة المطلوبة في قائمة المهام")}catch(c){throw console.error("خطأ في تسجيل إكمال التأجيلة:",c),c}},P=async(e,t,l,n,f)=>{if(!n||!n.id){console.error("❌ خطأ: بيانات القضية غير متوفرة في handleCompleteAction"),alert("خطأ: بيانات القضية غير متوفرة");return}if(!g.currentUser||!g.currentUser.uid){console.error("❌ خطأ: بيانات المستخدم غير متوفرة في handleCompleteAction"),alert("خطأ: المستخدم غير مسجل الدخول");return}const u=n.id,h=g.currentUser.uid;try{const d=y();let r=[...n.actions||[]],p=[...n.deferrals||[]],c=[...n.history||[]];console.log("🔍 البحث عن الإجراء بالمعرف:",e),console.log("📋 قائمة الإجراءات المتاحة:",r.map(a=>({id:a.id,description:a.description})));let i=r.findIndex(a=>a.id===e);if(i===-1&&(console.log("⚠️ لم يتم العثور على الإجراء بالمعرف، البحث بطريقة بديلة..."),r.length>0&&(i=r.length-1,console.log("🎯 استخدام آخر إجراء في القائمة، الفهرس:",i))),i!==-1){const a=r[i],o={actionId:e,timestamp:new Date().toLocaleString(),action:`تم تنفيذ إجراء: "${a.description}"`,type:"completed_action",userId:h,description:a.description,completedAt:new Date().toISOString(),originalDeadline:a.deadline};if(c.push(o),r.splice(i,1),l&&l(r),f&&f(c),d==="online"){const s=_(O,"cases",u);await C(s,{deferrals:p,actions:r,history:c,updatedAt:new Date().toISOString()})}else{const s={...n,deferrals:p,actions:r,history:c,updatedAt:new Date().toISOString()};await v(h,u,s)}console.log("✅ تم إكمال الإجراء وتحديث قاعدة البيانات")}else throw console.error("❌ لم يتم العثور على الإجراء في القائمة"),new Error("لم يتم العثور على الإجراء المطلوب في قائمة المهام")}catch(d){throw console.error("خطأ في تسجيل إكمال الإجراء:",d),d}},x=async(e,t,l,n,f)=>{E(n,g.currentUser);const u=n.id,h=g.currentUser.uid;try{const d=y();let r=[...t],p=[...n.deferrals||[]],c=[...n.history||[]];const i=r.findIndex(a=>a.id===e);if(i!==-1){const a=r[i];console.log("🗑️ حذف الإجراء:",a);const o={timestamp:new Date().toLocaleString(),action:`تم حذف إجراء: "${a.description}"`,type:"action_deleted",userId:h,description:a.description,deletedAt:new Date().toISOString(),originalDeadline:a.deadline};if(c.push(o),r.splice(i,1),l&&l(r),f&&f(c),d==="online"){const s=_(O,"cases",u);await C(s,{deferrals:p,actions:r,history:c,updatedAt:new Date().toISOString()})}else{const s={...n,deferrals:p,actions:r,history:c,updatedAt:new Date().toISOString()};await v(h,u,s)}console.log("✅ تم حذف الإجراء وتحديث قاعدة البيانات")}}catch(d){alert("خطأ في شطب الإجراء: "+d.message)}},z=async(e,t,l,n,f,u,h,d)=>{var i,a;const r=t.findIndex((o,s)=>s===e);t[r],E(u,g.currentUser);const p=u.id,c=g.currentUser.uid;try{const o=y();let s=[...t],D=[...h||[]],A=[...n||[]];if(r>=0&&r<s.length){const w=s[r];console.log("🗑️ حذف التأجيلة:",w);const U={timestamp:new Date().toLocaleString(),action:`تم حذف تأجيل: ${((i=w.reasons)==null?void 0:i.join("، "))||w.content||"غير محدد"}`,type:"deferral_deleted",userId:c,description:((a=w.reasons)==null?void 0:a.join("، "))||w.content||"غير محدد",deletedAt:new Date().toISOString(),originalDate:w.date};A.push(U),s.splice(r,1);const $=w.id||`${p}-defer-${r}`;if(D=D.filter(S=>S.linkedDeferralId===$?(console.log("🔗 حذف إجراء مرتبط:",S.description),!1):!0),l&&l(s),d&&d(D),f&&f(A),o==="online"){const S=_(O,"cases",p);await C(S,{deferrals:s,actions:D,history:A,updatedAt:new Date().toISOString()})}else{const S={...u,deferrals:s,actions:D,history:A,updatedAt:new Date().toISOString()};await v(c,p,S)}console.log("✅ تم حذف التأجيلة وتحديث قاعدة البيانات"),alert("تم حذف التأجيلة بنجاح")}else throw new Error("فهرس التأجيلة غير صحيح")}catch(o){throw console.error("خطأ في حذف التأجيلة:",o),alert("خطأ في حذف التأجيلة: "+o.message),o}};export{T as a,z as b,m as c,x as d,J as e,P as h};
