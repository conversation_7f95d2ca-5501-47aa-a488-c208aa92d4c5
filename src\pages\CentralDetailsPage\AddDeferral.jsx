import { useState, useEffect, useMemo } from 'react';
import { FaSave, FaTimes, FaPlus } from 'react-icons/fa';
import Swal from 'sweetalert2';
import styles from "./AddDeferralAction.module.css";
import { db } from '../../config/firebaseConfig';
import { collection, getDocs, query, where, addDoc } from 'firebase/firestore';

const DEFERRAL_CATEGORIES = {
  JUDGMENT: 'للحكم', ANNOUNCEMENT: 'للإعلان', MEMORANDUMS: 'للمذكرات',
  REPORT: 'للتقرير', DOCUMENTS: 'للمستندات', REPORT_RECEIVED: 'لورود التقرير', OTHER: 'أخرى',
};
const DEFAULT_TEMPLATES = [
    { category: DEFERRAL_CATEGORIES.JUDGMENT, actions: [{ description: 'مراجعة الأوراق والمستندات', linkage: { type: 'تسلسلي' } }, { description: 'إعداد المرافعة النهائية', linkage: { type: 'تسلسلي' } }] },
    { category: DEFERRAL_CATEGORIES.ANNOUNCEMENT, actions: [{ description: 'متابعة إجراءات الإعلان', linkage: { type: 'مباشر' } }] },
    { category: DEFERRAL_CATEGORIES.MEMORANDUMS, actions: [{ description: 'إعداد المذكرة القانونية', linkage: { type: 'تسلسلي' } }, { description: 'مراجعة وتدقيق المذكرة', linkage: { type: 'تسلسلي' } }] },
    { category: DEFERRAL_CATEGORIES.REPORT, actions: [{ description: 'متابعة إعداد التقرير', linkage: { type: 'مباشر' } }] },
    { category: DEFERRAL_CATEGORIES.DOCUMENTS, actions: [{ description: 'جمع المستندات المطلوبة', linkage: { type: 'تسلسلي' } }, { description: 'مراجعة وتنظيم المستندات', linkage: { type: 'تسلسلي' } }] },
    { category: DEFERRAL_CATEGORIES.REPORT_RECEIVED, actions: [{ description: 'مراجعة التقرير الوارد', linkage: { type: 'تسلسلي' } }, { description: 'إعداد الرد على التقرير', linkage: { type: 'تسلسلي' } }] },
    { category: DEFERRAL_CATEGORIES.OTHER, actions: [{ description: 'متابعة الإجراءات المطلوبة', linkage: { type: 'مباشر' } }] }
];


const AddDeferral = ({ currentUser, onSave, onClose }) => {
  const [reportDate, setReportDate] = useState(new Date().toISOString().split('T')[0]);
  const [selectedReasons, setSelectedReasons] = useState([]);
  const [deferralDescription, setDeferralDescription] = useState('');
  const [error, setError] = useState(null);
  const [templates, setTemplates] = useState([]);
  const [loadingTemplates, setLoadingTemplates] = useState(false);

  useEffect(() => {
    const fetchTemplates = async () => {
      if (!currentUser?.uid) {
        setError('لا يوجد مستخدم مسجل الدخول.');
        return;
      }
      setLoadingTemplates(true);
      try {
        const templatesRef = collection(db, 'deferralTemplates');
        const q = query(templatesRef, where('userId', '==', currentUser.uid));
        const querySnapshot = await getDocs(q);
        const userTemplates = querySnapshot.docs.map(doc => ({ id: doc.id, ...doc.data() }));
        
        const allTemplates = [
          ...DEFAULT_TEMPLATES.filter(def => !userTemplates.some(u => (u.category || u.reason) === def.category)),
          ...userTemplates
        ];
        setTemplates(allTemplates);
      } catch (e) {
        setError('خطأ في جلب القوالب.');
      } finally {
        setLoadingTemplates(false);
      }
    };

    fetchTemplates();
  }, [currentUser]);

  const toggleReasonSelection = (reason) => {
    setSelectedReasons((prev) =>
      prev.includes(reason)
        ? prev.filter((r) => r !== reason)
        : [...prev, reason]
    );
  };

  const handleSaveClick = () => {
    if (!reportDate || selectedReasons.length === 0) {
      setError('يرجى ملء تاريخ التأجيلة وسبب واحد على الأقل.');
      return;
    }
    onSave('deferral', { reportDate, selectedReasons, deferralDescription });
  };

  const handleAddCustomReason = async () => {
    // ... (الكود الأصلي لهذه الدالة يبقى كما هو)
  };

  const memoizedReasonButtons = useMemo(() => {
    return templates
      .filter(template => (template.category || template.reason) !== DEFERRAL_CATEGORIES.OTHER)
      .map((template) => {
        const reason = template.category || template.reason;
        return (
          <button
            key={template.id || reason}
            className={`${styles.reasonButton} ${selectedReasons.includes(reason) ? styles.selected : ''}`}
            onClick={() => toggleReasonSelection(reason)}
            type="button"
          >
            {reason}
          </button>
        );
      })
  }, [templates, selectedReasons]);

  return (
    <div className={styles.addReportForm}>
      {error && <div style={{ color: 'red', marginBottom: '15px' }}>{error}</div>}
      <div className={styles.dateReasonSection}>
        <div className={styles.verticalSection}>
          <div className={styles.dateField}>
            <label>تاريخ التأجيلة:</label>
            <input
              type="date"
              value={reportDate}
              onChange={(e) => setReportDate(e.target.value)}
              className={styles.actionInput}
              min={new Date().toISOString().split('T')[0]}
            />
          </div>
          <div className={styles.dateField}>
            <label className={styles.optionalLabel}>
              وصف التأجيل
              <span className={styles.optional}>(اختياري)</span>
            </label>
            <textarea
              value={deferralDescription}
              onChange={(e) => setDeferralDescription(e.target.value)}
              className={styles.actionInput}
              placeholder="أدخل تفاصيل إضافية عن التأجيل..."
              rows={3}
            />
          </div>
        </div>
        <div className={styles.reasonSection}>
          <label>أسباب التأجيلة:</label>
          <div className={styles.selectedReasons}>
            {selectedReasons.length > 0 ? selectedReasons.join(', ') : <span className={styles.noSelection}>لم يتم اختيار أسباب</span>}
          </div>
          {loadingTemplates ? (
            <div>جاري تحميل القوالب...</div>
          ) : (
            <div className={styles.reasonButtons}>
              {memoizedReasonButtons}
              <button className={`${styles.reasonButton} ${styles.addOtherButton}`} onClick={handleAddCustomReason} type="button">
                <FaPlus /> أخرى
              </button>
            </div>
          )}
        </div>
      </div>
      <div className={styles.reportFormButtons}>
        <button onClick={handleSaveClick} className={styles.saveButton}>
          <FaSave className={styles.buttonIcon} />
          <span>حفظ الإضافة</span>
        </button>
        <button onClick={onClose} className={styles.cancelButton}>
          <FaTimes className={styles.buttonIcon} />
          <span>إلغاء</span>
        </button>
      </div>
    </div>
  );
};

export default AddDeferral;
