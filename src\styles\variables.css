/*
 * ملف المتغيرات الموحدة للمشروع - نظام الثيمات الثلاثة
 * CSS Variables File for Unified Project Styling - Three Theme System
 */

:root {
  /* ========================================= */
  /* نظام الثيمات - Theme System */
  /* ========================================= */

  /* الثيم الافتراضي - Default Theme (Light) */
  --current-theme: 'light';

  /* ========================================= */
  /* الخطوط - Fonts */
  /* ========================================= */

  /* الخط الأساسي للمشروع */
  --font-family-primary: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, Oxygen, Ubuntu, Cantarell, 'Open Sans', 'Helvetica Neue', sans-serif;
  
  /* أحجام الخطوط - Font Sizes */
  --font-size-xs: 8px;       /* للنصوص الصغيرة جداً */
  --font-size-sm: 9px;       /* للنصوص الصغيرة */
  --font-size-base: 11px;    /* الحجم الأساسي */
  --font-size-md: 16px;      /* للنصوص المتوسطة */
  --font-size-lg: 18px;      /* للنصوص الكبيرة */
  --font-size-xl: 20px;      /* للعناوين الصغيرة */
  --font-size-2xl: 22px;     /* للعناوين المتوسطة */
  --font-size-3xl: 24px;     /* للعناوين الكبيرة */
  --font-size-4xl: 28px;     /* للعناوين الكبيرة جداً */
  
  /* أوزان الخطوط - Font Weights */
  --font-weight-light: 300;
  --font-weight-normal: 400;
  --font-weight-medium: 500;
  --font-weight-semibold: 600;
  --font-weight-bold: 700;
  
  /* ارتفاع الأسطر - Line Heights */
  --line-height-tight: 1.25;
  --line-height-normal: 1.5;
  --line-height-relaxed: 1.75;

  /* ========================================= */
  /* الألوان - Colors */
  /* ========================================= */
  
  /* الألوان الأساسية للمشروع */
  --primary-color: #380ac1;
  --primary-dark-blue: #2a2e70;
  --primary-medium-blue: #4c68c0;
  --secondary-color: orangered;
  --primary-dark-Violet: #4e2a70;
  --primary-medium-Violet: #7c4cc0;
  
  /* الألوان المحايدة */
  --neutral-50: #f8fafc;
  --neutral-100: #f1f5f9;
  --neutral-200: #e2e8f0;
  --neutral-300: #cbd5e1;
  --neutral-400: #94a3b8;
  --neutral-500: #64748b;
  --neutral-600: #475569;
  --neutral-700: #334155;
  --neutral-800: #1e293b;
  --neutral-900: #0f172a;
  
  /* ألوان الحالة */
  --success-color: #10b981;
  --success-dark: #059669;
  --success-50: #f0fdf4;
  --success-200: #bbf7d0;
  --success-700: #15803d;
  --warning-50: #fffbeb;
  --warning-200: #fed7aa;
  --warning-700: #c2410c;
  --error-50: #fef2f2;
  --error-200: #fecaca;
  --error-700: #b91c1c;
  --info: #3b82f6;
  --info-50: #eff6ff;
  --info-200: #bfdbfe;
  --info-700: #1d4ed8;
  
  /* ألوان إضافية */
  --light-purple-gray: #dad1de;
  --dark-blue-gray: #555269;
  --white: #ffffff;
  
  /* ألوان البطاقات المتخصصة - Specialized Card Colors */
  --parties-color: #1a365d;
  --parties-dark: #2d3748;
  --identification-color: #744210;
  --identification-dark: #553c0f;
  --location-color: #1a202c;
  --location-dark: #2d3748;
  --timeline-color: #f7fafc;
  --timeline-dark: #e2e8f0;

  /* ========================================= */
  /* نظام الثيمات الثلاثة - Three Theme System */
  /* ========================================= */

  /* متغيرات الثيم المضيء - Light Theme Variables */
  --theme-bg-primary: #ffffff;
  --theme-bg-secondary:  var(--neutral-100);
  --theme-bg-tertiary: #f1f5f9;
  --theme-text-primary: #1e293b;
  --theme-text-secondary: #475569;
  --theme-text-tertiary: #64748b;
  --theme-border-primary: #e2e8f0;
  --theme-border-secondary: #cbd5e1;
  --theme-shadow-light: rgba(0, 0, 0, 0.05);
  --theme-shadow-medium: rgba(0, 0, 0, 0.1);
  --theme-shadow-heavy: rgba(0, 0, 0, 0.15);

  /* متغيرات الثيم المظلم - Dark Theme Variables */
  --dark-bg-primary: #0f172a;
  --dark-bg-secondary: #432121;
  --dark-bg-tertiary: #334155;
  --dark-text-primary: #f8fafc;
  --dark-text-secondary: #e2e8f0;
  --dark-text-tertiary: #cbd5e1;
  --dark-border-primary: #334155;
  --dark-border-secondary: #475569;
  --dark-shadow-light: rgba(0, 0, 0, 0.3);
  --dark-shadow-medium: rgba(0, 0, 0, 0.5);
  --dark-shadow-heavy: rgba(0, 0, 0, 0.7);

  /* ========================================= */
  /* المسافات - Spacing */
  /* ========================================= */
  
  --space-1: 4px;
  --space-2: 8px;
  --space-3: 12px;
  --space-4: 16px;
  --space-5: 20px;
  --space-6: 24px;
  --space-8: 32px;
  --space-10: 40px;
  --space-12: 48px;
  
  /* ========================================= */
  /* الزوايا - Border Radius */
  /* ========================================= */
  
  --radius-sm: 4px;
  --radius-md: 8px;
  --radius-lg: 12px;
  --radius-xl: 16px;
  
  /* ========================================= */
  /* الظلال - Shadows */
  /* ========================================= */
  
  --shadow-sm: 0 1px 2px rgba(0, 0, 0, 0.05);
  --shadow-md: 0 4px 6px rgba(0, 0, 0, 0.1);
  --shadow-lg: 0 10px 15px rgba(0, 0, 0, 0.1);
  --shadow-light: rgba(42, 46, 112, 0.1);
  --shadow-medium: rgba(42, 46, 112, 0.2);
  --shadow-heavy: rgba(42, 46, 112, 0.3);
  
  /* ========================================= */
  /* الانتقالات - Transitions */
  /* ========================================= */
  
  --transition-fast: 0.15s ease;
  --transition-normal: 0.3s ease;
  --transition-slow: 0.5s ease;
}

/* ========================================= */
/* الحركات المشتركة - Common Animations */
/* ========================================= */

@keyframes float {
  0%, 100% { transform: translateY(0px) rotate(0deg); }
  50% { transform: translateY(-20px) rotate(2deg); }
}

@keyframes pulse {
  0%, 100% { transform: scale(1); opacity: 0.9; }
  50% { transform: scale(1.05); opacity: 1; }
}

@keyframes modernSpin {
  0% { transform: rotate(0deg); }
  100% { transform: rotate(360deg); }
}

@keyframes rotate {
  from { transform: rotate(0deg); }
  to { transform: rotate(360deg); }
}

@keyframes shimmer {
  0% {
    transform: rotate(45deg) translate(-100%, -100%);
  }
  50% {
    transform: rotate(45deg) translate(0%, 0%);
  }
  100% {
    transform: rotate(45deg) translate(100%, 100%);
  }
}

@keyframes slideDown {
  from {
    opacity: 0;
    transform: translateY(-10px);
  }
  to {
    opacity: 1;
    transform: translateY(0);
  }
}

@keyframes slideIn {
  from {
    opacity: 0;
    transform: translateX(-20px);
  }
  to {
    opacity: 1;
    transform: translateX(0);
  }
}

@keyframes fadeIn {
  from { 
    opacity: 0; 
  }
  to { 
    opacity: 1; 
  }
}

/* ========================================= */
/* التدرجات المشتركة - Common Gradients */
/* ========================================= */

:root {
  /* تدرجات الألوان الأساسية */
  --gradient-primary: linear-gradient(135deg, var(--primary-color), var(--primary-dark));
  --gradient-primary-reverse: linear-gradient(135deg, var(--primary-dark), var(--primary-color));
  --gradient-secondary: linear-gradient(135deg, var(--secondary-color), var(--secondary-dark));
  
  /* تدرجات الخلفيات */
  --gradient-bg-light: linear-gradient(135deg, var(--neutral-50) 0%, var(--neutral-200) 100%);
  --gradient-bg-white: linear-gradient(135deg, #ffffff, #f8fafc);
  
  /* تدرجات الأزرار */
  --gradient-button-primary: linear-gradient(135deg, var(--primary-medium-blue) 0%, var(--primary-dark-blue) 100%);
  --gradient-button-success: linear-gradient(135deg, #28a745, #20c997);
  --gradient-button-danger: linear-gradient(135deg, #dc3545, #c82333);
  
  /* تدرجات الحالات */
  --gradient-parties: linear-gradient(135deg, var(--parties-color), var(--parties-dark));
  --gradient-identification: linear-gradient(135deg, var(--identification-color), var(--identification-dark));
  --gradient-location: linear-gradient(135deg, var(--location-color), var(--location-dark));
  
  /* المسافات المشتركة - Common Paddings */
  --padding-button: 12px 20px;
  --padding-card: 20px;
  --padding-section: 16px 20px;
  --padding-form: 15px 20px;
  
  /* الحدود المشتركة - Common Border Radius */
  --border-radius-sm: 8px;
  --border-radius-md: 12px;
  --border-radius-lg: 15px;
  --border-radius-xl: 20px;
  
  /* الظلال المشتركة - Common Box Shadows */
  --box-shadow-sm: 0 1px 3px rgba(0, 0, 0, 0.1);
  --box-shadow-md: 0 2px 8px rgba(0, 0, 0, 0.1);
  --box-shadow-lg: 0 4px 12px rgba(0, 0, 0, 0.15);
  --box-shadow-xl: 0 8px 25px rgba(0, 0, 0, 0.2);
  --box-shadow-primary: 0 2px 8px rgba(76, 104, 192, 0.2);
  --box-shadow-danger: 0 2px 8px rgba(220, 38, 38, 0.1);
  
  /* الانتقالات المشتركة - Common Transitions */
  --transition-all: all 0.3s ease;
  --transition-transform: transform 0.3s ease;
  --transition-opacity: opacity 0.3s ease;
  --transition-colors: background-color 0.3s ease, color 0.3s ease;
  
  /* التحويلات المشتركة - Common Transforms */
  --transform-hover-up: translateY(-2px);
  --transform-hover-scale: scale(1.05);
  
  /* المسافات الإضافية - Additional Spacing */
  --spacing-xs: 4px;
  --spacing-sm: 8px;
  --spacing-md: 12px;
  --spacing-lg: 16px;
  --spacing-xl: 24px;
  --spacing-2xl: 32px;
  
  /* الزوايا الإضافية - Additional Border Radius */
  --radius-full: 50%;
  
  /* الألوان الأساسية المحدثة - Updated Primary Colors */
  --primary-dark: #524b6c;
  --primary-light: #dbeafe;
  
  /* الأنيميشنات المشتركة - Common Animations */
  --animation-shimmer: shimmer 3s infinite;
  --animation-slide-down: slideDown 0.2s ease-out;
  --animation-slide-in: slideIn 0.5s ease;
  --animation-fade-in: fadeIn 0.3s ease-in-out;
  --animation-spin: modernSpin 1s linear infinite;
  
  /* نقاط التوقف للتصميم المتجاوب - Responsive Breakpoints */
  --breakpoint-mobile: 480px;
  --breakpoint-tablet: 768px;
  --breakpoint-desktop: 1024px;
  --breakpoint-large: 1200px;
  
  /* لون الخلفية الموحد لجميع الصفحات - Unified Page Background */
  --page-background: var(--neutral-100);
  --page-background-alt: var(--neutral-50);
  
  /* تصميم الكروت الموحد - Unified Card Design */
  --card-bg: white;
  --card-border: 1px solid var(--neutral-200);
  --card-border-radius: var(--border-radius-md);
  --card-shadow: var(--box-shadow-md);
  --card-shadow-hover: var(--box-shadow-lg);
  --card-padding: 1.5rem;
  
  /* تصميم رؤوس الكروت الموحد - Unified Card Headers */
  --card-header-bg: linear-gradient(135deg, #014871 0%, #4a8fa3 50%, #d7ede2 100%);
  --card-header-padding: var(--padding-section);

  /* ========================================= */
  /* نظام الأزرار الموحد - Unified Button System */
  /* ========================================= */

  /* الأزرار الأساسية - Base Button Styles */
  --btn-padding: 12px 20px;
  --btn-padding-sm: 8px 16px;
  --btn-padding-lg: 16px 32px;
  --btn-border-radius: var(--radius-md);
  --btn-font-size: var(--font-size-md);
  --btn-font-weight: var(--font-weight-medium);
  --btn-transition: all var(--transition-normal);
  --btn-min-height: 44px;
  --btn-gap: 8px;

  /* الأزرار الأساسية - Primary Buttons */
  --btn-primary-bg: var(--primary-color);
  --btn-primary-bg-hover: var(--primary-dark-blue);
  --btn-primary-color: var(--white);
  --btn-primary-shadow: var(--box-shadow-primary);
  --btn-primary-gradient: linear-gradient(135deg, var(--primary-medium-blue) 0%, var(--primary-dark-blue) 100%);

  /* الأزرار الثانوية - Secondary Buttons */
  --btn-secondary-bg: var(--secondary-color);
  --btn-secondary-bg-hover: var(--secondary-dark);
  --btn-secondary-color: var(--white);
  --btn-secondary-shadow: var(--box-shadow-md);

  /* أزرار الخطر - Danger Buttons */
  --btn-danger-bg: #dc3545;
  --btn-danger-bg-hover: #c82333;
  --btn-danger-color: var(--white);
  --btn-danger-shadow: var(--box-shadow-danger);

  /* الأزرار المحايدة - Neutral Buttons */
  --btn-neutral-bg: var(--current-bg-tertiary);
  --btn-neutral-bg-hover: var(--current-bg-primary);
  --btn-neutral-color: var(--current-text-primary);
  --btn-neutral-border: 1px solid var(--current-border-primary);

  /* أزرار الأيقونات - Icon Buttons */
  --btn-icon-size: 52px; /* تم تكبير الحجم من 45px إلى 52px */
  --btn-icon-size-sm: 36px;
  --btn-icon-size-lg: 56px; /* تم تكبير الحجم الكبير أيضاً */
  --btn-icon-bg: var(--current-bg-secondary);
  --btn-icon-border: 1px solid var(--current-border-primary);

  /* ========================================= */
  /* نظام النماذج الموحد - Unified Form System */
  /* ========================================= */

  /* حقول الإدخال الأساسية - Base Input Styles */
  --input-padding: 12px 16px;
  --input-padding-sm: 8px 12px;
  --input-padding-lg: 16px 20px;
  --input-border-radius: var(--radius-md);
  --input-font-size: var(--font-size-base);
  --input-min-height: 44px;
  --input-transition: all var(--transition-normal);

  /* ألوان حقول الإدخال - Input Colors */
  --input-bg: var(--current-bg-primary);
  --input-bg-focus: var(--current-bg-primary);
  --input-border: 1px solid var(--current-border-primary);
  --input-border-focus: 1px solid var(--primary-color);
  --input-color: var(--current-text-primary);
  --input-placeholder: var(--current-text-secondary);

  /* حالات حقول الإدخال - Input States */
  --input-shadow-focus: 0 0 0 3px rgba(37, 99, 235, 0.1);
  --input-border-error: 1px solid #dc3545;
  --input-border-success: 1px solid var(--success-color);

  /* ========================================= */
  /* نظام التحميل الموحد - Unified Loading System */
  /* ========================================= */

  /* أنيميشن الدوران الموحد - Unified Spin Animation */
  --loading-spinner-size: 40px;
  --loading-spinner-size-sm: 20px;
  --loading-spinner-size-lg: 60px;
  --loading-spinner-border: 3px solid var(--neutral-200);
  --loading-spinner-border-top: 3px solid var(--primary-color);
  --loading-animation: spin 1s linear infinite;
  --card-header-border-radius: var(--border-radius-lg) var(--border-radius-lg) 0 0;
  --card-header-text-color: white;
  --card-header-text-shadow: 0 1px 2px rgba(0, 0, 0, 0.2);
  
  /* تصميم محتوى الكروت الموحد - Unified Card Content */
  --card-content-bg: linear-gradient(135deg, rgba(1, 72, 113, 0.08) 0%, rgba(74, 143, 163, 0.12) 50%, rgba(215, 237, 226, 0.15) 100%);
  --card-content-border-radius: 0 0 var(--border-radius-lg) var(--border-radius-lg);
}

/* تطبيق الخط الأساسي على جميع العناصر */
* {
  font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, Oxygen, Ubuntu, Cantarell, 'Open Sans', 'Helvetica Neue', sans-serif;
}

/* ========================================= */
/* تطبيق الثيمات - Theme Application */
/* ========================================= */

/* الثيم المضيء (افتراضي) - Light Theme (Default) */
body,
body.theme-light {
  --current-bg-primary: var(--theme-bg-primary);
  --current-bg-secondary: var(--theme-bg-secondary);
  --current-bg-tertiary: var(--theme-bg-tertiary);
  --current-text-primary: var(--theme-text-primary);
  --current-text-secondary: var(--theme-text-secondary);
  --current-text-tertiary: var(--theme-text-tertiary);
  --current-border-primary: var(--theme-border-primary);
  --current-border-secondary: var(--theme-border-secondary);
  --current-shadow-light: var(--theme-shadow-light);
  --current-shadow-medium: var(--theme-shadow-medium);
  --current-shadow-heavy: var(--theme-shadow-heavy);
}

/* الثيم المظلم - Dark Theme */
body.theme-dark {
  --current-bg-primary: var(--dark-bg-primary);
  --current-bg-secondary: var(--dark-bg-secondary);
  --current-bg-tertiary: var(--dark-bg-tertiary);
  --current-text-primary: var(--dark-text-primary);
  --current-text-secondary: var(--dark-text-secondary);
  --current-text-tertiary: var(--dark-text-tertiary);
  --current-border-primary: var(--dark-border-primary);
  --current-border-secondary: var(--dark-border-secondary);
  --current-shadow-light: var(--dark-shadow-light);
  --current-shadow-medium: var(--dark-shadow-medium);
  --current-shadow-heavy: var(--dark-shadow-heavy);
}

/* تحديث متغيرات الصفحة لتستخدم الثيم الحالي */
body {
  background-color: var(--current-bg-secondary);
  color: var(--current-text-primary);
  transition: background-color 0.3s ease, color 0.3s ease;
}

/* ========================================= */
/* فئات الأزرار الموحدة - Unified Button Classes */
/* ========================================= */

/* الزر الأساسي - Base Button */
.btn {
  padding: var(--btn-padding);
  border-radius: var(--btn-border-radius);
  font-size: var(--btn-font-size);
  font-weight: var(--btn-font-weight);
  font-family: var(--font-family-primary);
  border: none;
  cursor: pointer;
  transition: var(--btn-transition);
  min-height: var(--btn-min-height);
  display: inline-flex;
  align-items: center;
  justify-content: center;
  gap: var(--btn-gap);
  text-decoration: none;
  box-sizing: border-box;
  position: relative;
  overflow: hidden;
}

.btn:disabled {
  opacity: 0.6;
  cursor: not-allowed;
  transform: none !important;
}

/* أحجام الأزرار - Button Sizes */
.btn-sm {
  padding: var(--btn-padding-sm);
  font-size: var(--font-size-sm);
  min-height: 36px;
}

.btn-lg {
  padding: var(--btn-padding-lg);
  font-size: var(--font-size-lg);
  min-height: 52px;
}

/* الزر الأساسي - Primary Button */
.btn-primary {
  background: var(--btn-primary-gradient);
  color: var(--btn-primary-color);
  box-shadow: var(--btn-primary-shadow);
}

.btn-primary:hover:not(:disabled) {
  background: var(--btn-primary-bg-hover);
  transform: var(--transform-hover-up);
  box-shadow: var(--box-shadow-lg);
}

/* الزر الثانوي - Secondary Button */
.btn-secondary {
  background-color: var(--btn-secondary-bg);
  color: var(--btn-secondary-color);
  box-shadow: var(--btn-secondary-shadow);
}

.btn-secondary:hover:not(:disabled) {
  background-color: var(--btn-secondary-bg-hover);
  transform: var(--transform-hover-up);
}

/* زر الخطر - Danger Button */
.btn-danger {
  background-color: var(--btn-danger-bg);
  color: var(--btn-danger-color);
  box-shadow: var(--btn-danger-shadow);
}

.btn-danger:hover:not(:disabled) {
  background-color: var(--btn-danger-bg-hover);
  transform: var(--transform-hover-up);
}

/* الزر المحايد - Neutral Button */
.btn-neutral {
  background-color: var(--btn-neutral-bg);
  color: var(--btn-neutral-color);
  border: var(--btn-neutral-border);
}

.btn-neutral:hover:not(:disabled) {
  background-color: var(--btn-neutral-bg-hover);
  transform: var(--transform-hover-up);
}

/* زر الأيقونة - Icon Button */
.btn-icon {
  width: var(--btn-icon-size);
  height: var(--btn-icon-size);
  padding: 0;
  border-radius: var(--radius-full);
  background-color: var(--btn-icon-bg);
  border: var(--btn-icon-border);
  color: var(--current-text-secondary);
}

.btn-icon:hover:not(:disabled) {
  background-color: var(--current-bg-tertiary);
  color: var(--current-text-primary);
  transform: var(--transform-hover-scale);
  border-color: var(--primary-color);
  box-shadow: var(--current-shadow-light);
}

.btn-icon-sm {
  width: var(--btn-icon-size-sm);
  height: var(--btn-icon-size-sm);
}

.btn-icon-lg {
  width: var(--btn-icon-size-lg);
  height: var(--btn-icon-size-lg);
}

/* ========================================= */
/* فئات النماذج الموحدة - Unified Form Classes */
/* ========================================= */

/* حقل الإدخال الأساسي - Base Input */
.form-input {
  padding: var(--input-padding);
  border-radius: var(--input-border-radius);
  font-size: var(--input-font-size);
  font-family: var(--font-family-primary);
  min-height: var(--input-min-height);
  width: 100%;
  box-sizing: border-box;
  transition: var(--input-transition);
  background-color: var(--input-bg);
  border: var(--input-border);
  color: var(--input-color);
  direction: rtl;
}

.form-input::placeholder {
  color: var(--input-placeholder);
  opacity: 0.7;
}

.form-input:focus {
  outline: none;
  background-color: var(--input-bg-focus);
  border: var(--input-border-focus);
  box-shadow: var(--input-shadow-focus);
}

/* أحجام حقول الإدخال - Input Sizes */
.form-input-sm {
  padding: var(--input-padding-sm);
  font-size: var(--font-size-sm);
  min-height: 36px;
}

.form-input-lg {
  padding: var(--input-padding-lg);
  font-size: var(--font-size-lg);
  min-height: 52px;
}

/* حالات حقول الإدخال - Input States */
.form-input-error {
  border: var(--input-border-error);
}

.form-input-error:focus {
  border: var(--input-border-error);
  box-shadow: 0 0 0 3px rgba(220, 38, 38, 0.1);
}

.form-input-success {
  border: var(--input-border-success);
}

.form-input-success:focus {
  border: var(--input-border-success);
  box-shadow: 0 0 0 3px rgba(16, 185, 129, 0.1);
}

/* مجموعة النموذج - Form Group */
.form-group {
  display: flex;
  flex-direction: column;
  gap: var(--spacing-sm);
  margin-bottom: var(--spacing-lg);
}

.form-label {
  font-size: var(--font-size-sm);
  font-weight: var(--font-weight-medium);
  color: var(--current-text-primary);
  margin-bottom: var(--spacing-xs);
}

.form-error {
  font-size: var(--font-size-sm);
  color: #dc3545;
  margin-top: var(--spacing-xs);
}

.form-success {
  font-size: var(--font-size-sm);
  color: var(--success-color);
  margin-top: var(--spacing-xs);
}

/* ========================================= */
/* فئات التحميل الموحدة - Unified Loading Classes */
/* ========================================= */

/* دائرة التحميل الأساسية - Base Loading Spinner */
.loading-spinner {
  width: var(--loading-spinner-size);
  height: var(--loading-spinner-size);
  border: var(--loading-spinner-border);
  border-top: var(--loading-spinner-border-top);
  border-radius: 50%;
  animation: var(--loading-animation);
  margin: 0 auto;
}

.loading-spinner-sm {
  width: var(--loading-spinner-size-sm);
  height: var(--loading-spinner-size-sm);
  border-width: 2px;
}

.loading-spinner-lg {
  width: var(--loading-spinner-size-lg);
  height: var(--loading-spinner-size-lg);
  border-width: 4px;
}

/* أنيميشن الدوران */
@keyframes spin {
  0% { transform: rotate(0deg); }
  100% { transform: rotate(360deg); }
}