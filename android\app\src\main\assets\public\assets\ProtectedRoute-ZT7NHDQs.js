import{r,j as t,N as u}from"./index-HUlFBKIW.js";const d=({children:s,user:o,loading:e})=>{const[c,i]=r.useState(!1);return r.useEffect(()=>{const n=setTimeout(()=>{e&&(console.log("ProtectedRoute - Loading timeout exceeded."),i(!0))},1e4);return()=>clearTimeout(n)},[e]),console.log("ProtectedRoute - User:",o),console.log("ProtectedRoute - Loading:",e),c?(console.log("ProtectedRoute - Showing timeout error."),t.jsx("div",{children:"خطأ: لقد استغرق التحقق من حالة المصادقة وقتًا طويلاً. يرجى تحديث الصفحة."})):e?(console.log("ProtectedRoute - Showing loading state..."),t.jsx("div",{children:"جاري التحقق من حالة المصادقة..."})):o?(console.log("ProtectedRoute - User authenticated, showing children..."),s):(console.log("ProtectedRoute - User not authenticated, redirecting to login..."),t.jsx(u,{to:"/login",replace:!0}))};export{d as default};
