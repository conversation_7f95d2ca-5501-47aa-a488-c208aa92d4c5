import{r as C,j as e,L as q}from"./index-HUlFBKIW.js";import{r as Q,s as k,t as U,u as ee,v as te,w as ne}from"./index-DhfUDJs-.js";import{T as oe}from"./TopBar-rd4Lqjij.js";import{g as se,c as O,d as ie}from"./PermissionsService-Dhc9PbZp.js";import{h as le,a as ae}from"./ReportDetailsLogic-CEDnmfXZ.js";import{cacheManager as R,notifyTaskCompleted as H}from"./CacheManager-c0VPvbsl.js";import"./iconBase-BPj5F03O.js";const re="_spin_n6il9_1",ce="_btn_n6il9_1",de="_pageContainer_n6il9_71",_e="_contentWrapper_n6il9_87",me="_header_n6il9_103",ge="_pageTitle_n6il9_121",pe="_filterTabs_n6il9_175",fe="_filterTab_n6il9_175",ue="_activeTab_n6il9_295",he="_tabCount_n6il9_325",ye="_notificationsContainer_n6il9_361",Ne="_notificationsList_n6il9_373",Ie="_slideInUp_n6il9_1",De="_notificationCard_n6il9_415",Ce="_high_n6il9_475",be="_medium_n6il9_497",Te="_low_n6il9_519",we="_cardHeader_n6il9_561",xe="_notificationMeta_n6il9_579",je="_typeIcon_n6il9_587",ve="_notificationType_n6il9_597",Ae="_accountBadge_n6il9_619",Se="_onlineBadge_n6il9_637",Le="_localBadge_n6il9_649",Me="_date_n6il9_661",Oe="_cardBody_n6il9_679",Ee="_caseInfo_n6il9_693",$e="_caseNumber_n6il9_701",Fe="_caseIcon_n6il9_721",Be="_clientInfo_n6il9_731",ke="_courtInfo_n6il9_733",Re="_infoLabel_n6il9_759",He="_infoValue_n6il9_771",Ve="_infoIcon_n6il9_781",Ge="_notificationDetails_n6il9_793",Pe="_detailItem_n6il9_801",We="_detailLabel_n6il9_823",Ye="_detailValue_n6il9_839",Je="_notificationActions_n6il9_859",ze="_completeButton_n6il9_873",Ke="_buttonIcon_n6il9_979",Xe="_emptyState_n6il9_995",Ze="_emptyIcon_n6il9_1015",qe="_emptyTitle_n6il9_1027",Qe="_emptyMessage_n6il9_1041",Ue="_errorContainer_n6il9_1059",et="_errorIcon_n6il9_1079",tt="_errorTitle_n6il9_1091",nt="_errorMessage_n6il9_1105",ot="_float_n6il9_1",st="_pulse_n6il9_1",it="_modernSpin_n6il9_1",lt="_rotate_n6il9_1",at="_shimmer_n6il9_1",rt="_slideDown_n6il9_1",ct="_slideIn_n6il9_1",dt="_fadeIn_n6il9_1",o={spin:re,"theme-light":"_theme-light_n6il9_1","theme-dark":"_theme-dark_n6il9_1",btn:ce,"btn-sm":"_btn-sm_n6il9_1","btn-lg":"_btn-lg_n6il9_1","btn-primary":"_btn-primary_n6il9_1","btn-secondary":"_btn-secondary_n6il9_1","btn-danger":"_btn-danger_n6il9_1","btn-neutral":"_btn-neutral_n6il9_1","btn-icon":"_btn-icon_n6il9_1","btn-icon-sm":"_btn-icon-sm_n6il9_1","btn-icon-lg":"_btn-icon-lg_n6il9_1","form-input":"_form-input_n6il9_1","form-input-sm":"_form-input-sm_n6il9_1","form-input-lg":"_form-input-lg_n6il9_1","form-input-error":"_form-input-error_n6il9_1","form-input-success":"_form-input-success_n6il9_1","form-group":"_form-group_n6il9_1","form-label":"_form-label_n6il9_1","form-error":"_form-error_n6il9_1","form-success":"_form-success_n6il9_1","loading-spinner":"_loading-spinner_n6il9_1","loading-spinner-sm":"_loading-spinner-sm_n6il9_1","loading-spinner-lg":"_loading-spinner-lg_n6il9_1",pageContainer:de,contentWrapper:_e,header:me,pageTitle:ge,filterTabs:pe,filterTab:fe,activeTab:ue,tabCount:he,notificationsContainer:ye,notificationsList:Ne,slideInUp:Ie,notificationCard:De,high:Ce,medium:be,low:Te,cardHeader:we,notificationMeta:xe,typeIcon:je,notificationType:ve,accountBadge:Ae,onlineBadge:Se,localBadge:Le,date:Me,cardBody:Oe,caseInfo:Ee,caseNumber:$e,caseIcon:Fe,clientInfo:Be,courtInfo:ke,infoLabel:Re,infoValue:He,infoIcon:Ve,notificationDetails:Ge,detailItem:Pe,detailLabel:We,detailValue:Ye,notificationActions:Je,completeButton:ze,buttonIcon:Ke,emptyState:Xe,emptyIcon:Ze,emptyTitle:qe,emptyMessage:Qe,errorContainer:Ue,errorIcon:et,errorTitle:tt,errorMessage:nt,float:ot,pulse:st,modernSpin:it,rotate:lt,shimmer:at,slideDown:rt,slideIn:ct,fadeIn:dt},_t=()=>e.jsxs("svg",{xmlns:"http://www.w3.org/2000/svg",width:"24",height:"24",viewBox:"0 0 24 24",fill:"none",stroke:"currentColor",strokeWidth:"2",strokeLinecap:"round",strokeLinejoin:"round",children:[e.jsx("path",{d:"M18 8A6 6 0 0 0 6 8c0 7-3 9-3 9h18s-3-2-3-9"}),e.jsx("path",{d:"M13.73 21a2 2 0 0 1-3.46 0"})]}),V=()=>e.jsxs("svg",{xmlns:"http://www.w3.org/2000/svg",width:"20",height:"20",viewBox:"0 0 24 24",fill:"none",stroke:"currentColor",strokeWidth:"2",strokeLinecap:"round",strokeLinejoin:"round",children:[e.jsx("path",{d:"M22 11.08V12a10 10 0 1 1-5.93-9.14"}),e.jsx("polyline",{points:"22 4 12 14.01 9 11.01"})]}),G=()=>e.jsxs("svg",{xmlns:"http://www.w3.org/2000/svg",width:"24",height:"24",viewBox:"0 0 24 24",fill:"none",stroke:"currentColor",strokeWidth:"2",strokeLinecap:"round",strokeLinejoin:"round",children:[e.jsx("circle",{cx:"12",cy:"12",r:"10"}),e.jsx("line",{x1:"12",y1:"8",x2:"12",y2:"12"}),e.jsx("line",{x1:"12",y1:"16",x2:"12.01",y2:"16"})]}),mt="notifications_",gt=30*1e3,p={DEFERRAL:"تأجيل",ACTION:"إجراء",UPCOMING_ACTION:"إجراء قريب"},S={HIGH:"high",MEDIUM:"medium"},B=l=>{const f=new Date,g=new Date;g.setDate(f.getDate()+1);const v=new Date(l.getFullYear(),l.getMonth(),l.getDate()),u=new Date(g.getFullYear(),g.getMonth(),g.getDate()),N=v.getTime()===u.getTime();return console.log("🔍 isTomorrow check:",{inputDate:l.toDateString(),today:f.toDateString(),tomorrow:g.toDateString(),result:N}),N},pt=(l,f)=>{const g=new Date,v=new Date(f.getFullYear(),f.getMonth(),f.getDate()),u=new Date(g.getFullYear(),g.getMonth(),g.getDate()),N=l.createdAt?new Date(l.createdAt):u,b=new Date(N.getFullYear(),N.getMonth(),N.getDate());if(console.log("🔍 shouldShowNotification:",{actionId:l.id,description:l.description,reminderType:l.reminderType,deadline:f.toDateString(),today:u.toDateString(),createdAt:b.toDateString()}),!l.reminderType||l.reminderType==="dayBefore"){const x=B(f);return console.log("📅 dayBefore logic result:",x),x}if(l.reminderType==="daily"){const x=u>=b,T=u<=v,I=x&&T;return console.log("📅 daily logic:",{isAfterCreation:x,isBeforeDeadline:T,result:I}),I}return console.log("❌ Unknown reminder type:",l.reminderType),!1},P=l=>{try{return new Date(l).toLocaleDateString("ar-EG",{day:"numeric",month:"long",year:"numeric"})}catch{return"تاريخ غير صالح"}},Ct=({currentUser:l})=>{const[f,g]=C.useState([]),[v,u]=C.useState([]),[N,b]=C.useState(!0),[x,T]=C.useState(null),[I,W]=C.useState("all"),[w]=C.useState(()=>{const t=se();return console.log("🔧 الحساب النشط في صفحة الإشعارات:",t),t}),M=C.useMemo(()=>`${mt}${(l==null?void 0:l.uid)||"guest"}`,[l]),Y=async t=>{var d;try{if(console.log("⚡ بدء معالجة إكمال الإجراء:",t),!t.actionId||!t.caseId){console.error("❌ بيانات الإشعار غير مكتملة:",t),alert("خطأ: بيانات الإشعار غير مكتملة");return}if(!window.confirm("هل أنت متأكد من إكمال هذا الإجراء؟"))return;u(m=>m.filter(y=>y.id!==t.id)),g(m=>m.filter(y=>y.id!==t.id));const r=await O(l.uid);console.log("📊 تم جلب بيانات القضايا:",r.length);const c=r.find(m=>m.id===t.caseId);if(console.log("📁 القضية المرتبطة:",c==null?void 0:c.fullCaseNumber),c){const m=(d=c.actions)==null?void 0:d.find(y=>y.id===t.actionId);console.log("📋 الإجراء المحدد:",m==null?void 0:m.description),m?(console.log("⚡ بدء تنفيذ handleCompleteAction"),await le(t.actionId,c.actions||[],()=>{},c,()=>{}),console.log("✅ تم تنفيذ الإجراء بنجاح"),alert("تم تنفيذ الإجراء وإضافته للأرشيف الزمني"),H(l.uid,t.caseId),h(!0)):(console.error("❌ لم يتم العثور على الإجراء المحدد"),alert("خطأ: لم يتم العثور على الإجراء المحدد"),await h(!0))}else console.error("❌ لم يتم العثور على القضية المرتبطة"),alert("خطأ: لم يتم العثور على القضية المرتبطة"),await h(!0)}catch(i){console.error("❌ خطأ في إكمال الإجراء:",i),alert("حدث خطأ أثناء تسجيل إكمال الإجراء: "+i.message),await h(!0)}},J=async t=>{try{if(console.log("⚡ بدء معالجة إكمال التأجيلة:",t),!window.confirm("هل أنت متأكد من تسجيل حضور هذه الجلسة؟"))return;u(c=>c.filter(m=>m.id!==t.id)),g(c=>c.filter(m=>m.id!==t.id));const i=await O(l.uid);console.log("📊 تم جلب بيانات القضايا:",i.length);const r=i.find(c=>c.id===t.caseId||c.fullCaseNumber===t.caseNumber);if(console.log("📁 القضية المرتبطة:",r==null?void 0:r.fullCaseNumber),r){const c=parseInt(t.id.split("-defer-")[1]);if(console.log("📋 فهرس التأجيلة:",c),c>=0&&r.deferrals&&c<r.deferrals.length){console.log("⚡ بدء تنفيذ handleCompleteDeferral");const m=y=>{window.showJudgmentVerdictModal&&window.showJudgmentVerdictModal(y)};await ae(c,r.deferrals||[],()=>{},r,()=>{},m),console.log("✅ تم إكمال التأجيلة بنجاح"),alert("تم تسجيل حضور الجلسة وإضافتها للأرشيف الزمني"),H(l.uid,t.caseId),h(!0)}else console.error("❌ فهرس التأجيلة غير صحيح"),alert("خطأ: لم يتم العثور على التأجيلة المحددة"),await h(!0)}else console.error("❌ لم يتم العثور على القضية المرتبطة"),alert("خطأ: لم يتم العثور على القضية المرتبطة"),await h(!0)}catch(d){console.error("❌ خطأ في إكمال التأجيلة:",d),alert("حدث خطأ أثناء تسجيل حضور الجلسة: "+d.message),await h(!0)}},h=C.useCallback(async(t=!1)=>{if(console.log("🚀 بدء fetchNotifications:",{currentUser:l==null?void 0:l.uid,activeAccount:w,bypassCache:t}),!l){g([]),u([]),b(!1),T("الرجاء تسجيل الدخول لعرض الإشعارات.");return}if(!t){const d=localStorage.getItem(M);if(d)try{const{data:i,timestamp:r,source:c}=JSON.parse(d);if(Date.now()-r<gt&&c===w){g(i),E(I,i),b(!1);return}}catch(i){console.error("Error parsing cache:",i),localStorage.removeItem(M)}}b(!0),T(null);try{const d=l.uid;let i=[];if(w==="online"){if(!navigator.onLine){T("أنت غير متصل بالإنترنت حاليًا. يرجى الاتصال بالإنترنت أو التبديل إلى الحساب المحلي."),b(!1);return}try{i=await O(d),i=i.map(n=>({...n,source:"online"}))}catch(n){console.error("خطأ في الاتصال بـ Firestore:",n),T("حدث خطأ في الاتصال بقاعدة البيانات. يرجى المحاولة لاحقًا أو التبديل إلى الحساب المحلي."),b(!1);return}}else i=await O(d),i=i.map(n=>({...n,source:"local"}));const r=[];console.log("عدد القضايا المجلبة:",i.length),i.forEach(n=>{console.log(`🔍 فحص القضية: ${n.fullCaseNumber}`,{deferralsCount:(n.deferrals||[]).length,actionsCount:(n.actions||[]).length,caseId:n.id,deferrals:n.deferrals,actions:n.actions}),(n.deferrals||[]).forEach((s,a)=>{var _;if(console.log(`🔍 فحص تأجيلة ${a} في القضية ${n.fullCaseNumber}:`,{date:s.date,reasons:s.reasons,isDeleted:s.isDeleted,hasDate:!!s.date,fullDeferral:s}),s.isDeleted||!s.date){console.log(`⏭️ تخطي التأجيلة ${a}: محذوفة أو بدون تاريخ`);return}try{const D=new Date(s.date);console.log(`📅 تاريخ التأجيلة ${a}:`,{originalDate:s.date,parsedDate:D,dateString:D.toDateString(),isValidDate:!isNaN(D.getTime())});const j=B(D);if(console.log(`✅ نتيجة فحص العرض للتأجيلة ${a}:`,j),j){let A=((_=s.reasons)==null?void 0:_.join("، "))||"لا يوجد سبب محدد";s.description&&s.description.trim()&&(A+=` - ${s.description}`);const L={id:`${n.id}-defer-${a}`,type:p.DEFERRAL,priority:S.HIGH,caseNumber:n.fullCaseNumber||"غير محدد",clientName:n.clientName||"غير محدد",courtLocation:n.courtLocation||"غير محددة",date:D,displayDate:P(s.date),reasons:A,deferralDescription:s.description||"",accountType:w,caseId:n.id,deferralIndex:a};console.log("🔔 إضافة إشعار تأجيلة:",L),r.push(L)}}catch(D){console.error(`❌ Invalid date for deferral ${a} in case ${n.id}:`,s.date,D)}}),(n.actions||[]).forEach((s,a)=>{if(console.log(`🔍 فحص إجراء ${a} في القضية ${n.fullCaseNumber}:`,{description:s.description,deadline:s.deadline,reminderType:s.reminderType,isDeleted:s.isDeleted,isCompleted:s.isCompleted,hasDeadline:!!s.deadline,fullAction:s}),s.isDeleted||!s.deadline||s.isCompleted){console.log(`⏭️ تخطي الإجراء ${a}: محذوف أو مكتمل أو بدون موعد`);return}try{const _=new Date(s.deadline);console.log(`📅 تاريخ الموعد النهائي للإجراء ${a}:`,{originalDeadline:s.deadline,parsedDeadline:_,deadlineString:_.toDateString(),isValidDate:!isNaN(_.getTime())});const D=pt(s,_);if(console.log(`✅ نتيجة فحص العرض للإجراء ${a}:`,D),D){let j=S.MEDIUM,A=p.UPCOMING_ACTION;if(s.reminderType==="dayBefore"||B(_))j=S.HIGH,A=p.ACTION;else if(s.reminderType==="daily"){const F=new Date,X=new Date(F.getFullYear(),F.getMonth(),F.getDate()),Z=new Date(_.getFullYear(),_.getMonth(),_.getDate());X.getTime()===Z.getTime()&&(j=S.HIGH,A=p.ACTION)}const L={id:`${n.id}-action-${a}-${j}`,type:A,priority:j,caseNumber:n.fullCaseNumber||"غير محدد",clientName:n.clientName||"غير محدد",courtLocation:n.courtLocation||"غير محددة",date:_,displayDate:P(s.deadline),description:s.description||"لا يوجد وصف",reminderType:s.reminderType||"dayBefore",accountType:w,actionId:s.id,caseId:n.id};console.log("🔔 إضافة إشعار إجراء:",L),r.push(L)}}catch(_){console.error(`❌ Invalid date for action ${a} in case ${n.id}:`,s.deadline,_)}})}),console.log("📊 إجمالي الإشعارات المُنشأة:",r.length),console.log("📋 تفاصيل الإشعارات:",r);const c=r.filter(n=>n.type===p.DEFERRAL),m=r.filter(n=>n.type===p.ACTION||n.type===p.UPCOMING_ACTION);console.log("🔔 إشعارات التأجيلات:",c.length,c),console.log("📋 إشعارات الإجراءات:",m.length,m);const y=new Date;console.log("📅 التاريخ الحالي:",{currentDate:y.toDateString(),currentDateISO:y.toISOString(),currentTime:y.getTime(),timezone:Intl.DateTimeFormat().resolvedOptions().timeZone,timezoneOffset:y.getTimezoneOffset()}),console.log("🔍 فحص شامل للبيانات المجلبة:",{totalCases:i.length,casesWithDeferrals:i.filter(n=>n.deferrals&&n.deferrals.length>0).length,casesWithActions:i.filter(n=>n.actions&&n.actions.length>0).length,totalDeferrals:i.reduce((n,s)=>{var a;return n+(((a=s.deferrals)==null?void 0:a.length)||0)},0),totalActions:i.reduce((n,s)=>{var a;return n+(((a=s.actions)==null?void 0:a.length)||0)},0),activeDeferrals:i.reduce((n,s)=>{var a;return n+(((a=s.deferrals)==null?void 0:a.filter(_=>!_.isDeleted&&_.date).length)||0)},0),activeActions:i.reduce((n,s)=>{var a;return n+(((a=s.actions)==null?void 0:a.filter(_=>!_.isDeleted&&!_.isCompleted&&_.deadline).length)||0)},0)}),r.sort((n,s)=>{const a={[S.HIGH]:1,[S.MEDIUM]:2};return a[n.priority]!==a[s.priority]?a[n.priority]-a[s.priority]:n.date-s.date}),r.forEach(n=>{n.accountType=w}),console.log("تم تعيين الإشعارات:",r.length),g(r),E(I,r);try{localStorage.setItem(M,JSON.stringify({data:r,timestamp:Date.now(),source:w})),w==="local"&&ie(d,r)}catch(n){console.error("Error writing to cache:",n)}}catch(d){console.error("Error fetching notifications:",d),T(w==="online"?"حدث خطأ أثناء جلب الإشعارات من السيرفر. يرجى المحاولة لاحقًا أو التحقق من اتصالك.":"حدث خطأ أثناء جلب الإشعارات المحلية."),g([]),u([])}finally{b(!1)}},[l,M,I]),E=C.useCallback((t,d)=>{u(t==="all"?d:d.filter(i=>i.priority===t))},[]),$=t=>{W(t),E(t,f)};C.useEffect(()=>{const t=setTimeout(()=>{h()},300),d=()=>{console.log("Cache update detected, refreshing notifications..."),h(!0)};R.addListener("notifications_refresh",d);const i=setInterval(()=>{console.log("Refreshing notifications..."),h(!0)},15*1e3);return()=>{clearTimeout(t),clearInterval(i),R.removeListener("notifications_refresh",d)}},[h]);const z={[p.DEFERRAL]:k,[p.ACTION]:Q,[p.UPCOMING_ACTION]:G},K=()=>{if(N)return e.jsx(q,{message:"جاري تحميل الإشعارات..."});if(x)return e.jsxs("div",{className:o.errorContainer,children:[e.jsx(G,{className:o.errorIcon}),e.jsx("h3",{className:o.errorTitle,children:"حدث خطأ"}),e.jsx("p",{className:o.errorMessage,children:x})]});if(v.length===0){const t=I==="all"?"لا يوجد لديك أي إشعارات حالياً":`لا توجد إشعارات ذات أولوية "${I==="high"?"عالية":"متوسطة"}"`;return e.jsxs("div",{className:o.emptyState,children:[e.jsx(_t,{className:o.emptyIcon}),e.jsx("h3",{className:o.emptyTitle,children:"لا توجد إشعارات للعرض"}),e.jsx("p",{className:o.emptyMessage,children:t})]})}return e.jsx("div",{className:o.notificationsList,children:v.map(t=>{const d=z[t.type]||U;return e.jsxs("div",{className:`${o.notificationCard} ${o[t.priority]}`,children:[e.jsxs("div",{className:o.cardHeader,children:[e.jsx("div",{className:o.notificationMeta,children:e.jsxs("h3",{className:o.notificationType,children:[e.jsx(d,{className:o.typeIcon}),t.type]})}),e.jsxs("span",{className:o.date,children:[e.jsx(k,{}),t.displayDate]})]}),e.jsxs("div",{className:o.cardBody,children:[e.jsxs("div",{className:o.caseInfo,children:[e.jsxs("h3",{className:o.caseNumber,children:[e.jsx(ee,{className:o.caseIcon}),t.caseNumber]}),e.jsxs("div",{className:o.clientInfo,children:[e.jsx(te,{className:o.infoIcon}),e.jsx("span",{className:o.infoLabel,children:"الموكل:"}),e.jsx("span",{className:o.infoValue,children:t.clientName})]}),e.jsxs("div",{className:o.courtInfo,children:[e.jsx(ne,{className:o.infoIcon}),e.jsx("span",{className:o.infoLabel,children:"المحكمة:"}),e.jsx("span",{className:o.infoValue,children:t.courtLocation})]})]}),e.jsxs("div",{className:o.notificationDetails,children:[t.type===p.DEFERRAL&&e.jsxs("div",{className:o.detailItem,children:[e.jsx("h4",{className:o.detailLabel,children:"سبب التأجيل"}),e.jsx("div",{className:o.detailValue,children:t.reasons})]}),(t.type===p.ACTION||t.type===p.UPCOMING_ACTION)&&e.jsxs("div",{className:o.detailItem,children:[e.jsx("h4",{className:o.detailLabel,children:"تفاصيل الإجراء"}),e.jsx("div",{className:o.detailValue,children:t.description})]})]}),e.jsxs("div",{className:o.notificationActions,children:[(t.type===p.ACTION||t.type===p.UPCOMING_ACTION)&&e.jsxs("button",{className:o.completeButton,onClick:i=>{i.stopPropagation(),Y(t)},title:"تم تنفيذ التنبيه",children:[e.jsx(V,{className:o.buttonIcon}),e.jsx("span",{children:"تم التنفيذ"})]}),t.type===p.DEFERRAL&&e.jsxs("button",{className:o.completeButton,onClick:i=>{i.stopPropagation(),J(t)},title:"تم حضور الجلسة",children:[e.jsx(V,{className:o.buttonIcon}),e.jsx("span",{children:"تم الحضور"})]})]})]})]},t.id)})})};return e.jsxs("div",{className:o.pageContainer,children:[e.jsx(oe,{currentUser:l}),e.jsxs("div",{className:o.contentWrapper,children:[e.jsx("div",{className:o.header,children:e.jsx("h1",{className:o.pageTitle,children:"الإشعارات"})}),e.jsxs("div",{className:o.filterTabs,children:[e.jsxs("button",{className:`${o.filterTab} ${I==="all"?o.activeTab:""}`,onClick:()=>$("all"),disabled:N,children:[e.jsx("span",{children:"جميع الإشعارات"}),e.jsx("span",{className:o.tabCount,children:f.length})]}),e.jsxs("button",{className:`${o.filterTab} ${I==="high"?o.activeTab:""}`,onClick:()=>$("high"),disabled:N,children:[e.jsx("span",{children:"أولوية عالية"}),e.jsx("span",{className:o.tabCount,children:f.filter(t=>t.priority==="high").length})]}),e.jsxs("button",{className:`${o.filterTab} ${I==="medium"?o.activeTab:""}`,onClick:()=>$("medium"),disabled:N,children:[e.jsx("span",{children:"أولوية متوسطة"}),e.jsx("span",{className:o.tabCount,children:f.filter(t=>t.priority==="medium").length})]})]}),e.jsx("div",{className:o.notificationsContainer,children:K()})]})]})};export{Ct as default};
