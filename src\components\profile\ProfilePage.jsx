import React, { useState, useEffect } from 'react';
import Swal from 'sweetalert2';
import { useNavigate } from 'react-router-dom';
import { FaEdit, FaSave, FaTimes, FaBuilding, FaUsers, FaGlobe, FaMobileAlt, FaUser, FaEnvelope, FaPhone, FaLock, FaChevronLeft, FaUserCircle, FaShieldAlt, FaCreditCard, FaPlus, FaWhatsapp, FaSignOutAlt } from 'react-icons/fa';
import TopBar from '../topbar/TopBar';
import styles from './ProfilePage.module.css';
import { auth, db } from '../../config/firebaseConfig';
import { signOut } from 'firebase/auth';
import { doc, getDoc, updateDoc, collection, query, where, getDocs, writeBatch } from 'firebase/firestore';
import { getActiveAccount, setActiveAccount } from '../../services/StorageService';
import LoadingSpinner from '../ui/LoadingSpinner';
import { useSubscription } from '../../hooks/useSubscription';

const ProfilePage = ({ currentUser }) => {
  const navigate = useNavigate();
  const subscription = useSubscription(currentUser);
  const [userData, setUserData] = useState(null);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState(null);
  const [formData, setFormData] = useState({
    name: '',
    email: '',
    phone: '',
    
  });
  const [editingField, setEditingField] = useState(null);
  const [activeAccount, setActiveAccountState] = useState(getActiveAccount());
  const [localUserData, setLocalUserData] = useState(null);

  useEffect(() => {
    const fetchUserData = async () => {
      if (!currentUser || !currentUser.uid) {
        setError('المستخدم غير مسجل الدخول. يرجى تسجيل الدخول مرة أخرى.');
        setLoading(false);
        return;
      }

      setLoading(true);
      setError(null);

      try {
        const userRef = doc(db, 'users', currentUser.uid);
        const userSnap = await getDoc(userRef);
        if (userSnap.exists()) {
          const data = userSnap.data();
          setUserData({
            ...data,
            name: data.name || data.username || currentUser?.displayName || currentUser?.email || '',
            email: data.email || currentUser?.email || '',
            phone: data.phone || '',
          });
          
        } else {
          console.log('لم يتم العثور على بيانات المستخدم في Firestore.');
          if (activeAccount === 'online' && !navigator.onLine) {
            setError('لا يمكن الوصول إلى بيانات الحساب السحابي حاليًا لأنك غير متصل بالإنترنت ولم يتم تخزين البيانات مؤقتًا بعد.');
          }
        }
      } catch (e) {
        console.error('خطأ في جلب بيانات المستخدم من Firestore:', e.message);
        if (activeAccount === 'online' && !navigator.onLine) {
          setError('حدث خطأ أثناء جلب بيانات الحساب السحابي. يرجى التحقق من اتصالك بالإنترنت.');
        }
      }

      try {
        const localData = localStorage.getItem('localUserData_' + currentUser.uid);
        if (localData) {
          const parsedLocalData = JSON.parse(localData);
          setLocalUserData({
            ...parsedLocalData,
            name: parsedLocalData.name || parsedLocalData.username || currentUser?.displayName || currentUser?.email || '',
            email: parsedLocalData.email || currentUser?.email || '',
            phone: parsedLocalData.phone || '',
          });
          
        } else {
          console.log('لم يتم العثور على بيانات محلية للمستخدم.');
        }
      } catch (e) {
        console.error('خطأ في قراءة البيانات المحلية:', e.message);
      }

      setLoading(false);
    };

    fetchUserData();

    const savedActiveAccount = localStorage.getItem('activeAccount');
    if (savedActiveAccount) {
      setActiveAccount(savedActiveAccount);
    }

    const handleOnlineStatusChange = () => {
      if (!navigator.onLine && activeAccount === 'online') {
        alert('أنت الآن غير متصل بالإنترنت. قد تواجه مشكلات في الوصول إلى بيانات الحساب الأونلاين. يمكنك التبديل إلى الحساب المحلي للاستمرار في العمل دون اتصال.');
      }
    };
    window.addEventListener('online', handleOnlineStatusChange);
    window.addEventListener('offline', handleOnlineStatusChange);
    return () => {
      window.removeEventListener('online', handleOnlineStatusChange);
      window.removeEventListener('offline', handleOnlineStatusChange);
    };
  }, [currentUser, activeAccount]);

  const handleSave = async (field, value) => {
    if (!currentUser?.uid) {
      setError('المستخدم غير مسجل الدخول.');
      return;
    }
    
    if (field === 'name' && !value) {
      setError('الاسم مطلوب.');
      return;
    }

    try {
      setError(null);
      const updateData = { [field]: value };

      if (activeAccount === 'online') {
        const userRef = doc(db, 'users', currentUser.uid);
        await updateDoc(userRef, {
          ...updateData,
          lastUpdateTime: new Date().toISOString()
        });
        setUserData(prevData => ({ ...prevData, ...updateData, lastUpdateTime: new Date().toISOString() }));
        Swal.fire({
          title: 'تم الحفظ!',
          text: 'تم حفظ البيانات بنجاح. ستتم مزامنتها مع الحساب السحابي عند توفر الاتصال بالإنترنت.',
          icon: 'success',
          customClass: {
            popup: styles['swal2-popup'],
            title: styles['swal2-title'],
            htmlContainer: styles['swal2-html-container'],
            confirmButton: styles['swal2-confirm'],
          },
          buttonsStyling: false,
        });
      } else {
        const updatedLocalData = {
          ...(localUserData || {}),
          uid: currentUser.uid,
          ...updateData,
          lastUpdatedAt: new Date().toISOString()
        };
        localStorage.setItem('localUserData_' + currentUser.uid, JSON.stringify(updatedLocalData));
        setLocalUserData(updatedLocalData);
        Swal.fire({
          title: 'تم الحفظ!',
          text: 'تم حفظ البيانات بنجاح في الحساب المحلي.',
          icon: 'success',
          customClass: {
            popup: styles['swal2-popup'],
            title: styles['swal2-title'],
            htmlContainer: styles['swal2-html-container'],
            confirmButton: styles['swal2-confirm'],
          },
          buttonsStyling: false,
        });
      }
      
      setEditingField(null);
      
    } catch (e) {
      setError('خطأ في تحديث تحديث البيانات: ' + e.message);
      if (activeAccount === 'online' && !navigator.onLine) {
        Swal.fire({
          title: 'خطأ في الحفظ',
          text: 'حدث خطأ في حفظ البيانات في الحساب الأونلاين. هل ترغب في التبديل إلى الحساب المحلي؟',
          icon: 'error',
          showCancelButton: true,
          confirmButtonText: 'نعم، التبديل',
          cancelButtonText: 'إلغاء',
        }).then((result) => {
          if (result.isConfirmed) {
            switchToLocalAccount();
          }
        });
      }
    }
  };

  const handleChange = (e) => {
    setFormData({ ...formData, [e.target.name]: e.target.value });
  };

  const handleLogout = async () => {
    const result = await Swal.fire({
      title: 'تسجيل الخروج',
      text: 'هل أنت متأكد من أنك تريد تسجيل الخروج؟',
      icon: 'question',
      showCancelButton: true,
      confirmButtonText: 'نعم، تسجيل الخروج',
      cancelButtonText: 'إلغاء',
      customClass: {
        popup: styles['swal2-popup'],
        title: styles['swal2-title'],
        htmlContainer: styles['swal2-html-container'],
        confirmButton: styles['swal2-confirm'],
        cancelButton: styles['swal2-cancel'],
      },
      buttonsStyling: false,
    });

    if (result.isConfirmed) {
      try {
        await signOut(auth);
        navigate('/login');
      } catch (error) {
        console.error("خطأ أثناء تسجيل الخروج:", error);
        Swal.fire({
          title: 'خطأ',
          text: 'حدث خطأ أثناء تسجيل الخروج. يرجى المحاولة مرة أخرى.',
          icon: 'error',
          customClass: {
            popup: styles['swal2-popup'],
            title: styles['swal2-title'],
            htmlContainer: styles['swal2-html-container'],
            confirmButton: styles['swal2-confirm'],
          },
          buttonsStyling: false,
        });
      }
    }
  };

  const switchToLocalAccount = () => {
    setActiveAccount('local');
    setActiveAccountState('local');
    window.location.reload();
  };

  const createLocalAccount = async () => {
    const { isConfirmed } = await Swal.fire({
      title: 'إنشاء حساب أوفلاين جديد',
      html: `
        <div style="text-align: right;">
          <p>نوفر لكم إمكانية إنشاء <strong>حساب واحد فقط من كل نوع</strong>:</p>
          <ul style="margin: 0 0 10px 24px; padding: 0; list-style-position: inside; direction: rtl; text-align: right;">
            <li style="margin-bottom: 4px;">حساب <strong>سحابي (أونلاين)</strong> يُخزّن البيانات على قاعدة بيانات سحابية للوصول إليها من أي مكان.</li>
            <li>حساب <strong>محلي (أوفلاين)</strong> يُخزّن البيانات على الجهاز للاستخدام بدون اتصال بالإنترنت.</li>
          </ul>
          <p>يمكنكم أيضًا <strong>حذف أي حساب</strong> في أي وقت لضمان التحكم الكامل في بياناتكم.</p>
          <div style="background-color: #fff3cd; border: 1px solid #ffeaa7; border-radius: 8px; padding: 15px; margin-top: 15px; text-align: right;">
            <div style="font-weight: bold; color: #856404; margin-bottom: 8px;">⚠️ ملاحظة مهمة:</div>
            <p style="margin: 0; color: #856404; font-size: 0.95rem;">
              سيتم إنشاء حساب فارغ جديد. يمكنك نقل البيانات لاحقاً من الحساب الأونلاين إذا أردت.
            </p>
          </div>
          <p style="margin-top: 20px; font-weight: 500;">هل تريد المتابعة وإنشاء الحساب الأوفلاين؟</p>
        </div>
      `,
      icon: 'info',
      showCancelButton: true,
      confirmButtonText: 'نعم، إنشاء الحساب',
      cancelButtonText: 'إلغاء',
      customClass: {
        popup: styles['swal2-popup'],
        title: styles['swal2-title'],
        htmlContainer: styles['swal2-html-container'],
        confirmButton: styles['swal2-confirm'],
        cancelButton: styles['swal2-cancel'],
        actions: styles['swal2-actions'],
      },
      buttonsStyling: false,
    });

    if (isConfirmed) {
      if (!currentUser?.uid) return;
      
      const newLocalUserData = {
        uid: currentUser.uid,
        name: currentUser.displayName || '',
        email: currentUser.email || '',
        phone: '',
        
        cases: [],
        createdAt: new Date().toISOString(),
        lastUpdatedAt: new Date().toISOString()
      };
      localStorage.setItem('localUserData_' + currentUser.uid, JSON.stringify(newLocalUserData));
      setLocalUserData(newLocalUserData);
      setActiveAccount('local');
      setActiveAccountState('local');
      
      Swal.fire({
        title: 'تم الإنشاء!',
        text: 'تم إنشاء حساب أوفلاين جديد بنجاح والتبديل إليه.',
        icon: 'success',
        customClass: {
          popup: styles['swal2-popup'],
          title: styles['swal2-title'],
          htmlContainer: styles['swal2-html-container'],
          confirmButton: styles['swal2-confirm'],
        },
        buttonsStyling: false,
      });
      window.location.reload();
    }
  };

  const handleDeleteAccountClick = async (accountType) => {
    const { value: passwordConfirm } = await Swal.fire({
      title: `تأكيد حذف الحساب ${accountType === 'online' ? 'الأونلاين' : 'المحلي'}`,
      html: `
        <div style="text-align: right;">
          <p style="margin-bottom: 15px;">هذا الإجراء لا يمكن التراجع عنه. لتأكيد الحذف، يرجى إدخال كلمة المرور الخاصة بك:</p>
          <input
            type="password"
            id="password-input"
            class="swal2-input"
            placeholder="كلمة المرور"
            style="direction: ltr; text-align: left;"
          />
        </div>
      `,
      icon: 'warning',
      showCancelButton: true,
      confirmButtonText: 'تأكيد الحذف',
      cancelButtonText: 'إلغاء',
      focusConfirm: false,
      preConfirm: () => {
        const password = Swal.getPopup().querySelector('#password-input').value;
        if (!password) {
          Swal.showValidationMessage(`يرجى إدخال كلمة المرور`);
          return false;
        }
        if (password.length < 6) {
          Swal.showValidationMessage(`كلمة المرور يجب أن تكون 6 أحرف على الأقل`);
          return false;
        }
        return password;
      },
      customClass: {
        popup: styles['swal2-popup'],
        title: styles['swal2-title'],
        htmlContainer: styles['swal2-html-container'],
        confirmButton: styles['swal2-confirm'],
        cancelButton: styles['swal2-cancel'],
        actions: styles['swal2-actions'],
      },
      buttonsStyling: false,
    });

    if (passwordConfirm) {
      if (accountType === 'online') {
        try {
          const casesRef = collection(db, 'cases');
          const q = query(casesRef, where('userId', '==', currentUser.uid));
          const querySnapshot = await getDocs(q);
          
          const batch = writeBatch(db);
          
          querySnapshot.forEach((doc) => {
            batch.delete(doc.ref);
          });

          // No longer need to delete linked/shared cases if group system is removed

          const userRef = doc(db, 'users', currentUser.uid);
          await updateDoc(userRef, {
            isDeleted: true,
            deletedAt: new Date().toISOString()
          });

          setUserData(null);
          Swal.fire({
            title: 'تم الحذف!',
            text: 'تم حذف الحساب الأونلاين وجميع القضايا المرتبطة به بنجاح.',
            icon: 'success',
            customClass: {
              popup: styles['swal2-popup'],
              title: styles['swal2-title'],
              htmlContainer: styles['swal2-html-container'],
              confirmButton: styles['swal2-confirm'],
            },
            buttonsStyling: false,
          });
        } catch (error) {
          console.error('خطأ في حذف البيانات:', error);
          Swal.fire('خطأ', 'حدث خطأ أثناء حذف البيانات. يرجى المحاولة مرة أخرى.', 'error');
        }
      } else if (accountType === 'local') {
        try {
          const allKeys = Object.keys(localStorage);
          const userKeys = allKeys.filter(key => key.includes(currentUser.uid));
          userKeys.forEach(key => localStorage.removeItem(key));
          
          setLocalUserData(null);
          if (activeAccount === 'local') {
            setActiveAccount('online');
            setActiveAccountState('online');
          }
          Swal.fire({
            title: 'تم الحذف!',
            text: 'تم حذف الحساب المحلي وجميع البيانات المرتبطة به بنجاح.',
            icon: 'success',
            customClass: {
              popup: styles['swal2-popup'],
              title: styles['swal2-title'],
              htmlContainer: styles['swal2-html-container'],
              confirmButton: styles['swal2-confirm'],
            },
            buttonsStyling: false,
          });
        } catch (error) {
          console.error('خطأ في حذف البيانات المحلية:', error);
          Swal.fire('خطأ', 'حدث خطأ أثناء حذف البيانات. يرجى المحاولة مرة أخرى.', 'error');
        }
      }
    }
  };

  const currentDisplayData = activeAccount === 'online' ? userData : localUserData;

  useEffect(() => {
    const data = activeAccount === 'online' ? userData : localUserData;
    if (data) {
      setFormData({
        name: data.name || data.username || currentUser?.displayName || currentUser?.email || '',
        email: data.email || currentUser?.email || '',
        phone: data.phone || '',
      });
    } else {
      // Clear form data if there is no user data
      setFormData({ name: '', email: '', phone: '' });
    }
  }, [activeAccount, userData, localUserData, currentUser]);

  if (loading) {
    return (
      <div className={styles.pageWrapper}>
        <TopBar currentUser={currentUser} />
        <LoadingSpinner message="جاري تحميل الملف الشخصي..." />
      </div>
    );
  }

  return (
    <>
      <TopBar currentUser={currentUser} />
      <div className={styles.pageWrapper}>
        <div className={styles.content}>
          {/* Profile Header */}
          <div className={styles.profileHeader}>
            <h1 className={styles.userName}>{currentDisplayData?.name || currentUser?.displayName || currentUser?.email || 'مستخدم'}</h1>
            <p className={styles.welcomeMessage}>مرحباً بك في ملفك الشخصي!</p>
          </div>

          {/* Personal Information Section */}
          <div className={styles.sectionCard}>
            <div className={styles.sectionHeader}>
              <FaUser size={20} color="var(--primary-dark-blue)" />
              <h3 className={styles.sectionTitle}>المعلومات الشخصية</h3>
            </div>
            <div className={styles.infoItem}>
              <span className={styles.infoLabel}><FaUser size={16} /> الاسم:</span>
              <span className={styles.infoValue}>
                {currentDisplayData?.name || 'غير محدد'}
                <button className={styles.editButton} onClick={() => {
                  setEditingField('name');
                  Swal.fire({
                    title: 'تعديل الاسم',
                    input: 'text',
                    inputValue: formData.name,
                    showCancelButton: true,
                    confirmButtonText: 'حفظ',
                    cancelButtonText: 'إلغاء',
                    inputValidator: (value) => {
                      if (!value) {
                        return 'الاسم مطلوب!';
                      }
                    },
                    customClass: {
                      popup: styles['swal2-popup'],
                      title: styles['swal2-title'],
                      htmlContainer: styles['swal2-html-container'],
                      input: styles['swal2-input'],
                      confirmButton: styles['swal2-confirm'],
                      cancelButton: styles['swal2-cancel'],
                      actions: styles['swal2-actions'],
                    },
                    buttonsStyling: false,
                  }).then((result) => {
                    if (result.isConfirmed) {
                      setFormData(prev => ({ ...prev, name: result.value }));
                      handleSave('name', result.value);
                    }
                  });
                }}><FaEdit /></button>
              </span>
            </div>
            <div className={styles.infoItem}>
              <span className={styles.infoLabel}><FaEnvelope size={16} /> البريد الإلكتروني:</span>
              <span className={styles.infoValue}>
                {currentDisplayData?.email || currentUser?.email || 'غير محدد'}
                <button className={styles.editButton} onClick={() => {
                  Swal.fire({
                    title: 'معلومات البريد الإلكتروني',
                    html: `
                      <div style="text-align: right;">
                        <p> لا يمكنك تغيير هذا العنوان. <strong>${currentDisplayData?.email || currentUser?.email || 'غير محدد'}</strong> يتم استخدام بريدك الإلكتروني لتسجيل الدخول وإرسال إشعارات مهمة متعلقة بحسابك. إذا كنت بحاجة إلى تغيير بريدك الإلكتروني، يرجى التواصل مع الدعم الفني.</p>
                      </div>
                    `,
                    icon: 'info',
                    confirmButtonText: 'حسناً',
                    customClass: {
                      popup: styles['swal2-popup'],
                      title: styles['swal2-title'],
                      htmlContainer: styles['swal2-html-container'],
                      confirmButton: styles['swal2-confirm'],
                    },
                    buttonsStyling: false,
                  });
                }}>
                  <FaEdit />
                </button>
              </span>
            </div>
            <div className={styles.infoItem}>
              <span className={styles.infoLabel}><FaPhone size={16} /> رقم الهاتف:</span>
              <span className={styles.infoValue}>
                {currentDisplayData?.phone || 'غير محدد'}
                <button className={styles.editButton} onClick={() => {
                  setEditingField('phone');
                  Swal.fire({
                    title: 'تعديل رقم الهاتف',
                    input: 'text',
                    inputValue: currentDisplayData?.phone || '',
                    showCancelButton: true,
                    confirmButtonText: 'حفظ',
                    cancelButtonText: 'إلغاء',
                    customClass: {
                      popup: styles['swal2-popup'],
                      title: styles['swal2-title'],
                      htmlContainer: styles['swal2-html-container'],
                      input: styles['swal2-input'],
                      confirmButton: styles['swal2-confirm'],
                      cancelButton: styles['swal2-cancel'],
                      actions: styles['swal2-actions'],
                    },
                    buttonsStyling: false,
                  }).then((result) => {
                    if (result.isConfirmed) {
                      setFormData(prev => ({ ...prev, phone: result.value }));
                      handleSave('phone', result.value);
                    }
                  });
                }}><FaEdit /></button>
              </span>
            </div>
            
            
          </div>

          {/* Account Management Section */}
          <div className={styles.sectionCard}>
            <div className={styles.sectionHeader}>
              <FaUsers size={20} color="var(--primary-dark-blue)" />
              <h3 className={styles.sectionTitle}>إدارة الحسابات</h3>
            </div>
            <div className={styles.accountCardsContainer}>
              <div className={`${styles.accountCard} ${activeAccount === 'online' ? styles.active : ''}`}>
                <FaGlobe className={styles.accountIcon} />
                <h4 className={styles.accountType}>الحساب السحابي (أونلاين)</h4>
                <p className={styles.accountEmail}>{userData?.email || currentUser?.email || 'غير محدد'}</p>
                <div className={styles.accountActions}>
                  {activeAccount !== 'online' && (
                    <button className={`${styles.actionButton} ${styles.switchButton}`} onClick={() => {
                      setActiveAccount('online');
                      setActiveAccountState('online');
                      Swal.fire('تم التبديل!', 'تم التبديل إلى الحساب السحابي بنجاح.', 'success');
                    }}>
                      التبديل إليه
                    </button>
                  )}
                  <button className={`${styles.actionButton} ${styles.deleteButton}`} onClick={() => handleDeleteAccountClick('online')}>
                    حذف
                  </button>
                </div>
              </div>

              {localUserData ? (
                <div className={`${styles.accountCard} ${activeAccount === 'local' ? styles.active : ''}`}>
                  <FaMobileAlt className={styles.accountIcon} />
                  <h4 className={styles.accountType}>الحساب المحلي (أوفلاين)</h4>
                  <p className={styles.accountEmail}>{localUserData?.email || 'غير محدد'}</p>
                  <div className={styles.accountActions}>
                    {activeAccount !== 'local' && (
                      <button className={`${styles.actionButton} ${styles.switchButton}`} onClick={() => {
                        setActiveAccount('local');
                        setActiveAccountState('local');
                        Swal.fire('تم التبديل!', 'تم التبديل إلى الحساب المحلي بنجاح.', 'success');
                      }}>
                        التبديل إليه
                      </button>
                    )}
                    <button className={`${styles.actionButton} ${styles.deleteButton}`} onClick={() => handleDeleteAccountClick('local')}>
                      حذف
                    </button>
                  </div>
                </div>
              ) : (
                <div className={`${styles.accountCard} ${styles.addAccountCard}`}>
                  <FaPlus size={40} color="var(--primary-dark-blue)" />
                  <h4 className={styles.accountType}>إنشاء حساب محلي جديد</h4>
                  <button className={`${styles.actionButton} ${styles.addButton}`} onClick={createLocalAccount}>
                    إنشاء
                  </button>
                </div>
              )}
            </div>
          </div>

          {/* Security Section */}
          <div className={styles.sectionCard}>
            <div className={styles.sectionHeader}>
              <FaShieldAlt size={20} color="var(--primary-dark-blue)" />
              <h3 className={styles.sectionTitle}>الأمان</h3>
            </div>
            <div className={styles.infoItem}>
              <span className={styles.infoLabel}><FaLock size={16} /> كلمة المرور:</span>
              <span className={styles.infoValue}>
                ••••••••
                <button className={styles.editButton} onClick={() => {
                  Swal.fire({
                    title: 'تغيير كلمة المرور',
                    html: `
                      <div style="text-align: right;">
                        
                        <div style="margin-bottom: 15px;">
                          <label for="swal-input1" class="swal2-label">كلمة المرور الجديدة:</label>
                          <input id="swal-input1" class="swal2-input" type="password" placeholder="كلمة المرور الجديدة" style="direction: ltr; text-align: left;" />
                        </div>
                        <div>
                          <label for="swal-input2" class="swal2-label">تأكيد كلمة المرور الجديدة:</label>
                          <input id="swal-input2" class="swal2-input" type="password" placeholder="تأكيد كلمة المرور الجديدة" style="direction: ltr; text-align: left;" />
                        </div>
                        <p style="margin-top: 20px; font-size: 0.9rem; color: var(--neutral-600);">استخدم 8 أحرف على الأقل مع مزيج من الأحرف الكبيرة والصغيرة والأرقام والرموز لإنشاء كلمة مرور قوية. تجنب استخدام معلومات شخصية يمكن تخمينها بسهولة.</p>
                      </div>
                    `,
                    icon: 'info',
                    showCancelButton: true,
                    confirmButtonText: 'حفظ',
                    cancelButtonText: 'إلغاء',
                    focusConfirm: false,
                    preConfirm: () => {
                      const newPassword = Swal.getPopup().querySelector('#swal-input1').value;
                      const confirmPassword = Swal.getPopup().querySelector('#swal-input2').value;

                      if (!newPassword || !confirmPassword) {
                        Swal.showValidationMessage(`يرجى إدخال كلتا كلمتي المرور`);
                        return false;
                      }
                      if (newPassword !== confirmPassword) {
                        Swal.showValidationMessage(`كلمتا المرور غير متطابقتين`);
                        return false;
                      }
                      if (newPassword.length < 8) {
                        Swal.showValidationMessage(`كلمة المرور يجب أن تكون 8 أحرف على الأقل`);
                        return false;
                      }
                      // يمكنك إضافة المزيد من التحقق هنا (أحرف كبيرة، أرقام، رموز)
                      return { newPassword: newPassword };
                    },
                    customClass: {
                      popup: styles['swal2-popup'],
                      title: styles['swal2-title'],
                      htmlContainer: styles['swal2-html-container'],
                      input: styles['swal2-input'],
                      confirmButton: styles['swal2-confirm'],
                      cancelButton: styles['swal2-cancel'],
                      actions: styles['swal2-actions'],
                    },
                    buttonsStyling: false,
                  }).then((result) => {
                    if (result.isConfirmed) {
                      // هنا يمكنك إضافة منطق تحديث كلمة المرور الفعلية
                      Swal.fire({
                        title: 'تم الحفظ!',
                        text: 'تم تحديث كلمة المرور بنجاح (هذا مجرد عرض توضيحي).',
                        icon: 'success',
                        customClass: {
                          popup: styles['swal2-popup'],
                          title: styles['swal2-title'],
                          htmlContainer: styles['swal2-html-container'],
                          confirmButton: styles['swal2-confirm'],
                        },
                        buttonsStyling: false,
                      });
                    }
                  });
                }}>
                  <FaEdit />
                </button>
              </span>
            </div>
          </div>

          {/* Payments and Subscriptions Section */}
          <div className={styles.sectionCard}>
            <div className={styles.sectionHeader}>
              <FaCreditCard size={20} color="var(--primary-dark-blue)" />
              <h3 className={styles.sectionTitle}>الدفع والاشتراكات</h3>
            </div>
            <div className={styles.paymentInfoCard}>
              <p className={styles.paymentInfoText}>
                لتفعيل الاشتراك أو للاستفسار، يرجى التواصل معنا عبر WhatsApp.
                <a href="https://wa.me/201202453329" target="_blank" rel="noopener noreferrer" className={styles.whatsappLink}>
                  <FaWhatsapp />
                  <span>01202453329</span>
                </a>
                نحن هنا دائمًا لخدمتك وتقديم أفضل تجربة ممكنة.
              </p>
            </div>
            {subscription.status === 'local_locked' && (
                <div className={styles.subscriptionCard} style={{borderColor: 'orange', backgroundColor: '#fff3e0'}}>
                    <p className={styles.expiredText} style={{color: 'orange'}}>الحساب المحلي مُجمَّد</p>
                    <p>لقد مر أكثر من 21 يومًا على آخر اتصال بالإنترنت. لاستخدام الحساب المحلي مرة أخرى، يرجى الاتصال بالإنترنت لمرة واحدة على الأقل لتحديث حالة الحساب.</p>
                </div>
            )}
            <div className={styles.infoItem}>
              <span className={styles.infoLabel}><FaBuilding size={16} /> الاشتراك الحالي:</span>
              <span className={styles.infoValue}>
                {subscription.status === 'loading' && 'جاري التحميل...'}
                {subscription.status === 'trial' && `الفترة التجريبية (${subscription.daysRemaining} يوم متبقي)`}
                {subscription.status === 'active' && <span style={{ color: 'green' }}>الخطة المدفوعة</span>}
                {subscription.status === 'expired' && <span style={{ color: 'red' }}>الفترة التجريبية انتهت</span>}
                {subscription.status === 'local_locked' && <span style={{ color: 'orange' }}>الحساب المحلي مُجمَّد</span>}
              </span>
            </div>
          </div>

          {/* زر تسجيل الخروج */}
          <div className={styles.logoutSection}>
            <button
              className={styles.logoutButton}
              onClick={handleLogout}
            >
              <FaSignOutAlt size={18} />
              <span>تسجيل الخروج</span>
            </button>
          </div>

        </div>
      </div>
    </>
  );
};

export default ProfilePage;