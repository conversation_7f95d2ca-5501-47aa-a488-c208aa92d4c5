:root{--current-theme: "light";--font-family-primary: -apple-system, BlinkMacSystemFont, "Segoe UI", <PERSON><PERSON>, Oxygen, Ubuntu, Cantarell, "Open Sans", "Helvetica Neue", sans-serif;--font-size-xs: 8px;--font-size-sm: 9px;--font-size-base: 11px;--font-size-md: 16px;--font-size-lg: 18px;--font-size-xl: 20px;--font-size-2xl: 22px;--font-size-3xl: 24px;--font-size-4xl: 28px;--font-weight-light: 300;--font-weight-normal: 400;--font-weight-medium: 500;--font-weight-semibold: 600;--font-weight-bold: 700;--line-height-tight: 1.25;--line-height-normal: 1.5;--line-height-relaxed: 1.75;--primary-color: #380ac1;--primary-dark-blue: #2a2e70;--primary-medium-blue: #4c68c0;--secondary-color: orangered;--primary-dark-Violet: #4e2a70;--primary-medium-Violet: #7c4cc0;--neutral-50: #f8fafc;--neutral-100: #f1f5f9;--neutral-200: #e2e8f0;--neutral-300: #cbd5e1;--neutral-400: #94a3b8;--neutral-500: #64748b;--neutral-600: #475569;--neutral-700: #334155;--neutral-800: #1e293b;--neutral-900: #0f172a;--success-color: #10b981;--success-dark: #059669;--success-50: #f0fdf4;--success-200: #bbf7d0;--success-700: #15803d;--warning-50: #fffbeb;--warning-200: #fed7aa;--warning-700: #c2410c;--error-50: #fef2f2;--error-200: #fecaca;--error-700: #b91c1c;--info: #3b82f6;--info-50: #eff6ff;--info-200: #bfdbfe;--info-700: #1d4ed8;--light-purple-gray: #dad1de;--dark-blue-gray: #555269;--white: #ffffff;--parties-color: #1a365d;--parties-dark: #2d3748;--identification-color: #744210;--identification-dark: #553c0f;--location-color: #1a202c;--location-dark: #2d3748;--timeline-color: #f7fafc;--timeline-dark: #e2e8f0;--theme-bg-primary: #ffffff;--theme-bg-secondary: var(--neutral-100);--theme-bg-tertiary: #f1f5f9;--theme-text-primary: #1e293b;--theme-text-secondary: #475569;--theme-text-tertiary: #64748b;--theme-border-primary: #e2e8f0;--theme-border-secondary: #cbd5e1;--theme-shadow-light: rgba(0, 0, 0, .05);--theme-shadow-medium: rgba(0, 0, 0, .1);--theme-shadow-heavy: rgba(0, 0, 0, .15);--dark-bg-primary: #0f172a;--dark-bg-secondary: #432121;--dark-bg-tertiary: #334155;--dark-text-primary: #f8fafc;--dark-text-secondary: #e2e8f0;--dark-text-tertiary: #cbd5e1;--dark-border-primary: #334155;--dark-border-secondary: #475569;--dark-shadow-light: rgba(0, 0, 0, .3);--dark-shadow-medium: rgba(0, 0, 0, .5);--dark-shadow-heavy: rgba(0, 0, 0, .7);--space-1: 4px;--space-2: 8px;--space-3: 12px;--space-4: 16px;--space-5: 20px;--space-6: 24px;--space-8: 32px;--space-10: 40px;--space-12: 48px;--radius-sm: 4px;--radius-md: 8px;--radius-lg: 12px;--radius-xl: 16px;--shadow-sm: 0 1px 2px rgba(0, 0, 0, .05);--shadow-md: 0 4px 6px rgba(0, 0, 0, .1);--shadow-lg: 0 10px 15px rgba(0, 0, 0, .1);--shadow-light: rgba(42, 46, 112, .1);--shadow-medium: rgba(42, 46, 112, .2);--shadow-heavy: rgba(42, 46, 112, .3);--transition-fast: .15s ease;--transition-normal: .3s ease;--transition-slow: .5s ease}@keyframes float{0%,to{transform:translateY(0) rotate(0)}50%{transform:translateY(-20px) rotate(2deg)}}@keyframes pulse{0%,to{transform:scale(1);opacity:.9}50%{transform:scale(1.05);opacity:1}}@keyframes modernSpin{0%{transform:rotate(0)}to{transform:rotate(360deg)}}@keyframes rotate{0%{transform:rotate(0)}to{transform:rotate(360deg)}}@keyframes shimmer{0%{transform:rotate(45deg) translate(-100%,-100%)}50%{transform:rotate(45deg) translate(0)}to{transform:rotate(45deg) translate(100%,100%)}}@keyframes slideDown{0%{opacity:0;transform:translateY(-10px)}to{opacity:1;transform:translateY(0)}}@keyframes slideIn{0%{opacity:0;transform:translate(-20px)}to{opacity:1;transform:translate(0)}}@keyframes fadeIn{0%{opacity:0}to{opacity:1}}:root{--gradient-primary: linear-gradient(135deg, var(--primary-color), var(--primary-dark));--gradient-primary-reverse: linear-gradient(135deg, var(--primary-dark), var(--primary-color));--gradient-secondary: linear-gradient(135deg, var(--secondary-color), var(--secondary-dark));--gradient-bg-light: linear-gradient(135deg, var(--neutral-50) 0%, var(--neutral-200) 100%);--gradient-bg-white: linear-gradient(135deg, #ffffff, #f8fafc);--gradient-button-primary: linear-gradient(135deg, var(--primary-medium-blue) 0%, var(--primary-dark-blue) 100%);--gradient-button-success: linear-gradient(135deg, #28a745, #20c997);--gradient-button-danger: linear-gradient(135deg, #dc3545, #c82333);--gradient-parties: linear-gradient(135deg, var(--parties-color), var(--parties-dark));--gradient-identification: linear-gradient(135deg, var(--identification-color), var(--identification-dark));--gradient-location: linear-gradient(135deg, var(--location-color), var(--location-dark));--padding-button: 12px 20px;--padding-card: 20px;--padding-section: 16px 20px;--padding-form: 15px 20px;--border-radius-sm: 8px;--border-radius-md: 12px;--border-radius-lg: 15px;--border-radius-xl: 20px;--box-shadow-sm: 0 1px 3px rgba(0, 0, 0, .1);--box-shadow-md: 0 2px 8px rgba(0, 0, 0, .1);--box-shadow-lg: 0 4px 12px rgba(0, 0, 0, .15);--box-shadow-xl: 0 8px 25px rgba(0, 0, 0, .2);--box-shadow-primary: 0 2px 8px rgba(76, 104, 192, .2);--box-shadow-danger: 0 2px 8px rgba(220, 38, 38, .1);--transition-all: all .3s ease;--transition-transform: transform .3s ease;--transition-opacity: opacity .3s ease;--transition-colors: background-color .3s ease, color .3s ease;--transform-hover-up: translateY(-2px);--transform-hover-scale: scale(1.05);--spacing-xs: 4px;--spacing-sm: 8px;--spacing-md: 12px;--spacing-lg: 16px;--spacing-xl: 24px;--spacing-2xl: 32px;--radius-full: 50%;--primary-dark: #524b6c;--primary-light: #dbeafe;--animation-shimmer: shimmer 3s infinite;--animation-slide-down: slideDown .2s ease-out;--animation-slide-in: slideIn .5s ease;--animation-fade-in: fadeIn .3s ease-in-out;--animation-spin: modernSpin 1s linear infinite;--breakpoint-mobile: 480px;--breakpoint-tablet: 768px;--breakpoint-desktop: 1024px;--breakpoint-large: 1200px;--page-background: var(--neutral-100);--page-background-alt: var(--neutral-50);--card-bg: white;--card-border: 1px solid var(--neutral-200);--card-border-radius: var(--border-radius-md);--card-shadow: var(--box-shadow-md);--card-shadow-hover: var(--box-shadow-lg);--card-padding: 1.5rem;--card-header-bg: linear-gradient(135deg, #014871 0%, #4a8fa3 50%, #d7ede2 100%);--card-header-padding: var(--padding-section);--btn-padding: 12px 20px;--btn-padding-sm: 8px 16px;--btn-padding-lg: 16px 32px;--btn-border-radius: var(--radius-md);--btn-font-size: var(--font-size-md);--btn-font-weight: var(--font-weight-medium);--btn-transition: all var(--transition-normal);--btn-min-height: 44px;--btn-gap: 8px;--btn-primary-bg: var(--primary-color);--btn-primary-bg-hover: var(--primary-dark-blue);--btn-primary-color: var(--white);--btn-primary-shadow: var(--box-shadow-primary);--btn-primary-gradient: linear-gradient(135deg, var(--primary-medium-blue) 0%, var(--primary-dark-blue) 100%);--btn-secondary-bg: var(--secondary-color);--btn-secondary-bg-hover: var(--secondary-dark);--btn-secondary-color: var(--white);--btn-secondary-shadow: var(--box-shadow-md);--btn-danger-bg: #dc3545;--btn-danger-bg-hover: #c82333;--btn-danger-color: var(--white);--btn-danger-shadow: var(--box-shadow-danger);--btn-neutral-bg: var(--current-bg-tertiary);--btn-neutral-bg-hover: var(--current-bg-primary);--btn-neutral-color: var(--current-text-primary);--btn-neutral-border: 1px solid var(--current-border-primary);--btn-icon-size: 45px;--btn-icon-size-sm: 36px;--btn-icon-size-lg: 50px;--btn-icon-bg: var(--current-bg-secondary);--btn-icon-border: 1px solid var(--current-border-primary);--input-padding: 12px 16px;--input-padding-sm: 8px 12px;--input-padding-lg: 16px 20px;--input-border-radius: var(--radius-md);--input-font-size: var(--font-size-base);--input-min-height: 44px;--input-transition: all var(--transition-normal);--input-bg: var(--current-bg-primary);--input-bg-focus: var(--current-bg-primary);--input-border: 1px solid var(--current-border-primary);--input-border-focus: 1px solid var(--primary-color);--input-color: var(--current-text-primary);--input-placeholder: var(--current-text-secondary);--input-shadow-focus: 0 0 0 3px rgba(37, 99, 235, .1);--input-border-error: 1px solid #dc3545;--input-border-success: 1px solid var(--success-color);--loading-spinner-size: 40px;--loading-spinner-size-sm: 20px;--loading-spinner-size-lg: 60px;--loading-spinner-border: 3px solid var(--neutral-200);--loading-spinner-border-top: 3px solid var(--primary-color);--loading-animation: spin 1s linear infinite;--card-header-border-radius: var(--border-radius-lg) var(--border-radius-lg) 0 0;--card-header-text-color: white;--card-header-text-shadow: 0 1px 2px rgba(0, 0, 0, .2);--card-content-bg: linear-gradient(135deg, rgba(1, 72, 113, .08) 0%, rgba(74, 143, 163, .12) 50%, rgba(215, 237, 226, .15) 100%);--card-content-border-radius: 0 0 var(--border-radius-lg) var(--border-radius-lg)}*{font-family:-apple-system,BlinkMacSystemFont,Segoe UI,Roboto,Oxygen,Ubuntu,Cantarell,Open Sans,Helvetica Neue,sans-serif}body,body.theme-light{--current-bg-primary: var(--theme-bg-primary);--current-bg-secondary: var(--theme-bg-secondary);--current-bg-tertiary: var(--theme-bg-tertiary);--current-text-primary: var(--theme-text-primary);--current-text-secondary: var(--theme-text-secondary);--current-text-tertiary: var(--theme-text-tertiary);--current-border-primary: var(--theme-border-primary);--current-border-secondary: var(--theme-border-secondary);--current-shadow-light: var(--theme-shadow-light);--current-shadow-medium: var(--theme-shadow-medium);--current-shadow-heavy: var(--theme-shadow-heavy)}body.theme-dark{--current-bg-primary: var(--dark-bg-primary);--current-bg-secondary: var(--dark-bg-secondary);--current-bg-tertiary: var(--dark-bg-tertiary);--current-text-primary: var(--dark-text-primary);--current-text-secondary: var(--dark-text-secondary);--current-text-tertiary: var(--dark-text-tertiary);--current-border-primary: var(--dark-border-primary);--current-border-secondary: var(--dark-border-secondary);--current-shadow-light: var(--dark-shadow-light);--current-shadow-medium: var(--dark-shadow-medium);--current-shadow-heavy: var(--dark-shadow-heavy)}body{background-color:var(--current-bg-secondary);color:var(--current-text-primary);transition:background-color .3s ease,color .3s ease}.btn{padding:var(--btn-padding);border-radius:var(--btn-border-radius);font-size:var(--btn-font-size);font-weight:var(--btn-font-weight);font-family:var(--font-family-primary);border:none;cursor:pointer;transition:var(--btn-transition);min-height:var(--btn-min-height);display:inline-flex;align-items:center;justify-content:center;gap:var(--btn-gap);text-decoration:none;box-sizing:border-box;position:relative;overflow:hidden}.btn:disabled{opacity:.6;cursor:not-allowed;transform:none!important}.btn-sm{padding:var(--btn-padding-sm);font-size:var(--font-size-sm);min-height:36px}.btn-lg{padding:var(--btn-padding-lg);font-size:var(--font-size-lg);min-height:52px}.btn-primary{background:var(--btn-primary-gradient);color:var(--btn-primary-color);box-shadow:var(--btn-primary-shadow)}.btn-primary:hover:not(:disabled){background:var(--btn-primary-bg-hover);transform:var(--transform-hover-up);box-shadow:var(--box-shadow-lg)}.btn-secondary{background-color:var(--btn-secondary-bg);color:var(--btn-secondary-color);box-shadow:var(--btn-secondary-shadow)}.btn-secondary:hover:not(:disabled){background-color:var(--btn-secondary-bg-hover);transform:var(--transform-hover-up)}.btn-danger{background-color:var(--btn-danger-bg);color:var(--btn-danger-color);box-shadow:var(--btn-danger-shadow)}.btn-danger:hover:not(:disabled){background-color:var(--btn-danger-bg-hover);transform:var(--transform-hover-up)}.btn-neutral{background-color:var(--btn-neutral-bg);color:var(--btn-neutral-color);border:var(--btn-neutral-border)}.btn-neutral:hover:not(:disabled){background-color:var(--btn-neutral-bg-hover);transform:var(--transform-hover-up)}.btn-icon{width:var(--btn-icon-size);height:var(--btn-icon-size);padding:0;border-radius:var(--radius-full);background-color:var(--btn-icon-bg);border:var(--btn-icon-border);color:var(--current-text-secondary)}.btn-icon:hover:not(:disabled){background-color:var(--current-bg-tertiary);color:var(--current-text-primary);transform:var(--transform-hover-scale);border-color:var(--primary-color);box-shadow:var(--current-shadow-light)}.btn-icon-sm{width:var(--btn-icon-size-sm);height:var(--btn-icon-size-sm)}.btn-icon-lg{width:var(--btn-icon-size-lg);height:var(--btn-icon-size-lg)}.form-input{padding:var(--input-padding);border-radius:var(--input-border-radius);font-size:var(--input-font-size);font-family:var(--font-family-primary);min-height:var(--input-min-height);width:100%;box-sizing:border-box;transition:var(--input-transition);background-color:var(--input-bg);border:var(--input-border);color:var(--input-color);direction:rtl}.form-input::placeholder{color:var(--input-placeholder);opacity:.7}.form-input:focus{outline:none;background-color:var(--input-bg-focus);border:var(--input-border-focus);box-shadow:var(--input-shadow-focus)}.form-input-sm{padding:var(--input-padding-sm);font-size:var(--font-size-sm);min-height:36px}.form-input-lg{padding:var(--input-padding-lg);font-size:var(--font-size-lg);min-height:52px}.form-input-error{border:var(--input-border-error)}.form-input-error:focus{border:var(--input-border-error);box-shadow:0 0 0 3px #dc26261a}.form-input-success{border:var(--input-border-success)}.form-input-success:focus{border:var(--input-border-success);box-shadow:0 0 0 3px #10b9811a}.form-group{display:flex;flex-direction:column;gap:var(--spacing-sm);margin-bottom:var(--spacing-lg)}.form-label{font-size:var(--font-size-sm);font-weight:var(--font-weight-medium);color:var(--current-text-primary);margin-bottom:var(--spacing-xs)}.form-error{font-size:var(--font-size-sm);color:#dc3545;margin-top:var(--spacing-xs)}.form-success{font-size:var(--font-size-sm);color:var(--success-color);margin-top:var(--spacing-xs)}.loading-spinner{width:var(--loading-spinner-size);height:var(--loading-spinner-size);border:var(--loading-spinner-border);border-top:var(--loading-spinner-border-top);border-radius:50%;animation:var(--loading-animation);margin:0 auto}.loading-spinner-sm{width:var(--loading-spinner-size-sm);height:var(--loading-spinner-size-sm);border-width:2px}.loading-spinner-lg{width:var(--loading-spinner-size-lg);height:var(--loading-spinner-size-lg);border-width:4px}@keyframes spin{0%{transform:rotate(0)}to{transform:rotate(360deg)}}.case-registration-page{min-height:100vh;font-family:Inter,-apple-system,BlinkMacSystemFont,system-ui,sans-serif;direction:rtl;color:var(--text-primary)}.main-container{display:flex;flex-direction:column;gap:16px}.registration-card{width:85%;max-width:1200px;min-height:calc(100vh - 160px);margin:40px auto;background:var(--page-background);border-radius:20px;box-shadow:0 20px 60px var(--shadow-medium),0 10px 30px var(--shadow-light);border:none;transition:all var(--transition-normal);overflow:hidden;display:flex;flex-direction:column;position:relative}.registration-card:hover{box-shadow:0 25px 70px var(--shadow-medium),0 15px 40px var(--shadow-light);transform:translateY(-4px)}.card-header{margin-bottom:16px;text-align:right;padding:24px 32px 0}.header-content h1{font-weight:700;text-align:right;line-height:1.2;background:linear-gradient(135deg,var(--primary-dark-blue) 0%,var(--primary-medium-blue) 100%);-webkit-background-clip:text;-webkit-text-fill-color:transparent;background-clip:text}.transfer-badge{background:#fff3;border:1px solid rgba(255,255,255,.3);border-radius:50px;padding:.5rem 1rem;margin-top:1rem;display:inline-block}.transfer-badge span{font-size:.9rem;font-weight:500}.message{display:flex;align-items:center;gap:.75rem;padding:1rem 1.5rem;margin:1rem 2rem;border-radius:var(--border-radius);font-weight:500;transition:var(--transition)}.message-icon{font-size:1.25rem;flex-shrink:0}.message-text{flex:1}.error-message{background:var(--error-light);color:var(--error-color);border:1px solid #fecaca}.success-message{background:var(--success-light);color:var(--success-color);border:1px solid #a7f3d0}.loading-message{background:var(--primary-light);color:var(--primary-color);border:1px solid #bfdbfe}.loading-spinner{width:1.25rem;height:1.25rem;border:2px solid var(--primary-light);border-top:2px solid var(--primary-color);border-radius:50%;animation:var(--loading-animation)}.registration-form{display:flex;flex-direction:column;gap:24px;padding:32px;width:100%;flex:1}.form-body{display:flex;flex-direction:column;gap:12px;width:100%;align-items:stretch}.input-row{display:flex;flex-wrap:wrap;gap:8px;margin-bottom:8px}.input-field{flex:1 1 30%;min-width:220px;display:flex;flex-direction:column;gap:4px;margin-left:0;margin-right:0}.input-field.full-width{flex:1 1 100%;max-width:100%}.input-field label{font-size:.875rem;font-weight:600;color:var(--text-primary);display:flex;align-items:center;gap:.25rem}.label{display:block;font-size:.95rem;font-weight:600;color:#4a5568;margin-bottom:4px}.input{width:100%;height:48px;background-color:#fff;border-radius:12px;padding:0 16px;border:2px solid #ffffff;font-size:1rem;color:#1a202c;transition:all .2s ease;outline:none;box-shadow:0 20px 60px var(--shadow-medium),0 10px 30px var(--shadow-light)}.input:hover{border-color:#cbd5e0}.input:focus{border-color:var(--secondary-color)}select.input{-webkit-appearance:none;-moz-appearance:none;appearance:none;background-image:url("data:image/svg+xml,%3Csvg xmlns='http://www.w3.org/2000/svg' fill='none' viewBox='0 0 24 24' stroke='%234A5568'%3E%3Cpath stroke-linecap='round' stroke-linejoin='round' stroke-width='2' d='M19 9l-7 7-7-7'%3E%3C/path%3E%3C/svg%3E");background-repeat:no-repeat;background-position:left 12px center;background-size:20px;padding-left:40px;cursor:pointer}input[type=date].input{padding-right:12px;min-height:48px}.input-field input:disabled,.input-field select:disabled{background:var(--background);color:var(--text-muted);cursor:not-allowed}.input-field textarea{resize:vertical;min-height:120px;line-height:1.5}textarea.input{min-height:44px;max-height:200px;height:44px;resize:vertical;padding:12px 16px;font-size:1rem;border-radius:12px;border:2px solid #e2e8f0;background:#fff;transition:border .2s}.case-number-container{background:#f8fafc;border-radius:16px;padding:24px;width:100%;border:1px solid #e2e8f0}.number-inputs{display:flex;flex-direction:row;align-items:center;gap:8px;width:100%;max-width:500px;margin:0 auto}.number-input,.year-input{padding:.75rem 1rem;border:2px solid var(--border-color);border-radius:var(--border-radius);font-size:1.125rem;text-align:center;font-weight:600;background:var(--card-background);transition:var(--transition);min-width:120px;flex:1 1 0}.number-input:focus,.year-input:focus{outline:none;border-color:var(--secondary-color);box-shadow:0 0 0 3px var(--secondary-color)}.separator{font-size:1.5rem;font-weight:600;color:var(--text-secondary);margin:0 2px;padding:0;flex-shrink:0}.final-buttons{display:flex;flex-direction:row;gap:12px;justify-content:flex-end;margin-top:12px}@media (max-width: 768px){.registration-card{margin:16px;width:calc(100% - 32px);min-height:auto}.registration-form{padding:20px}.form-body{gap:20px}.number-inputs{flex-direction:column;gap:8px;max-width:100%}.separator{display:none}.input{height:52px}select.input{padding-left:16px;background-position:left 16px center}.case-number-container{padding:20px}.final-buttons{flex-direction:column;gap:16px}.submit-btn,.cancel-btn{width:100%}.input-row{flex-direction:column;gap:8px}.input-field,.input-field.full-width{min-width:100%;max-width:100%;margin-left:0;margin-right:0}.final-buttons{flex-direction:column;align-items:stretch;gap:10px}.submit-btn,.cancel-btn{max-width:100%;min-width:100%}}@media (max-width: 768px){.card-header{padding:20px 24px 0}.header-content h1{font-size:2rem}}@media (max-width: 480px){.card-header{padding:16px 20px 0}.header-content h1{font-size:1.8rem}}@media print{.case-registration-page{background:#fff}.registration-card{box-shadow:none;border:1px solid var(--border-color)}}
