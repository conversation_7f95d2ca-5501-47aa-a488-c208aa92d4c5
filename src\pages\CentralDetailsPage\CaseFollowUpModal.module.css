@import '../../styles/variables.css';

/* الأنماط العامة للنافذة */
.modalContent {
  display: flex;
  flex-direction: column;
  max-height: 90vh;
  background: var(--page-background);
}
.header {
  display: flex;
  /* تم تغيير justify-content لتوسيط العنوان بعد حذف الزر */
  justify-content: center; 
  align-items: center;
  padding: 1rem 1.5rem;
  border-bottom: 1px solid var(--neutral-200);
  flex-shrink: 0;
  position: relative; /* لإبقاء العنوان في المنتصف */
}
.title {
  font-size: 1.3rem;
  font-weight: 600;
  color: var(--primary-color);
  margin: 0;
}
/* تم حذف .closeButton */

.content {
  padding: 1.5rem;
  overflow-y: auto;
  flex-grow: 1;
}

.sectionTitle {
    font-size: 1.1rem;
    color: var(--neutral-800);
    margin-bottom: 1rem;
    font-weight: 600;
    padding-bottom: 0.5rem;
    border-bottom: 1px solid var(--neutral-200);
}

/* أنماط عرض الملاحظات المحفوظة */
.savedNotesContainer {
  margin-bottom: 2rem;
}

.notesGrid {
  display: flex;
  flex-wrap: wrap;
  gap: 10px;
  justify-content: flex-start;
}

.noteChip {
  display: flex;
  align-items: center;
  gap: 8px;
  background: var(--neutral-100);
  border: 1px solid var(--neutral-200);
  border-radius: 16px;
  padding: 8px 12px; /* تعديل الحشو ليكون متساوياً */
  font-size: 0.9rem;
  color: var(--neutral-800);
  transition: all 0.2s ease;
  max-width: 100%;
  cursor: pointer; /* تغيير المؤشر للإشارة إلى إمكانية التفاعل */
  user-select: none; /* لمنع تحديد النص أثناء الضغط */
}

.noteChip:hover {
  border-color: var(--neutral-300);
  box-shadow: 0 2px 8px rgba(0,0,0,0.08);
}

/* تأثير بسيط عند الضغط */
.noteChip:active {
    transform: scale(0.97);
    background-color: var(--neutral-200);
}

.chipContent {
  display: flex;
  align-items: baseline;
  gap: 5px;
  flex-grow: 1;
  word-break: break-word;
}

.chipType {
  font-weight: 700;
  color: var(--primary-color);
  white-space: nowrap;
}

.chipText {
  line-height: 1.5;
}

/* تم حذف .chipDeleteButton */

.noNotes {
  text-align: center;
  color: var(--neutral-500);
  padding: 2rem 0;
}

/* أنماط إضافة ملاحظة جديدة */
.addNoteContainer {
  /* لا تغيير هنا */
}
.textarea {
  width: 100%;
  padding: 0.75rem;
  border: 1px solid var(--neutral-300);
  border-radius: 8px;
  font-size: 1rem;
  margin-bottom: 1rem;
}
.textarea:focus {
  outline: none;
  border-color: var(--primary-color);
  box-shadow: 0 0 0 3px rgba(59, 130, 246, 0.2);
}

/* أنماط أزرار الحفظ والإلغاء */
.actionsContainer {
  display: flex;
  justify-content: flex-end;
  gap: 12px;
}
.actionButton {
  display: flex;
  align-items: center;
  justify-content: center;
  gap: 8px;
  padding: 0.6rem 1.2rem;
  border: none;
  border-radius: 8px;
  font-size: 0.95rem;
  font-weight: 600;
  cursor: pointer;
}
.saveButton {
  background: var(--primary-color);
  color: white;
}
.cancelButton {
  background: var(--neutral-200);
  color: var(--neutral-700);
}
