/* إعدادات الإشعارات الخارجية */

.container {
  background: white;
  border-radius: 12px;
  box-shadow: 0 4px 20px rgba(0, 0, 0, 0.1);
  max-width: 600px;
  margin: 0 auto;
  overflow: hidden;
  direction: rtl;
}

.header {
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
  color: white;
  padding: 20px;
  display: flex;
  justify-content: space-between;
  align-items: center;
}

.header h3 {
  margin: 0;
  display: flex;
  align-items: center;
  gap: 10px;
  font-size: 1.2rem;
}

.headerIcon {
  font-size: 1.3rem;
}

.closeButton {
  background: rgba(255, 255, 255, 0.2);
  border: none;
  color: white;
  padding: 8px;
  border-radius: 50%;
  cursor: pointer;
  transition: background-color 0.3s ease;
  display: flex;
  align-items: center;
  justify-content: center;
  width: 36px;
  height: 36px;
}

.closeButton:hover {
  background: rgba(255, 255, 255, 0.3);
}

.content {
  padding: 20px;
}

.loading {
  text-align: center;
  padding: 40px 20px;
  color: #666;
}

.loading p {
  margin-top: 15px;
  font-size: 1rem;
}

/* أقسام المحتوى */
.infoSection,
.actionsSection,
.helpSection {
  margin-bottom: 25px;
  padding-bottom: 20px;
  border-bottom: 1px solid #eee;
}

.infoSection:last-child,
.actionsSection:last-child,
.helpSection:last-child {
  border-bottom: none;
  margin-bottom: 0;
  padding-bottom: 0;
}

.infoSection h4,
.actionsSection h4,
.helpSection h4 {
  color: #333;
  margin: 0 0 15px 0;
  font-size: 1.1rem;
  font-weight: 600;
}

/* شبكة المعلومات */
.infoGrid {
  display: grid;
  gap: 12px;
}

.infoItem {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 10px;
  background: #f8f9fa;
  border-radius: 8px;
}

.infoLabel {
  font-weight: 500;
  color: #555;
}

.infoValue {
  display: flex;
  align-items: center;
  gap: 6px;
  font-weight: 600;
}

.supported {
  color: #28a745;
}

.notSupported {
  color: #dc3545;
}

.statusGranted {
  color: #28a745;
}

.statusDenied {
  color: #dc3545;
}

.statusDefault {
  color: #ffc107;
}

/* أزرار الإجراءات */
.actionButtons {
  display: flex;
  flex-direction: column;
  gap: 12px;
}

.actionButton {
  display: flex;
  align-items: center;
  justify-content: center;
  gap: 8px;
  padding: 12px 20px;
  border: none;
  border-radius: 8px;
  font-size: 1rem;
  font-weight: 500;
  cursor: pointer;
  transition: all 0.3s ease;
  min-height: 48px;
}

.primaryButton {
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
  color: white;
}

.primaryButton:hover:not(:disabled) {
  transform: translateY(-2px);
  box-shadow: 0 4px 12px rgba(102, 126, 234, 0.4);
}

.secondaryButton {
  background: #f8f9fa;
  color: #495057;
  border: 2px solid #dee2e6;
}

.secondaryButton:hover:not(:disabled) {
  background: #e9ecef;
  border-color: #adb5bd;
}

.actionButton:disabled {
  opacity: 0.6;
  cursor: not-allowed;
  transform: none !important;
  box-shadow: none !important;
}

/* رسالة التحذير */
.warningMessage {
  display: flex;
  align-items: flex-start;
  gap: 12px;
  padding: 15px;
  background: #fff3cd;
  border: 1px solid #ffeaa7;
  border-radius: 8px;
  color: #856404;
}

.warningMessage svg {
  color: #f39c12;
  margin-top: 2px;
  flex-shrink: 0;
}

.warningMessage p {
  margin: 0;
  line-height: 1.5;
}

/* قسم الرسائل */
.messageSection {
  margin-top: 15px;
}

.message {
  padding: 12px 15px;
  border-radius: 8px;
  margin: 0;
  font-weight: 500;
  background: #d1ecf1;
  color: #0c5460;
  border: 1px solid #bee5eb;
}

/* قسم المساعدة */
.helpList {
  margin: 0;
  padding-right: 20px;
  color: #666;
}

.helpList li {
  margin-bottom: 8px;
  line-height: 1.5;
}

.helpList li:last-child {
  margin-bottom: 0;
}

/* أيقونة الدوران */
.spinningIcon {
  animation: spin 1s linear infinite;
}

@keyframes spin {
  from {
    transform: rotate(0deg);
  }
  to {
    transform: rotate(360deg);
  }
}

/* تصميم متجاوب */
@media (max-width: 768px) {
  .container {
    margin: 10px;
    border-radius: 8px;
  }

  .header {
    padding: 15px;
  }

  .header h3 {
    font-size: 1.1rem;
  }

  .content {
    padding: 15px;
  }

  .infoItem {
    flex-direction: column;
    align-items: flex-start;
    gap: 5px;
  }

  .actionButtons {
    gap: 10px;
  }

  .actionButton {
    padding: 10px 16px;
    font-size: 0.95rem;
  }
}