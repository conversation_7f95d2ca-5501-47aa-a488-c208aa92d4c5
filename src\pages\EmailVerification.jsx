import React, { useState, useEffect } from 'react';
import { useNavigate, useLocation } from 'react-router-dom';
import { auth } from '../config/firebaseConfig';
import { sendEmailVerification, onAuthStateChanged, signOut, applyActionCode } from 'firebase/auth';
import LoadingSpinner from '../components/ui/LoadingSpinner';
import './EmailVerification.css'; // سنقوم بإنشاء هذا الملف لاحقًا

function EmailVerification() {
  const [loading, setLoading] = useState(false);
  const [errorMessage, setErrorMessage] = useState('');
  const [successMessage, setSuccessMessage] = useState('');
  const navigate = useNavigate();
  const location = useLocation();

  useEffect(() => {
    const queryParams = new URLSearchParams(location.search);
    const mode = queryParams.get('mode');
    const oobCode = queryParams.get('oobCode');

    if (mode === 'verifyEmail' && oobCode) {
      setLoading(true);
      applyActionCode(auth, oobCode)
        .then(() => {
          setSuccessMessage('تم التحقق من بريدك الإلكتروني بنجاح! سيتم توجيهك إلى لوحة التحكم.');
          setTimeout(() => navigate('/welcome', { replace: true }), 3000);
        })
        .catch((error) => {
          console.error('خطأ في التحقق من البريد الإلكتروني:', error);
          let userFacingMessage = 'حدث خطأ أثناء التحقق من بريدك الإلكتروني. يرجى المحاولة مرة أخرى.';
          switch (error.code) {
            case 'auth/invalid-action-code':
              userFacingMessage = 'رمز التحقق غير صالح أو انتهت صلاحيته.';
              break;
            case 'auth/user-disabled':
              userFacingMessage = 'تم تعطيل حساب المستخدم هذا.';
              break;
            default:
              userFacingMessage = 'حدث خطأ غير متوقع أثناء التحقق من البريد الإلكتروني.';
          }
          setErrorMessage(userFacingMessage);
          setLoading(false);
        });
    } else {
      // المنطق الحالي لمراقبة حالة التحقق إذا لم يكن هناك رمز في الرابط
      const unsubscribe = onAuthStateChanged(auth, (user) => {
        if (user) {
          if (user.emailVerified) {
            navigate('/welcome', { replace: true });
          } else {
            const intervalId = setInterval(async () => {
              try {
                await user.reload();
                if (auth.currentUser.emailVerified) {
                  clearInterval(intervalId);
                  navigate('/welcome', { replace: true });
                }
              } catch (error) {
                console.error("Error reloading user for verification check:", error);
                clearInterval(intervalId);
              }
            }, 3000);
            return () => clearInterval(intervalId);
          }
        } else {
          navigate('/login', { replace: true });
        }
      });
      return () => unsubscribe();
    }
  }, [navigate, location.search]);

  const handleResendVerificationEmail = async () => {
    const user = auth.currentUser;
    if (user) {
      setLoading(true);
      setErrorMessage('');
      setSuccessMessage('');
      try {
        await sendEmailVerification(user);
        setSuccessMessage('تم إعادة إرسال بريد التحقق بنجاح. يرجى التحقق من صندوق الوارد أو البريد العشوائي.');
      } catch (error) {
        console.error('خطأ في إعادة إرسال بريد التحقق:', error);
        let userFacingMessage = 'حدث خطأ أثناء إعادة إرسال بريد التحقق. يرجى المحاولة مرة أخرى.';
        switch (error.code) {
          case 'auth/too-many-requests':
            userFacingMessage = 'لقد تجاوزت الحد الأقصى لعدد محاولات إعادة الإرسال. يرجى الانتظار لمدة 5 دقائق والمحاولة مرة أخرى.';
            break;
          default:
            userFacingMessage = 'حدث خطأ أثناء إعادة إرسال بريد التحقق. يرجى المحاولة مرة أخرى.';
        }
        setErrorMessage(userFacingMessage);
      } finally {
        setLoading(false);
      }
    } else {
      setErrorMessage('لا يوجد مستخدم مسجل دخول لإعادة إرسال بريد التحقق.');
      setTimeout(() => navigate('/login'), 2000);
    }
  };

  

  const handleLogout = async () => {
    setLoading(true);
    setErrorMessage('');
    setSuccessMessage('');
    try {
      await signOut(auth);
      setSuccessMessage('تم تسجيل الخروج بنجاح. سيتم توجيهك إلى صفحة تسجيل الدخول.');
      setTimeout(() => navigate('/login', { replace: true }), 1500);
    } catch (error) {
      console.error('خطأ في تسجيل الخروج:', error);
      setErrorMessage('حدث خطأ أثناء تسجيل الخروج. يرجى المحاولة مرة أخرى.');
    } finally {
      setLoading(false);
    }
  };

  return (
    <div className="email-verification-container">
      <div className="email-verification-card">
        <h2>التحقق من البريد الإلكتروني</h2>
        <p>
          لقد قمنا بإرسال رابط تحقق إلى بريدك الإلكتروني. يرجى النقر على الرابط الموجود في الرسالة لتفعيل حسابك.
        </p>
        <p>
          إذا لم تستلم البريد الإلكتروني، يرجى التحقق من مجلد البريد العشوائي (Spam/Junk).
        </p>

        {loading && <LoadingSpinner message="جاري المعالجة..." />}
        {errorMessage && <div className="error-message">{errorMessage}</div>}
        {successMessage && <div className="success-message">{successMessage}</div>}

        <div className="button-group">
          <button onClick={handleResendVerificationEmail} disabled={loading}>
            إعادة إرسال بريد التحقق
          </button>
          
          <button onClick={handleLogout} disabled={loading} className="logout-button">
            تسجيل الخروج
          </button>
        </div>
      </div>
    </div>
  );
}

export default EmailVerification;
