import{r as n,c as q,j as a}from"./index-HUlFBKIW.js";import{T as E}from"./TopBar-rd4Lqjij.js";import{c as y,a as N,b as T}from"./CaseFilters-CPt9izwP.js";import{b as A,g as O}from"./PermissionsService-Dhc9PbZp.js";import{notifyCaseCreated as F}from"./CacheManager-c0VPvbsl.js";import"./iconBase-BPj5F03O.js";const H=({casesList:L=[],setCasesList:R=()=>{},currentUser:l})=>{var S;console.log("CaseRegistration component rendered");const g=new Date().getFullYear().toString(),[e,c]=n.useState({caseNumber:"",caseYear:g,clientName:"",caseDescription:"",caseCategory:"",opponentName:"",caseDegree:"",circleNumber:"",courtLocation:"",caseStatus:"قيد النظر",reportNumber:"",reportLocation:"",caseDate:"",originalCaseId:"",originalCaseDegree:"",originalCaseNumber:""}),[B,u]=n.useState(""),[b,j]=n.useState(!1),[h,C]=n.useState(!1),[v,r]=n.useState(null),[D,m]=n.useState(null),[d,Y]=n.useState(!1),x=q();n.useEffect(()=>{const t=localStorage.getItem("transferData");if(t)try{const s=JSON.parse(t);c(p=>({...p,clientName:s.clientName||"",caseDescription:s.caseDescription||"",opponentName:s.opponentName||"",caseCategory:s.caseCategory||"",courtLocation:s.courtLocation||"",caseStatus:"دعوى قضائية",originalCaseId:s.originalCaseId||"",originalCaseDegree:s.originalCaseDegree||"",originalCaseNumber:s.originalCaseNumber||"",originalCourtLocation:s.originalCourtLocation||"",originalCaseDate:s.originalCaseDate||"",originalClientName:s.originalClientName||"",originalOpponentName:s.originalOpponentName||"",originalCaseDescription:s.originalCaseDescription||"",originalCaseCategory:s.originalCaseCategory||"",originalCircleNumber:s.originalCircleNumber||""})),Y(!0),localStorage.removeItem("transferData")}catch(s){console.error("خطأ في تحميل بيانات التحويل:",s)}},[]),n.useEffect(()=>{if(!b&&e.courtLocation){const t=y.find(s=>s.name===e.courtLocation);t&&c(s=>({...s,caseDegree:t.degree}))}},[e.courtLocation,b]),n.useEffect(()=>{e.caseDegree&&N[e.caseDegree]&&(N[e.caseDegree].includes(e.caseCategory)||c(t=>({...t,caseCategory:N[e.caseDegree][0]})))},[e.caseDegree]),n.useEffect(()=>{e.caseStatus==="دعوى قضائية"?u(e.caseNumber?`${e.caseNumber}/${e.caseYear}`:e.caseYear):e.caseStatus==="محضر"?u(e.reportNumber?`${e.reportNumber}/${e.caseYear}`:e.caseYear):u("")},[e.caseNumber,e.caseYear,e.reportNumber,e.caseStatus]);const i=t=>{const{name:s,value:p}=t.target;c(f=>({...f,[s]:p})),s==="caseDegree"&&j(!0)},w=async t=>{if(t.preventDefault(),r(null),m(null),!l){alert("يجب تسجيل الدخول لحفظ القضايا."),x("/login");return}if(!e.clientName.trim()){r("يجب إدخال اسم الموكل");return}let s="";if(e.caseStatus==="قيد النظر")s=`قيد النظر-${Date.now()}`;else if(e.caseStatus==="محضر"){if(!e.reportNumber.trim()){r("يجب إدخال رقم المحضر.");return}if(!e.caseYear||!/^\d{4}$/.test(e.caseYear)){r("السنة يجب أن تكون مكونة من 4 أرقام.");return}s=`${e.reportNumber.trim()}/${e.caseYear}`}else if(e.caseStatus==="دعوى قضائية"){if(!e.caseNumber.trim()){r("يجب إدخال رقم القضية.");return}if(!e.caseYear||!/^\d{4}$/.test(e.caseYear)){r("السنة يجب أن تكون مكونة من 4 أرقام.");return}s=`${e.caseNumber.trim()}/${e.caseYear}`}if(e.caseStatus==="دعوى قضائية"){if(!e.circleNumber.trim()){r("يجب إدخال رقم الدائرة.");return}if(!e.caseDegree){r("يجب اختيار درجة الدعوى.");return}if(!e.caseCategory){r("يجب اختيار نوع الدعوى.");return}if(!e.courtLocation){r("يجب اختيار مكان المحكمة.");return}if(!e.caseDescription.trim()){r("يجب إدخال الوصف.");return}if(!e.caseDate.trim()){r("يجب إدخال تاريخ رفع الدعوى.");return}}if(e.caseStatus==="محضر"){if(!e.reportLocation.trim()){r("يجب إدخال مكان الجهة المختصة.");return}if(!e.reportNumber.trim()){r("يجب إدخال رقم المحضر.");return}if(!e.caseDescription.trim()){r("يجب إدخال وصف المحضر.");return}if(!e.caseDate.trim()){r("يجب إدخال تاريخ المحضر.");return}}if(e.caseStatus==="قيد النظر"){if(!e.reportLocation.trim()){r("يجب إدخال مكان الجهة المختصة.");return}if(!e.caseDescription.trim()){r("يجب إدخال الوصف القضائي.");return}}if(!window.confirm("هل أنت متأكد من حف�� القضية؟"))return;const f={fullCaseNumber:s,caseNumber:e.caseStatus==="دعوى قضائية"?e.caseNumber.trim():null,caseYear:e.caseStatus==="قيد النظر"?g:e.caseYear,clientName:e.clientName.trim(),opponentName:e.opponentName.trim()||null,caseDescription:e.caseDescription.trim()||null,caseCategory:e.caseCategory||null,caseDegree:e.caseDegree||null,courtLocation:e.courtLocation||null,circleNumber:e.caseStatus==="دعوى قضائية"&&e.circleNumber.trim()||null,caseDate:(e.caseStatus==="دعوى قضائية"||e.caseStatus==="محضر")&&e.caseDate.trim()||null,caseStatus:e.caseStatus||"قيد النظر",reportNumber:e.caseStatus==="محضر"&&e.reportNumber.trim()||null,reportLocation:(e.caseStatus==="محضر"||e.caseStatus==="قيد النظر")&&e.reportLocation.trim()||null,deferrals:[],actions:[],history:[],createdAt:new Date().toISOString(),updatedAt:new Date().toISOString(),userId:l.uid,originalCaseId:e.originalCaseId||null,originalCaseDegree:e.originalCaseDegree||null,originalCaseNumber:e.originalCaseNumber||null,isTransferredCase:d};C(!0);try{const o=await A(l.uid,f);console.log("Case saved with ID: ",o.id),F(l.uid),c({caseNumber:"",caseYear:g,clientName:"",caseDescription:"",caseCategory:"",opponentName:"",caseDegree:"",circleNumber:"",courtLocation:"",caseStatus:"قيد النظر",reportNumber:"",reportLocation:"",caseDate:""}),u(""),j(!1);const $=O();m(d?"تم تحويل الدرجة القضائية بنجاح!":$==="online"?"تم حفظ القضية بنجاح في الحساب الأونلاين!":"تم حفظ القضية بنجاح في الحساب المحلي!"),setTimeout(()=>{x(`/case-details/${o.id}`)},1500)}catch(o){console.error("Error saving case: ",o),r("حدث خطأ أثناء حفظ القضية: "+o.message)}finally{C(!1)}},I=()=>{x("/dashboard",{state:{refresh:!0}})};return a.jsxs("div",{className:"case-registration-page",children:[a.jsx(E,{currentUser:l,casesList:L}),a.jsx("div",{className:"main-container",children:a.jsxs("div",{className:"registration-card",children:[a.jsx("div",{className:"card-header",children:a.jsxs("div",{className:"header-content",children:[a.jsx("h1",{children:d?"تحويل درجة قضائية":"تسجيل قضية جديدة"}),d&&a.jsx("div",{className:"transfer-badge",children:a.jsxs("span",{children:["تحويل من: ",e.originalCaseDegree," - رقم: ",e.originalCaseNumber]})})]})}),v&&a.jsxs("div",{className:"message error-message",children:[a.jsx("div",{className:"message-icon",children:"⚠️"}),a.jsx("div",{className:"message-text",children:v})]}),D&&a.jsxs("div",{className:"message success-message",children:[a.jsx("div",{className:"message-icon",children:"✅"}),a.jsx("div",{className:"message-text",children:D})]}),h&&a.jsxs("div",{className:"message loading-message",children:[a.jsx("div",{className:"loading-spinner"}),a.jsx("div",{className:"message-text",children:"جاري حفظ القضية..."})]}),a.jsxs("form",{className:"registration-form",onSubmit:w,children:[a.jsxs("div",{className:"form-body",children:[a.jsxs("div",{className:"input-row",children:[a.jsxs("div",{className:"input-field",children:[a.jsx("label",{className:"label",children:"نوع القضية"}),a.jsxs("select",{name:"caseStatus",className:"input",value:e.caseStatus,onChange:t=>c(s=>({...s,caseStatus:t.target.value})),required:!0,children:[a.jsx("option",{value:"قيد النظر",children:"⏳ قيد النظر"}),a.jsx("option",{value:"محضر",children:"📋 محضر"}),a.jsx("option",{value:"دعوى قضائية",children:"⚖️ دعوى قضائية"})]})]}),a.jsxs("div",{className:"input-field",children:[a.jsx("label",{className:"label",children:"اسم الموكل"}),a.jsx("input",{type:"text",name:"clientName",className:"input",value:e.clientName,onChange:i,required:!0,placeholder:"اسم الموكل *"})]}),a.jsxs("div",{className:"input-field",children:[a.jsx("label",{className:"label",children:"اسم الخصم"}),a.jsx("input",{type:"text",name:"opponentName",className:"input",value:e.opponentName,onChange:i,placeholder:"اسم الخصم"})]})]}),a.jsxs("div",{className:"input-row",children:[e.caseStatus==="دعوى قضائية"&&a.jsxs("div",{className:"input-field",children:[a.jsx("label",{className:"label",children:"رقم القضية/السنة"}),a.jsxs("div",{className:"number-inputs",children:[a.jsx("input",{type:"text",name:"caseNumber",value:e.caseNumber,onChange:i,placeholder:"رقم القضية",className:"input"}),a.jsx("span",{className:"separator",children:"/"}),a.jsx("input",{type:"text",name:"caseYear",value:e.caseYear,onChange:i,placeholder:"السنة",className:"input"})]})]}),e.caseStatus==="محضر"&&a.jsxs("div",{className:"input-field",children:[a.jsx("label",{className:"label",children:"رقم المحضر/السنة"}),a.jsxs("div",{className:"number-inputs",children:[a.jsx("input",{type:"text",name:"reportNumber",value:e.reportNumber,onChange:i,placeholder:"رقم المحضر",className:"input"}),a.jsx("span",{className:"separator",children:"/"}),a.jsx("input",{type:"text",name:"caseYear",value:e.caseYear,onChange:i,placeholder:"السنة",className:"input"})]})]}),e.caseStatus==="دعوى قضائية"&&a.jsxs("div",{className:"input-field",children:[a.jsx("label",{className:"label",children:"تاريخ رفع الدعوى"}),a.jsx("input",{type:"date",name:"caseDate",className:"input",value:e.caseDate,onChange:i,required:!0})]}),e.caseStatus==="محضر"&&a.jsxs("div",{className:"input-field",children:[a.jsx("label",{className:"label",children:"تاريخ المحضر"}),a.jsx("input",{type:"date",name:"caseDate",className:"input",value:e.caseDate,onChange:i,required:!0})]}),(e.caseStatus==="محضر"||e.caseStatus==="قيد النظر")&&a.jsxs("div",{className:"input-field",children:[a.jsx("label",{className:"label",children:"مكان الجهة المختصة"}),a.jsx("input",{type:"text",name:"reportLocation",className:"input",placeholder:"مكان الجهة المختصة *",value:e.reportLocation,onChange:i,required:!0})]})]}),e.caseStatus==="دعوى قضائية"&&a.jsxs("div",{className:"input-row",children:[a.jsxs("div",{className:"input-field",children:[a.jsx("label",{className:"label",children:"المحكمة"}),a.jsxs("select",{name:"courtLocation",className:"input",value:e.courtLocation,onChange:i,required:!0,children:[a.jsx("option",{value:"",children:"اختر المحكمة"}),y.map(t=>a.jsx("option",{value:t.name,children:t.name},t.name))]})]}),a.jsxs("div",{className:"input-field",children:[a.jsx("label",{className:"label",children:"رقم الدائرة"}),a.jsx("input",{type:"text",name:"circleNumber",className:"input",placeholder:"رقم الدائرة *",value:e.circleNumber,onChange:i,required:!0})]}),a.jsxs("div",{className:"input-field",children:[a.jsx("label",{className:"label",children:"درجة الدعوى"}),a.jsxs("select",{name:"caseDegree",className:"input",value:e.caseDegree,onChange:i,required:!0,children:[a.jsx("option",{value:"",children:"اختر درجة الدعوى"}),T.map(t=>a.jsx("option",{value:t,children:t},t))]})]})]}),a.jsxs("div",{className:"input-row",children:[e.caseStatus==="دعوى قضائية"&&a.jsxs("div",{className:"input-field",children:[a.jsx("label",{className:"label",children:"نوع الدعوى"}),a.jsxs("select",{name:"caseCategory",className:"input",value:e.caseCategory,onChange:i,disabled:!e.caseDegree,required:!0,children:[a.jsx("option",{value:"",children:"اختر نوع الدعوى"}),e.caseDegree&&((S=N[e.caseDegree])==null?void 0:S.map(t=>a.jsx("option",{value:t,children:t},t)))]})]}),a.jsxs("div",{className:e.caseStatus==="دعوى قضائية"?"input-field":"input-field full-width",children:[a.jsx("label",{className:"label",children:"وصف القضية"}),a.jsx("input",{type:"text",name:"caseDescription",className:"input",value:e.caseDescription,onChange:i,required:!0,placeholder:"وصف القضية *"})]})]})]}),a.jsxs("div",{className:"final-buttons",children:[a.jsx("button",{type:"submit",className:"submit-btn",disabled:h,style:{maxWidth:"150px"},children:h?"جاري الحفظ...":"تسجيل القضية"}),a.jsx("button",{type:"button",onClick:I,className:"cancel-btn",style:{maxWidth:"150px"},children:"إلغاء"})]})]})]})})]})};export{H as default};
