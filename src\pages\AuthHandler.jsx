import React, { useEffect, useState } from 'react';
import { useLocation, useNavigate } from 'react-router-dom';
import { getAuth, applyActionCode, verifyPasswordResetCode, confirmPasswordReset } from 'firebase/auth';

const AuthHandler = () => {
  const [status, setStatus] = useState('processing'); // 'processing' | 'success' | 'error' | 'resetting'
  const [newPassword, setNewPassword] = useState('');
  const navigate = useNavigate();
  const query = new URLSearchParams(useLocation().search);
  const mode = query.get('mode');
  const oobCode = query.get('oobCode');

  useEffect(() => {
    const auth = getAuth();

    if (!oobCode) {
      setStatus('error');
      return;
    }

    if (mode === 'resetPassword' && oobCode) {
      navigate(`/reset-password?oobCode=${oobCode}`, { replace: true });
    } else if (mode === 'verifyEmail') {
      applyActionCode(auth, oobCode)
        .then(async () => {
          setStatus('success');
          await auth.currentUser?.reload();
          navigate('/welcome', { replace: true });
        })
        .catch((error) => {
          console.error('Email verification error:', error);
          setStatus('error');
        });
    } else {
      setStatus('error');
    }
  }, [mode, oobCode, navigate]);

  const renderContent = () => {
    switch (status) {
      case 'processing':
        return (
          <>
            <h2>جاري معالجة طلبك...</h2>
            <p>يرجى الانتظار لحظات.</p>
          </>
        );
      case 'success':
        return (
          <>
            <h2 style={{ color: 'green' }}>تمت العملية بنجاح ✅</h2>
            <p>سيتم تحويلك الآن...</p>
          </>
        );
      case 'error':
      default:
        return (
          <>
            <h2 style={{ color: 'red' }}>فشل التحقق ❌</h2>
            <p>الرابط غير صالح أو انتهت صلاحيته. يرجى طلب رابط جديد.</p>
          </>
        );
    }
  };

  return (
    <div style={{
      maxWidth: '500px',
      margin: '100px auto',
      padding: '40px',
      borderRadius: '15px',
      textAlign: 'center',
      backgroundColor: '#f9f9f9',
      boxShadow: '0 10px 20px rgba(0,0,0,0.1)',
      fontFamily: 'Segoe UI, sans-serif'
    }}>
      {renderContent()}
    </div>
  );
};

export default AuthHandler;