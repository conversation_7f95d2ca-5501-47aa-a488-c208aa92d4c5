import { useState } from 'react';
import { FaClock, FaTimes } from 'react-icons/fa';
import styles from "./AddDeferralAction.module.css";

const AddAction = ({ onSave, onClose }) => {
  const [newAction, setNewAction] = useState('');
  const [actionDeadline, setActionDeadline] = useState(new Date().toISOString().split('T')[0]);
  const [reminderType, setReminderType] = useState('');
  const [error, setError] = useState(null);

  const handleSaveClick = () => {
    if (!newAction || !actionDeadline || !reminderType) {
      setError('يرجى ملء جميع الحقول المطلوبة.');
      return;
    }
    setError(null);
    onSave('action', { description: newAction, deadline: actionDeadline, reminderType });
  };

  return (
    <div className={styles.addActionForm}>
      {error && <div className={styles.errorAlert}>{error}</div>}
      <div className={styles.actionField}>
        <label>وصف الإجراء:</label>
        <input
          type="text"
          value={newAction}
          onChange={(e) => setNewAction(e.target.value)}
          placeholder="أدخل وصف الإجراء"
          className={styles.actionInput}
        />
      </div>
      <div className={styles.actionField}>
        <label>تاريخ الإجراء:</label>
        <input
          type="date"
          value={actionDeadline}
          onChange={(e) => setActionDeadline(e.target.value)}
          className={styles.actionInput}
          min={new Date().toISOString().split('T')[0]}
        />
      </div>
      <div className={styles.actionField}>
        <label>توقيت الإشعار:</label>
        <div className={styles.reminderTags}>
          <div 
            className={`${styles.reminderTag} ${reminderType === 'daily' ? styles.selectedTag : ''}`}
            onClick={() => setReminderType(reminderType === 'daily' ? '' : 'daily')}
          >
            <span className={styles.tagIcon}>🔔</span>
            <span className={styles.tagText}>إشعار يومي</span>
          </div>
          <div 
            className={`${styles.reminderTag} ${reminderType === 'dayBefore' ? styles.selectedTag : ''}`}
            onClick={() => setReminderType(reminderType === 'dayBefore' ? '' : 'dayBefore')}
          >
            <span className={styles.tagIcon}>⏰</span>
            <span className={styles.tagText}>إشعار قبلها بيوم</span>
          </div>
        </div>
      </div>
      <div className={styles.actionFormButtons}>
        <button onClick={handleSaveClick} className={styles.addActionButton}>
          <FaClock className={styles.buttonIcon} />
          <span>إضافة تنبيه بإجراء</span>
        </button>
        <button onClick={onClose} className={styles.cancelButton}>
          <FaTimes className={styles.buttonIcon} />
          <span>إلغاء</span>
        </button>
      </div>
    </div>
  );
};

export default AddAction;
