/**
 * Service Worker للإشعارات الخارجية
 * يدعم Push Notifications والإشعارات في الخلفية
 */

const CACHE_NAME = 'safwat-notifications-v1';
const urlsToCache = [
  '/',
  '/icon.png',
  '/manifest.webmanifest'
];

// تثبيت Service Worker
self.addEventListener('install', (event) => {
  console.log('📦 تثبيت Service Worker للإشعارات');
  event.waitUntil(
    caches.open(CACHE_NAME)
      .then((cache) => {
        return cache.addAll(urlsToCache);
      })
  );
});

// تفعيل Service Worker
self.addEventListener('activate', (event) => {
  console.log('✅ تفعيل Service Worker للإشعارات');
  event.waitUntil(
    caches.keys().then((cacheNames) => {
      return Promise.all(
        cacheNames.map((cacheName) => {
          if (cacheName !== CACHE_NAME) {
            return caches.delete(cacheName);
          }
        })
      );
    })
  );
});

// معالجة النقر على الإشعارات
self.addEventListener('notificationclick', (event) => {
  console.log('🔔 تم النقر على الإشعار:', event.notification.data);
  
  event.notification.close();

  const data = event.notification.data || {};
  
  // تحديد الإجراء بناءً على نوع النقر
  if (event.action === 'view' || !event.action) {
    // فتح التطبيق والانتقال إلى القضية
    const urlToOpen = data.caseId ? 
      `/case-details/${data.caseId}` : 
      '/reports-overview';

    event.waitUntil(
      clients.matchAll({ type: 'window' }).then((clientList) => {
        // البحث عن نافذة مفتوحة للتطبيق
        for (const client of clientList) {
          if (client.url.includes(self.location.origin)) {
            // إذا وجدت نافذة مفتوحة، ركز عليها وانتقل للصفحة المطلوبة
            client.focus();
            client.postMessage({
              type: 'NAVIGATE_TO',
              url: urlToOpen
            });
            return;
          }
        }
        
        // إذا لم توجد نافذة مفتوحة، افتح نافذة جديدة
        return clients.openWindow(self.location.origin + urlToOpen);
      })
    );
  } else if (event.action === 'dismiss') {
    // تجاهل الإشعار فقط
    console.log('تم تجاهل الإشعار');
  }
});

// معالجة إغلاق الإشعارات
self.addEventListener('notificationclose', (event) => {
  console.log('❌ تم إغلاق الإشعار:', event.notification.data);
});

// معالجة Push Messages (للمستقبل)
self.addEventListener('push', (event) => {
  console.log('📨 تم استلام Push Message');
  
  if (event.data) {
    const data = event.data.json();
    
    const options = {
      body: data.body,
      icon: '/icon.png',
      badge: '/icon.png',
      dir: 'rtl',
      lang: 'ar',
      tag: data.tag || 'default',
      requireInteraction: true,
      data: data.data || {},
      actions: [
        {
          action: 'view',
          title: 'عرض'
        },
        {
          action: 'dismiss',
          title: 'تجاهل'
        }
      ]
    };

    event.waitUntil(
      self.registration.showNotification(data.title, options)
    );
  }
});

// معالجة الرسائل من التطبيق الرئيسي
self.addEventListener('message', (event) => {
  console.log('📩 رسالة من التطبيق الرئيسي:', event.data);
  
  if (event.data && event.data.type === 'SCHEDULE_NOTIFICATION') {
    // جدولة إشعار (يمكن تطويرها لاحقاً)
    const { title, body, showTime, data } = event.data;
    
    const delay = new Date(showTime).getTime() - Date.now();
    
    if (delay > 0) {
      setTimeout(() => {
        const options = {
          body: body,
          icon: '/icon.png',
          badge: '/icon.png',
          dir: 'rtl',
          lang: 'ar',
          tag: data.tag || 'scheduled',
          requireInteraction: true,
          data: data,
          actions: [
            {
              action: 'view',
              title: 'عرض'
            },
            {
              action: 'dismiss',
              title: 'تجاهل'
            }
          ]
        };

        self.registration.showNotification(title, options);
      }, delay);
    }
  }
});