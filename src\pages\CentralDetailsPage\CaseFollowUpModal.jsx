import React, { useState, useCallback } from 'react';
import { FaSave } from 'react-icons/fa';
import styles from './CaseFollowUpModal.module.css';

const CaseFollowUpModal = React.memo(({ onClose, onSave, caseId, userId, savedNotes = [] }) => {
  const [newNote, setNewNote] = useState('');
  const [loading, setLoading] = useState(false);
  
  // === منطق الضغط المطول للحذف ===
  const [pressTimer, setPressTimer] = useState(null);

  const handlePressStart = (noteId) => {
    // بدء مؤقت عند الضغط
    const timer = setTimeout(() => {
      // إذا استمر الضغط، قم بتنفيذ الحذف
      handleDelete(noteId);
    }, 800); // 800 مللي ثانية (0.8 ثانية)
    setPressTimer(timer);
  };

  const handlePressEnd = () => {
    // إلغاء المؤقت إذا تم رفع الضغط قبل اكتمال المدة
    clearTimeout(pressTimer);
  };
  // =================================

  const handleSave = async () => {
    if (!newNote.trim()) return;
    setLoading(true);
    try {
      const noteData = {
        id: `${caseId}-note-${Date.now()}`,
        type: 'ملاحظة',
        analysis: newNote.trim(),
        isDeleted: false,
        userId: userId,
        createdAt: new Date().toISOString()
      };
      await onSave(noteData);
      setNewNote('');
    } catch (error) {
      console.error('خطأ في حفظ الملاحظة:', error);
      alert('حدث خطأ أثناء حفظ الملاحظة.');
    } finally {
      setLoading(false);
    }
  };

  const handleDelete = async (noteId) => {
    // لا نحتاج رسالة تأكيد هنا لأن الضغط المطول هو التأكيد بحد ذاته
    try {
      await onSave({ id: noteId, isDeleted: true });
    } catch (error) {
      console.error('خطأ في حذف الملاحظة:', error);
      alert(`حدث خطأ أثناء حذف الملاحظة.`);
    }
  };

  return (
    <div className={styles.modalContent}>
      <div className={styles.header}>
        <h2 className={styles.title}>الملاحظات</h2>
        {/* تم حذف زر الخروج (X) من هنا */}
      </div>

      <div className={styles.content}>
        <div className={styles.savedNotesContainer}>
          {/* تم حذف عنوان "سجل الملاحظات" من هنا */}
          {savedNotes.length > 0 ? (
            <div className={styles.notesGrid}>
              {savedNotes.map((note) => (
                <div 
                  key={note.id} 
                  className={styles.noteChip}
                  // إضافة أحداث الضغط المطول هنا
                  onMouseDown={() => handlePressStart(note.id)}
                  onMouseUp={handlePressEnd}
                  onMouseLeave={handlePressEnd} // لإلغاء الحذف إذا خرج المؤشر
                  onTouchStart={() => handlePressStart(note.id)}
                  onTouchEnd={handlePressEnd}
                  title="اضغط مطولاً للحذف" // رسالة توضيحية للمستخدم
                >
                  <div className={styles.chipContent}>
                    <span className={styles.chipType}>{note.type || 'ملاحظة'}:</span>
                    <span className={styles.chipText}>{note.analysis}</span>
                  </div>
                  {/* تم حذف زر الحذف من هنا */}
                </div>
              ))}
            </div>
          ) : (
            <p className={styles.noNotes}>لا توجد ملاحظات محفوظة.</p>
          )}
        </div>

        <div className={styles.addNoteContainer}>
          <h3 className={styles.sectionTitle}>إضافة ملاحظة جديدة</h3>
          <textarea
            className={styles.textarea}
            value={newNote}
            onChange={(e) => setNewNote(e.target.value)}
            placeholder="اكتب ملاحظتك هنا..."
            rows="4"
          />
          <div className={styles.actionsContainer}>
            <button onClick={onClose} className={`${styles.actionButton} ${styles.cancelButton}`}>
              إلغاء
            </button>
            <button onClick={handleSave} disabled={loading || !newNote.trim()} className={`${styles.actionButton} ${styles.saveButton}`}>
              <FaSave />
              <span>{loading ? 'جاري الحفظ...' : 'حفظ الملاحظة'}</span>
            </button>
          </div>
        </div>
      </div>
    </div>
  );
});

export default CaseFollowUpModal;
