rules_version = '2';
service cloud.firestore {
  match /databases/{database}/documents {
    // القاعدة العامة: افتراضياً ممنوع أي قراءة أو كتابة على أي مستند
    match /{document=**} {
      allow read, write: if false;
    }

    // قواعد المستخدمين
    // تسمح للمستخدم بإنشاء مستند خاص به في مجموعة 'users'
    // وتسمح له بقراءة وتحديث وحذف مستنداته الخاصة فقط.
    match /users/{userId} {
      allow create: if request.auth != null && request.auth.uid == userId;
      allow read, update, delete: if request.auth != null && request.auth.uid == userId;
      // السماح بالاستعلام المحدود للتحقق من أسماء المستخدمين
      allow list: if request.auth == null && 
                     request.query.limit <= 1;
    }

    // قواعد القضايا
    // تسمح للمستخدمين المصادق عليهم بالقراءة والإنشاء والتحديث والحذف.
    match /cases/{caseId} {
      // السماح بالقراءة والإنشاء للمستخدمين المصادق عليهم (بغض النظر عن تفعيل الإيميل)
      allow read, create: if request.auth != null;
      // السماح بالتحديث والحذف فقط لصاحب القضية (userId)
      allow update, delete: if request.auth != null && resource.data.userId == request.auth.uid;

      // قواعد المجموعات الفرعية للتأجيلات (deferrals) داخل القضايا
      // تسمح للمستخدمين المصادق عليهم بالقراءة والكتابة.
      match /deferrals/{deferralId} {
        allow read, write: if request.auth != null;
      }

      // قواعد المجموعات الفرعية للإجراءات (actions) داخل القضايا
      // تسمح للمستخدمين المصادق عليهم بالقراءة والكتابة.
      match /actions/{actionId} {
        allow read, write: if request.auth != null;
      }
    }

    // قواعد المجموعات
    match /groups/{groupId} {
      allow read: if request.auth != null;
      allow create: if request.auth != null;
      allow update, delete: if request.auth != null && 
        (resource.data.createdBy == request.auth.uid || 
         resource.data.managers[request.auth.uid] == true);
    }

    // قواعد الأعضاء
    match /members/{memberId} {
      allow read: if request.auth != null;
      allow create: if request.auth != null;
      allow update, delete: if request.auth != null && 
        (get(/databases/$(database)/documents/groups/$(resource.data.groupId)).data.createdBy == request.auth.uid ||
         get(/databases/$(database)/documents/groups/$(resource.data.groupId)).data.managers[request.auth.uid] == true);
    }

    // قواعد مجموعة 'deferralTemplates'
    // تسمح للمستخدم بإنشاء وقراءة وتحديث وحذف قوالب التأجيل الخاصة به فقط.
    match /deferralTemplates/{templateId} {
      allow create: if request.auth != null && request.resource.data.userId == request.auth.uid;
      allow read: if request.auth != null && resource.data.userId == request.auth.uid;
      allow update: if request.auth != null && resource.data.userId == request.auth.uid;
      allow delete: if request.auth != null && resource.data.userId == request.auth.uid;
      allow list: if request.auth != null;
    }

    // قواعد مجموعة 'stats'
    // تسمح بالقراءة للمستخدمين المصادق عليهم.
    // تسمح بالكتابة للمستخدمين غير المصادق عليهم (null)، وهذا قد يكون غير آمن إذا لم يكن مقصودًا.
    // إذا كنت تريد أن يتمكن المستخدمون المصادق عليهم فقط من الكتابة، قم بتغيير 'null' إلى 'request.auth.uid != null'.
    match /stats/{docId} {
      allow read: if request.auth != null;
      allow write: if request.auth == null;
    }

    // قواعد خاصة بالقضايا المرتبطة بالمجموعات
    match /groupCases/{caseId} {
      // السماح بالقراءة لأي عضو في المجموعة
      allow read: if request.auth != null &&
                   exists(/databases/$(database)/documents/members/$(request.auth.uid + '_' + resource.data.groupId));

      // السماح بالإنشاء والتحديث والحذف لمنشئ المجموعة والمديرين
      allow create, update, delete: if request.auth != null &&
                                     (get(/databases/$(database)/documents/groups/$(resource.data.groupId)).data.createdBy == request.auth.uid ||
                                      get(/databases/$(database)/documents/groups/$(resource.data.groupId)).data.managers[request.auth.uid] == true);
    }

    // قواعد خاصة بالإشعارات المرتبطة بالمجموعات
    match /groupNotifications/{notificationId} {
      // السماح بالقراءة فقط للمستخدم المرسل إليه الإشعار
      allow read: if request.auth != null &&
                   resource.data.userId == request.auth.uid;

      // السماح بالكتابة لمنشئ المجموعة والمديرين
      allow write: if request.auth != null &&
                    (get(/databases/$(database)/documents/groups/$(resource.data.groupId)).data.createdBy == request.auth.uid ||
                     get(/databases/$(database)/documents/groups/$(resource.data.groupId)).data.managers[request.auth.uid] == true);
    }

    // قواعد خاصة بالمهام في المجموعات
    match /groupTasks/{taskId} {
      // السماح بالقراءة لأي عضو في المجموعة
      allow read: if request.auth != null &&
                   exists(/databases/$(database)/documents/members/$(request.auth.uid + '_' + resource.data.groupId));

      // السماح بالإنشاء والتحديث والحذف لمنشئ المجموعة والمديرين
      allow create, update, delete: if request.auth != null &&
                                     (get(/databases/$(database)/documents/groups/$(resource.data.groupId)).data.createdBy == request.auth.uid ||
                                      get(/databases/$(database)/documents/groups/$(resource.data.groupId)).data.managers[request.auth.uid] == true);
    }

    // قواعد التقارير
    match /reports/{reportId} {
      // السماح بالقراءة للمستخدمين المصادق عليهم
      allow read: if request.auth != null;
      
      // السماح بالإنشاء للمستخدمين المصادق عليهم
      allow create: if request.auth != null;
      
      // السماح بالتحديث والحذف لمالك التقرير أو مدير المجموعة
      allow update, delete: if request.auth != null && 
        (resource.data.createdBy == request.auth.uid ||
         (resource.data.groupId != null && 
          get(/databases/$(database)/documents/groups/$(resource.data.groupId)).data.managers[request.auth.uid] == true));
    }

    // قواعد نظام توزيع المهام
    match /taskAssignments/{assignmentId} {
      // السماح بالقراءة للمستخدمين المصادق عليهم
      allow read: if request.auth != null;
      
      // السماح بالإنشاء للمستخدمين المصادق عليهم
      allow create: if request.auth != null && request.auth.uid != null;
      
      // السماح بالتحديث والحذف للمستخدمين المصادق عليهم
      allow update, delete: if request.auth != null && request.auth.uid != null;
    }

    // قواعد للتحقق من أسماء المستخدمين أثناء التسجيل
    match /usernames/{username} {
      // السماح بالقراءة للجميع للتحقق من توفر اسم المستخدم
      allow read: if true;
      // السماح بالكتابة للمستخدمين المصادق عليهم فقط
      allow write: if request.auth != null;
    }
  }
}
