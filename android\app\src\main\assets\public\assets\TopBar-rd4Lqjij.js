import{r as i,c as B,j as t,d as I,b as S,g as W,v as b,a as P}from"./index-HUlFBKIW.js";import{G as d}from"./iconBase-BPj5F03O.js";import{g as f,p as _}from"./PermissionsService-Dhc9PbZp.js";function D(e){return d({attr:{viewBox:"0 0 24 24",fill:"none",stroke:"currentColor",strokeWidth:"2",strokeLinecap:"round",strokeLinejoin:"round"},child:[{tag:"line",attr:{x1:"5",y1:"12",x2:"19",y2:"12"},child:[]},{tag:"polyline",attr:{points:"12 5 19 12 12 19"},child:[]}]})(e)}function oe(e){return d({attr:{viewBox:"0 0 24 24",fill:"none",stroke:"currentColor",strokeWidth:"2",strokeLinecap:"round",strokeLinejoin:"round"},child:[{tag:"path",attr:{d:"M18 8A6 6 0 0 0 6 8c0 7-3 9-3 9h18s-3-2-3-9"},child:[]},{tag:"path",attr:{d:"M13.73 21a2 2 0 0 1-3.46 0"},child:[]}]})(e)}function E(e){return d({attr:{viewBox:"0 0 24 24",fill:"none",stroke:"currentColor",strokeWidth:"2",strokeLinecap:"round",strokeLinejoin:"round"},child:[{tag:"path",attr:{d:"M9 21H5a2 2 0 0 1-2-2V5a2 2 0 0 1 2-2h4"},child:[]},{tag:"polyline",attr:{points:"16 17 21 12 16 7"},child:[]},{tag:"line",attr:{x1:"21",y1:"12",x2:"9",y2:"12"},child:[]}]})(e)}function F(e){return d({attr:{viewBox:"0 0 24 24",fill:"none",stroke:"currentColor",strokeWidth:"2",strokeLinecap:"round",strokeLinejoin:"round"},child:[{tag:"line",attr:{x1:"3",y1:"12",x2:"21",y2:"12"},child:[]},{tag:"line",attr:{x1:"3",y1:"6",x2:"21",y2:"6"},child:[]},{tag:"line",attr:{x1:"3",y1:"18",x2:"21",y2:"18"},child:[]}]})(e)}function M(e){return d({attr:{viewBox:"0 0 24 24",fill:"none",stroke:"currentColor",strokeWidth:"2",strokeLinecap:"round",strokeLinejoin:"round"},child:[{tag:"circle",attr:{cx:"12",cy:"12",r:"10"},child:[]},{tag:"line",attr:{x1:"12",y1:"8",x2:"12",y2:"16"},child:[]},{tag:"line",attr:{x1:"8",y1:"12",x2:"16",y2:"12"},child:[]}]})(e)}function z(e){return d({attr:{viewBox:"0 0 24 24",fill:"none",stroke:"currentColor",strokeWidth:"2",strokeLinecap:"round",strokeLinejoin:"round"},child:[{tag:"path",attr:{d:"M17 21v-2a4 4 0 0 0-4-4H5a4 4 0 0 0-4 4v2"},child:[]},{tag:"circle",attr:{cx:"9",cy:"7",r:"4"},child:[]},{tag:"path",attr:{d:"M23 21v-2a4 4 0 0 0-3-3.87"},child:[]},{tag:"path",attr:{d:"M16 3.13a4 4 0 0 1 0 7.75"},child:[]}]})(e)}const O="_headerContainer_n9rup_4",q="_topBar_n9rup_11",$="_liquid_n9rup_26",G="_rightSection_n9rup_43",H="_iconWrapper_n9rup_51",T="_iconButton_n9rup_55",J="_tooltip_n9rup_82",V="_profileDropdown_n9rup_103",K="_menuContainer_n9rup_123",Q="_dropdownItem_n9rup_132",X="_userPhoto_n9rup_176",Y="_userInitials_n9rup_184",Z="_addCaseWrapper_n9rup_197",o={headerContainer:O,topBar:q,liquid:$,rightSection:G,iconWrapper:H,iconButton:T,tooltip:J,profileDropdown:V,menuContainer:K,dropdownItem:Q,userPhoto:X,userInitials:Y,addCaseWrapper:Z},ne=({currentUser:e})=>{const[c,p]=i.useState(!1),[u,g]=i.useState(f()),h=B(),[x,m]=i.useState(null),[v,y]=i.useState(null),[w,C]=i.useState(!1);i.useEffect(()=>{const s=()=>{const a=f();a!==u&&g(a)};window.addEventListener("storage",s);const l=setInterval(()=>{const a=f();a!==u&&g(a)},1e3);return()=>{window.removeEventListener("storage",s),clearInterval(l)}},[u]),i.useEffect(()=>{const s=()=>{if(e!=null&&e.uid){const n=_.getCurrentUserRole(e.uid),r=_.hasPermission(n,"addCases");C(r)}};s();const l=n=>{n.key==="user_permissions"&&s()};window.addEventListener("storage",l);const a=setInterval(s,2e3);return()=>{window.removeEventListener("storage",l),clearInterval(a)}},[e]),i.useEffect(()=>{(async()=>{if(!(e!=null&&e.uid))return;const l=f();if(console.log("جاري جلب صورة المستخدم، الحساب النشط:",l),l==="local"){const a=localStorage.getItem("localUserData_"+e.uid);if(a)try{const n=JSON.parse(a);if(n.photoURL&&(console.log("تم العثور على صورة في الحساب المحلي"),m(n.photoURL)),(n.name||n.username)&&y(n.name||n.username),n.photoURL||n.name||n.username)return}catch(n){console.error("خطأ في قراءة بيانات المستخدم المحلية:",n)}}else try{const a=I(S,"users",e.uid),n=await W(a);if(n.exists()){const r=n.data();if(r.photoURL&&(console.log("تم العثور على صورة في Firestore"),m(r.photoURL)),(r.name||r.username)&&y(r.name||r.username),r.photoURL||r.name||r.username)return}}catch(a){console.error("خطأ في جلب صورة المستخدم من Firestore:",a)}e.photoURL?(console.log("استخدام صورة المستخدم من Firebase Auth"),m(e.photoURL)):(console.log("لا توجد صورة للمستخدم"),m(null))})()},[e,u]);const j=i.useRef(null);i.useRef(null);const k=i.useRef(null);i.useEffect(()=>(c?document.body.style.overflow="hidden":document.body.style.overflow="",()=>{document.body.style.overflow=""}),[c]);const L=async()=>{try{await b(P),p(!1),h("/login")}catch(s){console.error("خطأ أثناء تسجيل الخروج:",s)}},N=()=>{h("/cases")},R=()=>{if(p(!c),!c)try{const s=new Audio("/sounds/pop.mp3");s.volume=.2,s.play()}catch{console.log("تعذر تشغيل الصوت")}},A=window.location.pathname==="/"||window.location.pathname==="/dashboard";return t.jsxs("div",{className:o.headerContainer,children:[t.jsxs("header",{ref:k,className:`${o.topBar} ${o.liquid}`,children:[" ",t.jsx("div",{className:o.leftSection}),t.jsxs("div",{className:o.rightSection,children:[w&&t.jsx("div",{className:`${o.iconWrapper} ${o.addCaseWrapper}`,children:t.jsxs("button",{className:o.iconButton,onClick:N,"aria-label":"إضافة قضية جديدة",children:[t.jsx(M,{size:20}),t.jsx("span",{className:o.tooltip,children:"إضافة قضية"})]})}),!A&&t.jsx("div",{className:o.iconWrapper,children:t.jsxs("button",{className:o.iconButton,onClick:()=>h(-1),"aria-label":"العودة",children:[t.jsx(D,{size:20}),t.jsx("span",{className:o.tooltip,children:"العودة"})]})}),t.jsx("div",{className:o.iconWrapper,children:t.jsxs("button",{className:o.iconButton,onClick:R,"aria-label":"القائمة","aria-expanded":c,children:[t.jsx(F,{size:20}),t.jsx("span",{className:o.tooltip,children:"القائمة"})]})})]})]}),c&&t.jsx("div",{ref:j,className:o.profileDropdown,onClick:()=>p(!1),children:t.jsxs("div",{className:o.menuContainer,onClick:s=>s.stopPropagation(),children:[t.jsxs("div",{className:o.dropdownItem,style:{"--item-index":0},onClick:()=>{h("/profile"),p(!1)},children:[t.jsxs("span",{style:{display:"flex",alignItems:"center",gap:8},children:[x?t.jsx("img",{src:x,alt:"صورة المستخدم",className:o.userPhoto,style:{width:32,height:32,marginLeft:8,borderRadius:"50%"}}):t.jsx("div",{className:o.userInitials,style:{width:32,height:32,marginLeft:8},children:v?v.charAt(0).toUpperCase():e!=null&&e.displayName?e.displayName.charAt(0).toUpperCase():"م"}),"الملف الشخصي"]}),t.jsx(z,{size:20})]}),t.jsxs("div",{className:o.dropdownItem,style:{"--item-index":2},onClick:L,children:[t.jsx("span",{children:"تسجيل الخروج"}),t.jsx(E,{size:20})]})]})})]})};export{oe as F,ne as T};
