import{c as P,r as o,j as e,E as k,k as I,l as F}from"./index-HUlFBKIW.js";import{F as R,h as E,o as c,p as x,n as z,l as B,m as T}from"./index-DhfUDJs-.js";import{T as A}from"./TopBar-rd4Lqjij.js";import{s}from"./ProfilePage.module-DwB6uXPZ.js";import"./iconBase-BPj5F03O.js";import"./PermissionsService-Dhc9PbZp.js";const G=({currentUser:r})=>{const f=P(),[n,g]=o.useState(""),[i,j]=o.useState(""),[h,y]=o.useState(""),[v,m]=o.useState(!1),[u,a]=o.useState(null),[l,w]=o.useState(!1),[d,b]=o.useState(!1),[p,N]=o.useState(!1),S=async()=>{if(!r){a("المستخدم غير مسجل الدخول.");return}if(!n){a("كلمة المرور الحالية مطلوبة.");return}if(!i){a("كلمة المرور الجديدة مطلوبة.");return}if(i.length<6){a("يجب أن تكون كلمة المرور الجديدة 6 أحرف على الأقل.");return}if(i!==h){a("كلمة المرور الجديدة وتأكيدها غير متطابقين.");return}try{m(!0),a(null);const t=k.credential(r.email,n);await I(r,t),await F(r,i),alert("تم تغيير كلمة المرور بنجاح."),f("/profile")}catch(t){console.error("خطأ في تغيير كلمة المرور:",t),t.code==="auth/wrong-password"?a("كلمة المرور الحالية غير صحيحة."):t.code==="auth/too-many-requests"?a("تم تجاوز عدد المحاولات المسموح بها. يرجى المحاولة لاحقاً."):a("حدث خطأ أثناء تغيير كلمة المرور. يرجى المحاولة مرة أخرى.")}finally{m(!1)}},C=()=>{f("/profile")};return e.jsxs("div",{className:s.pageWrapper,children:[e.jsx(A,{currentUser:r}),e.jsxs("div",{className:s.mainContainer,children:[e.jsx("div",{className:s.accountManagementSection,children:e.jsxs("div",{className:s.sidebarNavItem,style:{visibility:"hidden"},children:[e.jsx("div",{className:s.sidebarNavIcon,children:e.jsx(R,{})}),e.jsx("span",{children:"المعلومات الشخصية"})]})}),e.jsxs("div",{className:s.mainContentArea,children:[e.jsx("div",{className:s.pageHeader,children:e.jsx("h2",{className:s.pageTitle,children:"تعديل كلمة المرور"})}),u&&e.jsx("div",{className:s.errorMessage,children:u}),e.jsx("div",{className:s.personalInfoSection,children:e.jsxs("div",{className:s.editFieldForm,children:[e.jsx("h3",{className:s.sectionTitle,children:"تغيير كلمة المرور"}),e.jsx("div",{style:{color:"#5f6368",fontSize:"14px",marginBottom:"15px",lineHeight:"1.5",display:"flex",alignItems:"flex-start",backgroundColor:"#f8f9fa",padding:"12px 15px",borderRadius:"8px"},children:e.jsxs("div",{className:"field-description",children:[e.jsx(E,{style:{color:"#1a73e8",marginLeft:"8px",flexShrink:0}}),e.jsx("span",{children:"كلمة المرور القوية تساعد في حماية حسابك من الاختراق. استخدم مزيجًا من الأحرف والأرقام والرموز لزيادة مستوى الأمان."})]})}),e.jsxs("div",{className:s.formGroup,children:[e.jsxs("div",{style:{position:"relative",marginBottom:"15px"},children:[e.jsx("input",{type:l?"text":"password",value:n,onChange:t=>g(t.target.value),placeholder:"كلمة المرور الحالية",className:s.formInput,style:{paddingRight:"40px"}}),e.jsx("button",{type:"button",onClick:()=>w(!l),style:{position:"absolute",right:"10px",top:"50%",transform:"translateY(-50%)",background:"none",border:"none",cursor:"pointer",color:"#5f6368"},children:l?e.jsx(c,{}):e.jsx(x,{})})]}),e.jsxs("div",{style:{position:"relative",marginBottom:"15px"},children:[e.jsx("input",{type:d?"text":"password",value:i,onChange:t=>j(t.target.value),placeholder:"كلمة المرور الجديدة",className:s.formInput,style:{paddingRight:"40px"}}),e.jsx("button",{type:"button",onClick:()=>b(!d),style:{position:"absolute",right:"10px",top:"50%",transform:"translateY(-50%)",background:"none",border:"none",cursor:"pointer",color:"#5f6368"},children:d?e.jsx(c,{}):e.jsx(x,{})})]}),e.jsxs("div",{style:{position:"relative",marginBottom:"15px"},children:[e.jsx("input",{type:p?"text":"password",value:h,onChange:t=>y(t.target.value),placeholder:"تأكيد كلمة المرور الجديدة",className:s.formInput,style:{paddingRight:"40px"}}),e.jsx("button",{type:"button",onClick:()=>N(!p),style:{position:"absolute",right:"10px",top:"50%",transform:"translateY(-50%)",background:"none",border:"none",cursor:"pointer",color:"#5f6368"},children:p?e.jsx(c,{}):e.jsx(x,{})})]})]}),e.jsx("div",{style:{color:"#5f6368",fontSize:"12px",marginBottom:"20px",lineHeight:"1.5",display:"flex",alignItems:"flex-start",backgroundColor:"#f1f3f4",padding:"10px 12px",borderRadius:"8px"},children:e.jsxs("div",{className:"field-footer",children:[e.jsx(z,{style:{color:"#5f6368",marginLeft:"8px",flexShrink:0}}),e.jsx("span",{children:"استخدم 8 أحرف على الأقل مع مزيج من الأحرف الكبيرة والصغيرة والأرقام والرموز لإنشاء كلمة مرور قوية. تجنب استخدام معلومات شخصية يمكن تخمينها بسهولة."})]})}),e.jsxs("div",{className:s.buttonRow,children:[e.jsxs("button",{onClick:S,style:{background:"#1a73e8",color:"#fff",border:"none",borderRadius:"20px",padding:"8px 16px",fontSize:"14px",cursor:"pointer",display:"flex",alignItems:"center",gap:"5px"},disabled:v,children:[e.jsx(B,{style:{fontSize:"12px"}})," حفظ"]}),e.jsxs("button",{onClick:C,style:{background:"#f1f3f4",color:"#5f6368",border:"1px solid #dadce0",borderRadius:"20px",padding:"8px 16px",fontSize:"14px",cursor:"pointer",display:"flex",alignItems:"center",gap:"5px"},children:[e.jsx(T,{style:{fontSize:"12px"}})," إلغاء"]})]})]})})]})]}),e.jsx("style",{jsx:!0,children:`
        .field-description {
          display: flex;
          align-items: flex-start;
          gap: 8px;
          width: 100%;
        }
        .field-description span {
          flex: 1;
        }
        .field-footer {
          display: flex;
          align-items: flex-start;
          gap: 8px;
          width: 100%;
        }
        .field-footer span {
          flex: 1;
        }
      `})]})};export{G as default};
