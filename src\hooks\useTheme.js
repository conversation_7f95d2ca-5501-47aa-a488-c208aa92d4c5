import { useState, useEffect } from 'react';

/**
 * Hook لإدارة الثيمات في التطبيق
 * يدعم ثلاثة أوضاع: light, dark, liquid
 */
export const useTheme = () => {
  const [theme, setTheme] = useState('light');

  // تحميل الثيم المحفوظ عند بدء التطبيق
  useEffect(() => {
    const savedTheme = localStorage.getItem('app-theme');
    if (savedTheme && ['light'].includes(savedTheme)) {
      setTheme(savedTheme);
      applyTheme(savedTheme);
    } else {
      // تطبيق الثيم الافتراضي
      applyTheme('light');
    }
  }, []);

  // تطبيق الثيم على الصفحة
  const applyTheme = (newTheme) => {
    // إزالة جميع كلاسات الثيمات السابقة
    document.body.classList.remove('theme-light');
    
    // إضافة كلاس الثيم الجديد
    document.body.classList.add(`theme-${newTheme}`);
    
    // تحديث متغير CSS للثيم الحالي
    document.documentElement.style.setProperty('--current-theme', `'${newTheme}'`);
    
    // إضافة تأثير انتقال سلس
    document.body.style.transition = 'all 0.3s ease';
    setTimeout(() => {
      document.body.style.transition = '';
    }, 300);
  };

  // تغيير الثيم
  const changeTheme = (newTheme) => {
    if (!['light'].includes(newTheme)) {
      console.warn(`Invalid theme: ${newTheme}. Using 'light' instead.`);
      newTheme = 'light';
    }
    
    setTheme(newTheme);
    applyTheme(newTheme);
    
    // حفظ الثيم في localStorage
    localStorage.setItem('app-theme', newTheme);
    
    // إرسال حدث مخصص لإعلام المكونات الأخرى بتغيير الثيم
    window.dispatchEvent(new CustomEvent('themeChanged', { 
      detail: { theme: newTheme } 
    }));
  };

  // التبديل بين الثيمات
  const toggleTheme = () => {
    const themes = ['light'];
    const currentIndex = themes.indexOf(theme);
    const nextIndex = (currentIndex + 1) % themes.length;
    changeTheme(themes[nextIndex]);
  };

  // التحقق من الثيم الحالي
  const isLight = theme === 'light';
  const isDark = theme === 'dark';

  // الحصول على معلومات الثيم
  const getThemeInfo = () => {
    const themeMap = {
      light: {
        name: 'الوضع المضيء',
        description: 'الثيم الكلاسيكي المضيء',
        icon: '☀️'
      }
    };
    
    return themeMap[theme] || themeMap.light;
  };

  return {
    theme,
    changeTheme,
    toggleTheme,
    isLight,
    isDark,
    getThemeInfo
  };
};

export default useTheme;
