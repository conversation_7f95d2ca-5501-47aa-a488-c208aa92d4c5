@import '../../styles/variables.css';

.themeTest {
  max-width: 1200px;
  margin: 0 auto;
  padding: var(--space-6);
  background: var(--current-bg-primary);
  border-radius: var(--radius-lg);
  box-shadow: var(--current-shadow-medium);
  border: 1px solid var(--current-border-primary);
}

.header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: var(--space-6);
  padding-bottom: var(--space-4);
  border-bottom: 1px solid var(--current-border-primary);
}

.title {
  color: var(--current-text-primary);
  font-size: var(--font-size-2xl);
  font-weight: var(--font-weight-bold);
  margin: 0;
}

.currentTheme {
  display: flex;
  align-items: center;
  gap: var(--space-2);
  color: var(--current-text-secondary);
  font-size: var(--font-size-base);
}

.themeIcon {
  font-size: var(--font-size-lg);
}

.themeSelector {
  margin-bottom: var(--space-6);
}

.themeSelector h3 {
  color: var(--current-text-primary);
  margin-bottom: var(--space-3);
}

.themeButtons {
  display: flex;
  gap: var(--space-3);
  flex-wrap: wrap;
}

.themeButton {
  display: flex;
  align-items: center;
  gap: var(--space-2);
  padding: var(--space-3) var(--space-4);
  background: var(--current-bg-secondary);
  border: 1px solid var(--current-border-primary);
  border-radius: var(--radius-md);
  color: var(--current-text-primary);
  cursor: pointer;
  transition: all var(--transition-normal);
  font-size: var(--font-size-sm);
  font-weight: var(--font-weight-medium);
}

.themeButton:hover {
  background: var(--current-bg-tertiary);
  transform: translateY(-2px);
  box-shadow: var(--current-shadow-light);
}

.themeButton.active {
  background: var(--primary-color);
  color: white;
  border-color: var(--primary-color);
}

.testSections {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
  gap: var(--space-6);
  margin-bottom: var(--space-6);
}

.testSection {
  background: var(--current-bg-secondary);
  border-radius: var(--radius-md);
  padding: var(--space-4);
  border: 1px solid var(--current-border-primary);
}

.sectionTitle {
  color: var(--current-text-primary);
  font-size: var(--font-size-lg);
  font-weight: var(--font-weight-semibold);
  margin: 0 0 var(--space-3) 0;
}

.sectionContent {
  display: flex;
  flex-direction: column;
  gap: var(--space-3);
}

/* أزرار الاختبار */
.buttonGroup {
  display: flex;
  gap: var(--space-2);
  flex-wrap: wrap;
}

.primaryButton {
  padding: var(--space-2) var(--space-4);
  background: var(--primary-color);
  color: white;
  border: none;
  border-radius: var(--radius-sm);
  cursor: pointer;
  transition: all var(--transition-normal);
  font-weight: var(--font-weight-medium);
}

.primaryButton:hover {
  background: var(--primary-dark-blue);
  transform: translateY(-1px);
}

.secondaryButton {
  padding: var(--space-2) var(--space-4);
  background: var(--current-bg-tertiary);
  color: var(--current-text-primary);
  border: 1px solid var(--current-border-primary);
  border-radius: var(--radius-sm);
  cursor: pointer;
  transition: all var(--transition-normal);
  font-weight: var(--font-weight-medium);
}

.secondaryButton:hover {
  background: var(--current-bg-primary);
  transform: translateY(-1px);
}

.dangerButton {
  padding: var(--space-2) var(--space-4);
  background: var(--danger-color);
  color: white;
  border: none;
  border-radius: var(--radius-sm);
  cursor: pointer;
  transition: all var(--transition-normal);
  font-weight: var(--font-weight-medium);
}

.dangerButton:hover {
  background: var(--danger-dark);
  transform: translateY(-1px);
}

/* حقول الإدخال */
.inputGroup {
  display: flex;
  flex-direction: column;
  gap: var(--space-3);
}

.textInput,
.emailInput,
.textArea {
  padding: var(--space-3);
  background: var(--current-bg-primary);
  border: 1px solid var(--current-border-primary);
  border-radius: var(--radius-sm);
  color: var(--current-text-primary);
  font-size: var(--font-size-base);
  transition: all var(--transition-normal);
}

.textInput:focus,
.emailInput:focus,
.textArea:focus {
  outline: none;
  border-color: var(--primary-color);
  box-shadow: 0 0 0 3px rgba(37, 99, 235, 0.1);
}

.textInput::placeholder,
.emailInput::placeholder,
.textArea::placeholder {
  color: var(--current-text-tertiary);
}

/* البطاقات */
.cardGroup {
  display: flex;
  flex-direction: column;
  gap: var(--space-3);
}

.testCard {
  background: var(--current-bg-primary);
  border: 1px solid var(--current-border-primary);
  border-radius: var(--radius-md);
  padding: var(--space-4);
  transition: all var(--transition-normal);
}

.testCard:hover {
  transform: translateY(-2px);
  box-shadow: var(--current-shadow-light);
}

.testCard h4 {
  color: var(--current-text-primary);
  margin: 0 0 var(--space-2) 0;
  font-size: var(--font-size-md);
  font-weight: var(--font-weight-semibold);
}

.testCard p {
  color: var(--current-text-secondary);
  margin: 0 0 var(--space-3) 0;
  font-size: var(--font-size-sm);
}

.cardActions {
  display: flex;
  gap: var(--space-2);
}

.cardButton {
  display: flex;
  align-items: center;
  gap: var(--space-1);
  padding: var(--space-1) var(--space-2);
  background: var(--current-bg-secondary);
  border: 1px solid var(--current-border-primary);
  border-radius: var(--radius-sm);
  color: var(--current-text-primary);
  cursor: pointer;
  transition: all var(--transition-normal);
  font-size: var(--font-size-xs);
}

.cardButton:hover {
  background: var(--current-bg-tertiary);
}

/* الإحصائيات */
.statsGroup {
  display: flex;
  gap: var(--space-3);
  justify-content: space-between;
}

.statItem {
  text-align: center;
  flex: 1;
}

.statValue {
  font-size: var(--font-size-2xl);
  font-weight: var(--font-weight-bold);
  color: var(--primary-color);
  margin-bottom: var(--space-1);
}

.statLabel {
  font-size: var(--font-size-xs);
  color: var(--current-text-secondary);
  font-weight: var(--font-weight-medium);
}

/* لوحة الألوان */
.colorPalette {
  background: var(--current-bg-secondary);
  border-radius: var(--radius-md);
  padding: var(--space-4);
  border: 1px solid var(--current-border-primary);
}

.colorPalette h3 {
  color: var(--current-text-primary);
  margin: 0 0 var(--space-3) 0;
  font-size: var(--font-size-lg);
}

.colorGrid {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(150px, 1fr));
  gap: var(--space-3);
}

.colorItem {
  display: flex;
  align-items: center;
  gap: var(--space-2);
}

.colorSwatch {
  width: 24px;
  height: 24px;
  border-radius: var(--radius-sm);
  border: 1px solid var(--current-border-primary);
  flex-shrink: 0;
}

.colorItem span {
  font-size: var(--font-size-xs);
  color: var(--current-text-secondary);
}

/* تصميم متجاوب */
@media (max-width: 768px) {
  .themeTest {
    padding: var(--space-4);
  }
  
  .header {
    flex-direction: column;
    gap: var(--space-3);
    text-align: center;
  }
  
  .testSections {
    grid-template-columns: 1fr;
  }
  
  .statsGroup {
    flex-direction: column;
    gap: var(--space-2);
  }
  
  .colorGrid {
    grid-template-columns: 1fr;
  }
}
