import React, { useEffect, useState } from 'react';
import { useNavigate, useLocation } from 'react-router-dom';
import { auth, db } from '../config/firebaseConfig';
import { onAuthStateChanged } from 'firebase/auth';
import { doc, setDoc } from 'firebase/firestore';
import LoadingSpinner from '../components/ui/LoadingSpinner';
import Swal from 'sweetalert2';

function AuthDataSetup() {
  const navigate = useNavigate();
  const location = useLocation();
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState(null);

  useEffect(() => {
    const { username, phone } = location.state || {};

    if (!username) {
      setError('بيانات المستخدم غير متوفرة. يرجى إعادة التسجيل.');
      setLoading(false);
      setTimeout(() => navigate('/signup', { replace: true }), 3000);
      return;
    }

    const unsubscribe = onAuthStateChanged(auth, async (user) => {
      if (user) {
        // المستخدم مصادق عليه الآن، يمكننا حفظ بياناته في Firestore
        try {
          await setDoc(doc(db, 'users', user.uid), {
            name: username.trim() || '',
            username: username.trim() || null,
            email: user.email,
            phone: phone.trim() || null,
            role: 'محامي',
            createdAt: new Date().toISOString(),
            emailVerified: false,
          });
          console.log('تم حفظ بيانات المستخدم في Firestore بنجاح.');
          Swal.fire({
            icon: 'success',
            title: 'تم إنشاء الحساب بنجاح!',
            text: 'يرجى التحقق من بريدك الإلكتروني لتفعيل حسابك قبل تسجيل الدخول.',
            confirmButtonText: 'حسناً'
          }).then(() => {
            navigate('/verify-email', { replace: true });
          });
        } catch (firestoreError) {
          console.error('خطأ في حفظ بيانات المستخدم في Firestore:', firestoreError);
          setError('حدث خطأ أثناء حفظ بيانات المستخدم. يرجى المحاولة مرة أخرى.');
          Swal.fire({
            icon: 'error',
            title: 'خطأ!',
            text: 'حدث خطأ أثناء حفظ بيانات المستخدم. يرجى المحاولة مرة أخرى.',
            confirmButtonText: 'حسناً'
          }).then(() => {
            navigate('/signup', { replace: true }); // العودة لصفحة التسجيل
          });
        } finally {
          setLoading(false);
        }
      } else {
        // لا يوجد مستخدم مصادق عليه، ربما لم يتم إنشاء الحساب بعد
        setError('لا يوجد مستخدم مصادق عليه. يرجى إعادة التسجيل.');
        setLoading(false);
        setTimeout(() => navigate('/signup', { replace: true }), 3000);
      }
    });

    return () => unsubscribe(); // تنظيف المستمع عند إلغاء تحميل المكون
  }, [navigate, location.state]);

  if (loading) {
    return <LoadingSpinner message="جاري إعداد حسابك..." />;
  }

  return (
    <div style={{ textAlign: "center", padding: "50px" }}>
      {error ? (
        <div className="error-message">{error}</div>
      ) : (
        <LoadingSpinner message="جاري إعداد حسابك..." />
      )}
    </div>
  );
}

export default AuthDataSetup;
