/* PostVerificationPage.module.css */

@import '../styles/variables.css';

/* Keyframe Animations */
@keyframes fadeInDown {
  from {
    opacity: 0;
    transform: translateY(-10px);
  }
  to {
    opacity: 1;
    transform: translateY(0);
  }
}

@keyframes slideInRight {
  from {
    opacity: 0;
    transform: translateX(50px);
  }
  to {
    opacity: 1;
    transform: translateX(0);
  }
}

@keyframes slideInLeft {
  from {
    opacity: 0;
    transform: translateX(-50px);
  }
  to {
    opacity: 1;
    transform: translateX(0);
  }
}

@keyframes slideOutLeft {
  from {
    opacity: 1;
    transform: translateX(0);
  }
  to {
    opacity: 0;
    transform: translateX(-50px);
  }
}

@keyframes slideOutRight {
  from {
    opacity: 1;
    transform: translateX(0);
  }
  to {
    opacity: 0;
    transform: translateX(50px);
  }
}

/* Global Page Container */
.pageContainer {
  font-family: 'Inter', 'Segoe UI', Roboto, 'Helvetica Neue', Arial, sans-serif; /* Modern, clean font */
  max-width: 850px; /* Increased width */
  margin: 100px auto; /* Good vertical spacing */
  padding: 40px; /* Increased padding */
  color: #333; /* Standard dark text */
  display: flex;
  flex-direction: column;
  border-radius: 12px; /* Soft rounded corners */
}

/* Header Styling */
.header {
  text-align: center;
  margin-bottom: 20px;
  animation: fadeInDown 0.5s ease-out forwards;
}

.header h1 {
  font-size: 2.2rem; /* Balanced title size */
  color: #2c3e50; /* Deep blue for headings */
  margin-bottom: 10px;
  font-weight: 600; /* Semi-bold title */
}

.header p {
  font-size: 1.05rem; /* Clear subtitle */
  color: #7f8c8d; /* Softer grey */
  line-height: 1.5;
}

/* Step Content Container */
.stepContentContainer {
  position: relative;
  min-height: 280px; /* Adjusted height */
  display: flex;
  justify-content: center;
  align-items: center;
  overflow: hidden;
  /* Removed background and shadow from here for a cleaner look */
  padding: 20px 0; /* Padding for content inside */
}

.stepContent {
  position: absolute;
  width: 100%;
  height: 100%;
  display: flex;
  flex-direction: column;
  justify-content: center;
  align-items: center;
  text-align: center;
}

/* Animation Classes */
.slideInRight {
  animation: slideInRight 0.6s cubic-bezier(0.25, 0.46, 0.45, 0.94) forwards; /* Smoother ease-out */
}

.slideInLeft {
  animation: slideInLeft 0.6s cubic-bezier(0.25, 0.46, 0.45, 0.94) forwards; /* Smoother ease-out */
}

.slideOutLeft {
  animation: slideOutLeft 0.6s cubic-bezier(0.55, 0.085, 0.68, 0.53) forwards; /* Smoother ease-in */
}

.slideOutRight {
  animation: slideOutRight 0.6s cubic-bezier(0.55, 0.085, 0.68, 0.53) forwards; /* Smoother ease-in */
}

/* Card (Content Section) Styling - now even simpler */
.card {
  width: 100%;
  padding: 0; /* No padding on the card itself, content handles it */
  /* Removed flex properties from here */
}

.card h2 {
  font-size: 1.6rem; /* Slightly smaller section titles */
  color: #34495e;
  margin-top: 0;
  margin-bottom: 15px;
  border-bottom: 1px solid #eee; /* Lighter, thinner border */
  padding-bottom: 10px;
  font-weight: 600;
}

.card p {
  font-size: 1.05rem; /* Slightly larger paragraph text for better readability */
  line-height: 1.7; /* Increased line height for better spacing */
  color: #444; /* Slightly darker grey for better contrast */
  margin-bottom: 12px; /* Adjusted margin for paragraphs */
}

.card p strong {
  color: #222; /* Even darker for strong emphasis */
  font-weight: 700; /* Bolder strong text */
}

/* List Styling */
.featuresList {
  list-style-type: none;
  padding: 0;
  margin-top: 15px;
  text-align: right; /* Align list items to right */
}

.featuresList li {
  font-size: 1rem; /* Slightly larger list items */
  padding: 12px 0;
  border-bottom: 1px solid #f0f0f0; /* Very light border */
  display: flex;
  align-items: flex-start;
  direction: rtl;
  line-height: 1.5; /* Consistent line height */
}

.featuresList li:last-child {
  border-bottom: none;
}

.featuresList li::before {
  content: '✓';
  color: #28a745; /* Standard green checkmark */
  font-size: 1.2rem; /* Larger checkmark */
  margin-left: 10px;
  margin-right: 0;
  font-weight: bold;
  flex-shrink: 0;
}

/* Storage Options Layout */
.storageOptions {
  display: flex;
  flex-wrap: wrap; /* Allow wrapping on smaller screens */
  justify-content: center; /* Center items horizontally */
  gap: 20px; /* Space between the two options */
  margin-top: 20px;
}

.storageOptionItem {
  flex: 1; /* Allow items to grow and shrink */
  min-width: 280px; /* Minimum width before wrapping */
  background-color: #f8f9fa; /* Light background for each option */
  border-radius: 8px;
  padding: 20px;
  box-shadow: 0 2px 10px rgba(0, 0, 0, 0.03);
  text-align: right; /* Align text right within each item */
}

.storageOptionItem h2 {
  font-size: 1.3rem;
  color: #007bff; /* Blue for option titles */
  border-bottom: 1px solid #dee2e6;
  padding-bottom: 10px;
  margin-bottom: 15px;
}

.storageOptionItem ul {
  list-style-type: none;
  padding: 0;
}

.storageOptionItem li {
  font-size: 0.95rem;
  line-height: 1.6;
  margin-bottom: 8px;
  color: #555;
}

/* Progress Bar (if used) - simplified */
.progressBarContainer {
  background-color: #e9ecef;
  border-radius: 6px;
  height: 8px;
  margin-top: 20px;
  overflow: hidden;
}

.progressBar {
  background: #007bff; /* Solid blue */
  height: 100%;
  border-radius: 6px;
  transition: width 0.4s ease-in-out;
}

/* Upgrade Button (if used) - simplified */
.upgradeButton {
  border: none;
  padding: 18px 24px;
  border-radius: 12px;
  cursor: pointer;
  transition: all 0.3s ease;
  font-size: 1.1rem;
  width: 100%;
  margin-bottom: 0;
  font-weight: 600;
  font-family: var(--font-family-primary);
  min-height: 60px;
  position: relative;
  overflow: hidden;
  background: linear-gradient(135deg, var(--primary-medium-blue) 0%, var(--primary-dark-blue) 100%);
  color: var(--white);
  box-shadow: 0 4px 15px rgba(76, 104, 192, 0.3);
}

.upgradeButton:hover {
  transform: translateY(-2px);
  box-shadow: 0 8px 25px rgba(76, 104, 192, 0.4);
}

.upgradeButton:active {
  transform: translateY(0);
}

/* Navigation Controls */
.navigationControls {
  display: flex;
  justify-content: center;
  gap: 20px;
  margin-top: 30px;
}

.navButton {
  border: none;
  padding: 18px 24px;
  border-radius: 12px;
  cursor: pointer;
  transition: all 0.3s ease;
  font-size: 1.1rem;
  width: 100%;
  margin-bottom: 0;
  font-weight: 600;
  font-family: var(--font-family-primary);
  min-height: 60px;
  position: relative;
  overflow: hidden;
  background: linear-gradient(135deg, var(--primary-medium-blue) 150%, var(--primary-dark-blue) 350%);
  color: var(--white);
  box-shadow: 0 4px 15px rgba(76, 104, 192, 0.3);
}

.navButton:hover {
  transform: translateY(-2px);
  box-shadow: 0 8px 25px rgba(76, 104, 192, 0.4);
}

.navButton:active {
  transform: translateY(0);
}

/* Responsive Adjustments */
@media (max-width: 768px) {
  .pageContainer {
    max-width: 100%;
    margin: 0; /* Remove margin for full height */
    padding: 20px;
    min-height: 100vh; /* Full viewport height */
    border-radius: 0; /* Remove border-radius for full screen */
    box-shadow: none; /* Remove shadow for full screen */
  }

  .header h1 {
    font-size: 1.8rem;
  }

  .header p {
    font-size: 0.95rem;
  }

  .stepContentContainer {
    min-height: 170vh; /* Increased height for smaller screens */
  }

  .card h2 {
    font-size: 1.4rem;
  }

  .card p {
    font-size: 0.95rem;
  }

  .featuresList li {
    font-size: 0.9rem;
    padding: 10px 0;
  }

  .featuresList li::before {
    font-size: 1.1rem;
  }

  .storageOptionItem {
    min-width: 100%; /* Stack items on small screens */
    padding: 15px;
  }

  .storageOptionItem h2 {
    font-size: 1.2rem;
  }

  .storageOptionItem li {
    font-size: 0.9rem;
  }

  .navButton {
    font-size: 0.9rem;
    padding: 10px 18px;
  }
}

@media (max-width: 480px) {
  .pageContainer {
    padding: 15px;
  }

  .header h1 {
    font-size: 1.6rem;
  }

  .header p {
    font-size: 0.9rem;
  }

  .stepContentContainer {
    min-height: 500px;
  }

  .card h2 {
    font-size: 1.2rem;
  }

  .card p {
    font-size: 0.9rem;
  }

  .featuresList li {
    font-size: 0.85rem;
  }

  .featuresList li::before {
    font-size: 1rem;
  }

  .storageOptionItem {
    padding: 10px;
  }

  .storageOptionItem h2 {
    font-size: 1.1rem;
  }

  .storageOptionItem li {
    font-size: 0.85rem;
  }

  .navigationControls {
    flex-direction: row; /* Make buttons side-by-side */
    justify-content: space-around; /* Distribute space evenly */
    gap: 10px;
    margin-top: auto; /* Push to the bottom */
    width: 100%; /* Ensure it takes full width */
    padding: 0 15px; /* Add some horizontal padding */
  }

  .navButton {
    width: auto; /* Allow buttons to size based on content */
    flex: 1; /* Allow buttons to grow and shrink */
    max-width: 150px; /* Limit max width for larger phones */
  }
}