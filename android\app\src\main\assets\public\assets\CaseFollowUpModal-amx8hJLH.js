import{R as I,r as o,j as e}from"./index-HUlFBKIW.js";import{x as R,y as $}from"./index-DhfUDJs-.js";import{p as g,g as M}from"./PermissionsService-Dhc9PbZp.js";import"./iconBase-BPj5F03O.js";const k="_modalContent_1phsd_12",O="_notesHeader_1phsd_41",E="_notesHeaderLeft_1phsd_56",F="_notesHeaderTitle_1phsd_62",G="_buttonRow_1phsd_70",U="_styledButton_1phsd_77",q="_notesContent_1phsd_104",z="_notesInnerContent_1phsd_112",J="_emptyNotesSpace_1phsd_118",K="_emptyNotesMessage_1phsd_126",Q="_emptyText_1phsd_131",V="_textareaSection_1phsd_138",W="_typeSection_1phsd_142",X="_textareaTitle_1phsd_146",Y="_textareaContainer_1phsd_154",Z="_styledInput_1phsd_159",ee="_buttonGroup_1phsd_193",te="_saveButton_1phsd_239",se="_savedNotesSection_1phsd_249",ne="_activeButton_1phsd_253",oe="_savedNotesList_1phsd_261",ae="_savedNoteItem_1phsd_271",le="_noteHeader_1phsd_312",ce="_noteType_1phsd_319",de="_noteContent_1phsd_325",re="_expandToggle_1phsd_334",ie="_deleteModalOverlay_1phsd_360",_e="_deleteModal_1phsd_360",he="_deleteModalHeader_1phsd_389",pe="_deleteModalContent_1phsd_402",me="_notePreview_1phsd_413",ue="_deleteModalActions_1phsd_430",xe="_cancelButton_1phsd_436",Ne="_confirmDeleteButton_1phsd_437",ve="_deletable_1phsd_556",ye="_nonDeletable_1phsd_573",t={modalContent:k,notesHeader:O,notesHeaderLeft:E,notesHeaderTitle:F,buttonRow:G,styledButton:U,notesContent:q,notesInnerContent:z,emptyNotesSpace:J,emptyNotesMessage:K,emptyText:Q,textareaSection:V,typeSection:W,textareaTitle:X,textareaContainer:Y,styledInput:Z,buttonGroup:ee,saveButton:te,savedNotesSection:se,activeButton:ne,savedNotesList:oe,savedNoteItem:ae,noteHeader:le,noteType:ce,noteContent:de,expandToggle:re,deleteModalOverlay:ie,deleteModal:_e,deleteModalHeader:he,deleteModalContent:pe,notePreview:me,deleteModalActions:ue,cancelButton:xe,confirmDeleteButton:Ne,deletable:ve,nonDeletable:ye},be=I.memo(({onClose:je,onSave:i,caseId:D,userId:_,savedNotes:a=[]})=>{const[h,C]=o.useState(""),[l,f]=o.useState(""),[d,p]=o.useState(!1),[x,b]=o.useState(!1),[Ce,fe]=o.useState(null),[N,T]=o.useState(null),[B,v]=o.useState(!1),[c,y]=o.useState(null),[m,w]=o.useState(!1);o.useEffect(()=>{(()=>{if(_){const n=g.getCurrentUserRole(_),r=g.hasPermission(n,"deleteData");w(r)}})()},[_]);const S=o.useCallback(s=>{if(!m){console.log("🚫 المستخدم لا يملك صلاحية حذف البيانات");return}const n=setTimeout(()=>{y(s),v(!0)},800);T(n)},[m]),j=o.useCallback(()=>{N&&(clearTimeout(N),T(null))},[N]),H=async()=>{if(!(!l||!h.trim())){p(!0);try{const n={id:`${D}-note-${Date.now()}`,type:l,analysis:h.trim(),isDeleted:!1,userId:_,createdAt:new Date().toISOString()};i&&await i(n),C(""),f(""),alert("تم حفظ الملاحظة بنجاح")}catch(s){console.error("خطأ في حفظ الملاحظة:",s),alert("حدث خطأ أثناء حفظ الملاحظة. يرجى المحاولة مرة أخرى.")}finally{p(!1)}}},L=async()=>{if(c!==null){p(!0);try{const n=M()==="online"?"أونلاين":"محلي";if(i){const r=a.filter((Te,A)=>A!==c);await i({notes:r,deleted:!0})}v(!1),y(null),alert(`تم حذف الملاحظة بنجاح من الحساب ${n}`)}catch(s){const r=M()==="online"?"الأونلاين":"المحلي";console.error("خطأ في حذف الملاحظة:",s),alert(`حدث خطأ أثناء حذف الملاحظة من الحساب ${r}. يرجى المحاولة مرة أخرى.`)}finally{p(!1)}}},P=()=>{v(!1),y(null)},u=s=>e.jsx("button",{className:`${t.styledButton} ${l===s?t.activeButton:""}`,onClick:()=>f(s),children:s});return e.jsxs("div",{className:t.modalContent,children:[e.jsx("div",{className:t.notesHeader,children:e.jsx("div",{className:t.notesHeaderLeft,children:e.jsx("h2",{className:t.notesHeaderTitle,children:"الملاحظات"})})}),e.jsx("div",{className:t.notesContent,children:e.jsxs("div",{className:t.notesInnerContent,children:[a.length>0?e.jsxs("div",{className:t.savedNotesSection,children:[e.jsx("h3",{className:t.textareaTitle,children:"سجل البيانات"}),e.jsx("ul",{className:t.savedNotesList,children:a.map((s,n)=>e.jsxs("li",{className:`${t.savedNoteItem} ${m?t.deletable:t.nonDeletable}`,...m&&{onMouseDown:()=>S(n),onMouseUp:j,onMouseLeave:j,onTouchStart:()=>S(n),onTouchEnd:j},children:[e.jsx("div",{className:t.noteHeader,children:e.jsx("span",{className:t.noteType,children:s.type})}),e.jsx("div",{className:t.noteContent,children:s.analysis})]},n))})]}):e.jsx("div",{className:t.emptyNotesSpace,children:e.jsxs("div",{className:t.emptyNotesMessage,children:[e.jsx("h3",{className:t.textareaTitle,children:"سجل البيانات"}),e.jsx("p",{className:t.emptyText,children:"لا توجد ملاحظات محفوظة بعد"})]})}),x&&e.jsxs(e.Fragment,{children:[e.jsxs("div",{className:t.typeSection,children:[e.jsx("h3",{className:t.textareaTitle,children:"اختيار نوع الملاحظة"}),e.jsxs("div",{className:t.buttonRow,children:[u("رقم صادر"),u("رقم وارد"),u("رقم إعلان"),u("أخرى")]})]}),e.jsxs("div",{className:t.textareaSection,children:[e.jsx("h3",{className:t.textareaTitle,children:"تحرير ملاحظات"}),e.jsx("div",{className:t.textareaContainer,children:e.jsx("textarea",{className:t.styledInput,value:h,onChange:s=>C(s.target.value),placeholder:`اكتب ملاحظة ${l||""}`,disabled:!l})})]}),e.jsx("div",{className:t.buttonGroup,children:e.jsx("button",{onClick:H,disabled:!l||!h.trim()||d,className:t.saveButton,children:d?"جاري الحفظ...":"حفظ"})})]})]})}),e.jsxs("div",{className:t.expandToggle,onClick:()=>b(s=>!s),children:[x?"عرض أقل":"عرض المزيد",x?e.jsx(R,{}):e.jsx($,{})]}),B&&e.jsx("div",{className:t.deleteModalOverlay,children:e.jsxs("div",{className:t.deleteModal,children:[e.jsx("div",{className:t.deleteModalHeader,children:e.jsx("h3",{children:"تأكيد الحذف"})}),e.jsxs("div",{className:t.deleteModalContent,children:[e.jsx("p",{children:"هل أنت متأكد من حذف هذه الملاحظة؟"}),c!==null&&a[c]&&e.jsxs("div",{className:t.notePreview,children:[e.jsxs("strong",{children:[a[c].type,": "]}),e.jsx("span",{children:a[c].analysis})]})]}),e.jsxs("div",{className:t.deleteModalActions,children:[e.jsx("button",{className:t.cancelButton,onClick:P,disabled:d,children:"إلغاء"}),e.jsx("button",{className:t.confirmDeleteButton,onClick:L,disabled:d,children:d?"جاري الحذف...":"حذف"})]})]})})]})});export{be as default};
