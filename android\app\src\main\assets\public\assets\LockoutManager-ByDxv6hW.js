class i{constructor(t){this.type=t,this.storageKey=`${t}Lockout`}checkLockoutStatus(){const t=localStorage.getItem(this.storageKey);if(!t)return{isLocked:!1,attempts:0};const{timestamp:e,attempts:o}=JSON.parse(t),s=Date.now();if(s-e>=9e5)return localStorage.removeItem(this.storageKey),{isLocked:!1,attempts:0};if(o>=3){const n=Math.ceil((9e5-(s-e))/1e3/60);return{isLocked:!0,attempts:o,remainingMinutes:n,message:this.getLockoutMessage(n)}}return{isLocked:!1,attempts:o}}recordFailedAttempt(t=0){const e=t+1,o={timestamp:Date.now(),attempts:e};if(localStorage.setItem(this.storageKey,JSON.stringify(o)),e>=3){const s=Math.ceil(15);return{isLocked:!0,attempts:e,remainingMinutes:s,message:this.getLockoutMessage(s)}}return{isLocked:!1,attempts:e}}clearLockout(){localStorage.removeItem(this.storageKey)}getLockoutMessage(t){const e=this.type==="login"?"تسجيل الدخول":"إنشاء الحساب",o=t>1?"دقائق":"دقيقة";return`تم قفل ${e} مؤقتًا بسبب محاولات متكررة. يرجى المحاولة مرة أخرى بعد ${t} ${o}.`}getRemainingAttempts(t=0){return Math.max(0,3-t)}}const a=new i("login"),r=new i("signup"),u=()=>a.checkLockoutStatus(),g=()=>r.checkLockoutStatus(),k=c=>a.recordFailedAttempt(c),l=c=>r.recordFailedAttempt(c),L=()=>a.clearLockout(),m=()=>r.clearLockout();export{L as a,g as b,u as c,m as d,l as e,k as r};
