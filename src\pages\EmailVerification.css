.email-verification-container {
  display: flex;
  justify-content: center;
  align-items: center;
  min-height: 100vh;
  background-color: #f0f2f5;
  padding: 20px;
  box-sizing: border-box;
}

.email-verification-card {
  background-color: #fff;
  padding: 40px;
  border-radius: 8px;
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.1);
  text-align: center;
  max-width: 500px;
  width: 100%;
}

.email-verification-card h2 {
  color: #333;
  margin-bottom: 20px;
  font-size: 28px;
}

.email-verification-card p {
  color: #666;
  font-size: 16px;
  line-height: 1.6;
  margin-bottom: 25px;
}

.button-group button {
  background-color: #007bff;
  color: white;
  padding: 12px 25px;
  border: none;
  border-radius: 5px;
  cursor: pointer;
  font-size: 16px;
  margin: 10px;
  transition: background-color 0.3s ease;
  width: calc(100% - 20px);
  box-sizing: border-box;
}

.button-group button:hover {
  background-color: #0056b3;
}

.button-group button:disabled {
  background-color: #cccccc;
  cursor: not-allowed;
}

.logout-button {
  background-color: #dc3545 !important;
}

.logout-button:hover {
  background-color: #c82333 !important;
}

.error-message {
  color: #dc3545;
  background-color: #f8d7da;
  border: 1px solid #f5c6cb;
  padding: 10px;
  border-radius: 5px;
  margin-bottom: 15px;
  font-size: 14px;
}

.success-message {
  color: #28a745;
  background-color: #d4edda;
  border: 1px solid #c3e6cb;
  padding: 10px;
  border-radius: 5px;
  margin-bottom: 15px;
  font-size: 14px;
}
