const __vite__mapDeps=(i,m=__vite__mapDeps,d=(m.f||(m.f=["./PermissionsService-Dhc9PbZp.js","./index-HUlFBKIW.js","./index-z-WxkLDE.css","./CaseFilters-CPt9izwP.js"])))=>i.map(i=>d[i]);
var dt=t=>{throw TypeError(t)};var Qe=(t,i,l)=>i.has(t)||dt("Cannot "+l);var I=(t,i,l)=>(Qe(t,i,"read from private field"),l?l.call(t):i.get(t)),Ce=(t,i,l)=>i.has(t)?dt("Cannot add the same private member more than once"):i instanceof WeakSet?i.add(t):i.set(t,l),_e=(t,i,l,o)=>(Qe(t,i,"write to private field"),o?o.call(t,l):i.set(t,l),l),je=(t,i,l)=>(Qe(t,i,"access private method"),l);import{x as Ut,S as en,y as tn,z as ut,A as nn,B as Ct,C as _t,r as c,R as U,j as e,c as jt,_ as Be,d as sn,b as rn,g as an,i as on,L as ln}from"./index-HUlFBKIW.js";import{p as mt,H as ye,u as Xe,x as ft,y as ht,m as Ne,I as Se,d as Ze,n as Nt,k as cn,J as dn,F as vt,K as bt,s as pt,D as un,L as mn,q as fn,M as hn,N as pn}from"./index-DhfUDJs-.js";import{T as xt}from"./TopBar-rd4Lqjij.js";import{a as xn,b as gn,c as Cn}from"./CaseFilters-CPt9izwP.js";import{a as ue,g as Ee,p as Ue,e as _n,f as jn}from"./PermissionsService-Dhc9PbZp.js";import{c as Nn,a as vn,h as bn,b as yn,d as Sn,e as yt}from"./ReportDetailsLogic-CEDnmfXZ.js";import{notifyTaskCreated as $e,cacheManager as we}from"./CacheManager-c0VPvbsl.js";import St from"./AddDeferral-BXtzfjJf.js";import wt from"./AddAction-BZauwEMb.js";import wn from"./CaseFollowUpModal-amx8hJLH.js";import"./iconBase-BPj5F03O.js";import"./sweetalert2.esm.all-DWO__QVt.js";import"./AddDeferralAction.module-B_E75l-L.js";var Bn=Ut(),me,fe,W,se,re,ke,et,gt,En=(gt=class extends en{constructor(i,l){super();Ce(this,re);Ce(this,me);Ce(this,fe);Ce(this,W);Ce(this,se);_e(this,me,i),this.setOptions(l),this.bindMethods(),je(this,re,ke).call(this)}bindMethods(){this.mutate=this.mutate.bind(this),this.reset=this.reset.bind(this)}setOptions(i){var o;const l=this.options;this.options=I(this,me).defaultMutationOptions(i),tn(this.options,l)||I(this,me).getMutationCache().notify({type:"observerOptionsUpdated",mutation:I(this,W),observer:this}),l!=null&&l.mutationKey&&this.options.mutationKey&&ut(l.mutationKey)!==ut(this.options.mutationKey)?this.reset():((o=I(this,W))==null?void 0:o.state.status)==="pending"&&I(this,W).setOptions(this.options)}onUnsubscribe(){var i;this.hasListeners()||(i=I(this,W))==null||i.removeObserver(this)}onMutationUpdate(i){je(this,re,ke).call(this),je(this,re,et).call(this,i)}getCurrentResult(){return I(this,fe)}reset(){var i;(i=I(this,W))==null||i.removeObserver(this),_e(this,W,void 0),je(this,re,ke).call(this),je(this,re,et).call(this)}mutate(i,l){var o;return _e(this,se,l),(o=I(this,W))==null||o.removeObserver(this),_e(this,W,I(this,me).getMutationCache().build(I(this,me),this.options)),I(this,W).addObserver(this),I(this,W).execute(i)}},me=new WeakMap,fe=new WeakMap,W=new WeakMap,se=new WeakMap,re=new WeakSet,ke=function(){var l;const i=((l=I(this,W))==null?void 0:l.state)??nn();_e(this,fe,{...i,isPending:i.status==="pending",isSuccess:i.status==="success",isError:i.status==="error",isIdle:i.status==="idle",mutate:this.mutate,reset:this.reset})},et=function(i){Ct.batch(()=>{var l,o,a,B,m,w,h,v;if(I(this,se)&&this.hasListeners()){const T=I(this,fe).variables,V=I(this,fe).context;(i==null?void 0:i.type)==="success"?((o=(l=I(this,se)).onSuccess)==null||o.call(l,i.data,T,V),(B=(a=I(this,se)).onSettled)==null||B.call(a,i.data,null,T,V)):(i==null?void 0:i.type)==="error"&&((w=(m=I(this,se)).onError)==null||w.call(m,i.error,T,V),(v=(h=I(this,se)).onSettled)==null||v.call(h,void 0,i.error,T,V))}this.listeners.forEach(T=>{T(I(this,fe))})})},gt);function Tn(t,i){return typeof t=="function"?t(...i):!!t}function Ln(){}function kn(t,i){const l=_t(),[o]=c.useState(()=>new En(l,t));c.useEffect(()=>{o.setOptions(t)},[o,t]);const a=c.useSyncExternalStore(c.useCallback(m=>o.subscribe(Ct.batchCalls(m)),[o]),()=>o.getCurrentResult(),()=>o.getCurrentResult()),B=c.useCallback((m,w)=>{o.mutate(m,w).catch(Ln)},[o]);if(a.error&&Tn(o.options.throwOnError,[a.error]))throw a.error;return{...a,mutate:B,mutateAsync:a.mutate}}const $n="_coloredCardsContainerHorizontal_jz3h0_8",zn="_coloredCard_jz3h0_8",In="_simpleCard_jz3h0_48",Mn="_simpleCardHeader_jz3h0_70",Fn="_timelineCard_jz3h0_193",Hn="_cardHeader_jz3h0_199",An="_cardHeaderInSimple_jz3h0_212",On="_cardHeaderContent_jz3h0_246",Rn="_cardTitle_jz3h0_252",Dn="_cardPrimaryContent_jz3h0_263",Vn="_cardExpandedContent_jz3h0_264",Pn="_expanded_jz3h0_282",Jn="_expandedContentDivider_jz3h0_288",Gn="_dataFieldHorizontal_jz3h0_301",Yn="_fieldLabelHorizontal_jz3h0_343",qn="_fieldValueContainerHorizontal_jz3h0_351",Wn="_valueWithActionHorizontal_jz3h0_358",Kn="_valueTextHorizontal_jz3h0_367",Qn="_longTextPreview_jz3h0_380",Xn="_longTextFull_jz3h0_399",Zn="_moreTextIndicator_jz3h0_413",Un="_longTextContainer_jz3h0_423",es="_expandToggle_jz3h0_431",ts="_timelineScrollContainer_jz3h0_502",ns="_timelineEntry_jz3h0_508",ss="_timelineDate_jz3h0_522",rs="_timelineDescription_jz3h0_529",as="_noTimeline_jz3h0_537",is="_booleanValue_jz3h0_545",os="_trueValue_jz3h0_555",ls="_falseValue_jz3h0_560",cs="_editIconButtonHorizontal_jz3h0_566",ds="_referralButtonHorizontal_jz3h0_594",us="_fullCaseNumberEditContainer_jz3h0_666",ms="_caseNumberFieldsContainer_jz3h0_670",fs="_caseNumberField_jz3h0_670",hs="_inlineLabel_jz3h0_696",ps="_editInput_jz3h0_704",xs="_textareaInput_jz3h0_704",gs="_errorText_jz3h0_728",Cs="_transferDegreeSection_jz3h0_737",_s="_transferDegreeBox_jz3h0_743",js="_partiesTheme_jz3h0_758",Ns="_transferHeader_jz3h0_767",vs="_transferButton_jz3h0_771",bs="_identificationTheme_jz3h0_777",ys="_statusOptionsContainer_jz3h0_871",Ss="_questionText_jz3h0_879",ws="_statusOptions_jz3h0_871",Bs="_statusOptionButton_jz3h0_894",Es="_selected_jz3h0_915",Ts="_dateInputContainer_jz3h0_927",Ls="_dateLabel_jz3h0_935",ks="_dateInput_jz3h0_927",$s="_saveEditButton_jz3h0_961",zs="_cancelEditButton_jz3h0_962",Is="_transferConfirmButton_jz3h0_1005",Ms="_transferTitleSection_jz3h0_1035",Fs="_expandButton_jz3h0_1041",Hs="_transferIcon_jz3h0_1054",As="_transferTitle_jz3h0_1035",Os="_transferContent_jz3h0_1065",Rs="_transferInfoRow_jz3h0_1071",Ds="_transferLabel_jz3h0_1086",Vs="_transferValue_jz3h0_1091",Ps="_transferConfirmationOverlay_jz3h0_1126",Js="_transferConfirmationDialog_jz3h0_1140",Gs="_transferConfirmationIcon_jz3h0_1167",Ys="_transferConfirmationTitle_jz3h0_1181",qs="_transferConfirmationMessage_jz3h0_1188",Ws="_transferConfirmationActions_jz3h0_1195",Ks="_transferCancelButton_jz3h0_1202",Qs="_fullReportButton_jz3h0_1403",Xs="_fullReportModalOverlay_jz3h0_1434",Zs="_fullReportModal_jz3h0_1434",Us="_fullReportModalHeader_jz3h0_1485",er="_closeModalButton_jz3h0_1502",tr="_fullReportModalContent_jz3h0_1523",n={coloredCardsContainerHorizontal:$n,coloredCard:zn,simpleCard:In,simpleCardHeader:Mn,timelineCard:Fn,cardHeader:Hn,cardHeaderInSimple:An,cardHeaderContent:On,cardTitle:Rn,cardPrimaryContent:Dn,cardExpandedContent:Vn,expanded:Pn,expandedContentDivider:Jn,dataFieldHorizontal:Gn,fieldLabelHorizontal:Yn,fieldValueContainerHorizontal:qn,valueWithActionHorizontal:Wn,valueTextHorizontal:Kn,longTextPreview:Qn,longTextFull:Xn,moreTextIndicator:Zn,longTextContainer:Un,expandToggle:es,timelineScrollContainer:ts,timelineEntry:ns,timelineDate:ss,timelineDescription:rs,noTimeline:as,booleanValue:is,trueValue:os,falseValue:ls,editIconButtonHorizontal:cs,referralButtonHorizontal:ds,fullCaseNumberEditContainer:us,caseNumberFieldsContainer:ms,caseNumberField:fs,inlineLabel:hs,editInput:ps,textareaInput:xs,errorText:gs,transferDegreeSection:Cs,transferDegreeBox:_s,partiesTheme:js,transferHeader:Ns,transferButton:vs,identificationTheme:bs,statusOptionsContainer:ys,questionText:Ss,statusOptions:ws,statusOptionButton:Bs,selected:Es,dateInputContainer:Ts,dateLabel:Ls,dateInput:ks,saveEditButton:$s,cancelEditButton:zs,transferConfirmButton:Is,transferTitleSection:Ms,expandButton:Fs,transferIcon:Hs,transferTitle:As,transferContent:Os,transferInfoRow:Rs,transferLabel:Ds,transferValue:Vs,transferConfirmationOverlay:Ps,transferConfirmationDialog:Js,transferConfirmationIcon:Gs,transferConfirmationTitle:Ys,transferConfirmationMessage:qs,transferConfirmationActions:Ws,transferCancelButton:Ks,fullReportButton:Qs,fullReportModalOverlay:Xs,fullReportModal:Zs,fullReportModalHeader:Us,closeModalButton:er,fullReportModalContent:tr};function nr(t){const i=[];if(!t.history||t.history.length===0){if(t.caseDate){const l=new Date(t.caseDate).toLocaleDateString("ar-EG");let o="";t.caseStatus==="دعوى قضائية"?o=`تم رفع دعوى قضائية بتاريخ ${l}`:t.caseStatus==="محضر"?o=`تم تحرير المحضر بتاريخ ${l}`:o=`تم إنشاء ${t.caseStatus} بتاريخ ${l}`,i.push({date:l,event:o})}else if(t.createdAt){const l=new Date(t.createdAt).toLocaleDateString("ar-EG");i.push({date:l,event:`تم إنشاء ${t.caseStatus||"الملف"}`})}}return t.history&&t.history.length>0&&Nn(t.history).forEach(o=>{if(o.type==="completed_action"&&o.completedAt){const a=new Date(o.completedAt).toLocaleDateString("ar-EG");i.push({date:a,event:`تم تنفيذ إجراء: ${o.description||"غير محدد"}`})}else if(o.type==="completed_deferral"&&o.completedAt){const a=new Date(o.completedAt).toLocaleDateString("ar-EG");let B=`تم حضور جلسة: ${o.description||"غير محدد"}`;o.deferralDescription&&o.deferralDescription.trim()&&(B+=` - ${o.deferralDescription}`),i.push({date:a,event:B})}else if(o.type==="case_created"){const a=new Date(o.timestamp||o.createdAt).toLocaleDateString("ar-EG");i.push({date:a,event:o.action||"تم إنشاء القضية"})}else if(o.type==="status_transfer"&&o.timestamp){const a=new Date(o.timestamp).toLocaleDateString("ar-EG");i.push({date:a,event:o.action||`تم تحويل حالة الملف من "${o.oldStatus}" إلى "${o.newStatus}"`})}else if(o.type==="degree_transfer"&&o.timestamp){const a=new Date(o.timestamp).toLocaleDateString("ar-EG");i.push({date:a,event:o.action||`تم تحويل الدرجة إلى ${o.newCaseDegree}`})}else if(o.type==="report_created"&&o.timestamp){const a=new Date(o.timestamp).toLocaleDateString("ar-EG");i.push({date:a,event:o.action||"تم كتابة محضر"})}else if(o.type==="lawsuit_created"&&o.timestamp){const a=new Date(o.timestamp).toLocaleDateString("ar-EG");i.push({date:a,event:o.action||"تم رفع دعوى قضائية"})}}),t.timeline&&t.timeline.length>0&&t.timeline.forEach(l=>{i.push({date:l.date,event:l.description})}),i.sort((l,o)=>{try{const a=new Date(l.date.split("/").reverse().join("-")),B=new Date(o.date.split("/").reverse().join("-"));return isNaN(a)||isNaN(B)?0:B-a}catch{return 0}}),i}const sr=U.memo(({caseData:t,cardClassName:i})=>{const l=c.useMemo(()=>nr(t),[t]),[o,a]=c.useState(!1);if(!t)return null;const B=l.slice(-3),m=()=>{a(!0),document.body.classList.add("modal-open")},w=()=>{a(!1),document.body.classList.remove("modal-open")};return c.useEffect(()=>{const h=v=>{v.key==="Escape"&&o&&w()};return o&&document.addEventListener("keydown",h),()=>{document.removeEventListener("keydown",h),document.body.classList.remove("modal-open")}},[o]),e.jsxs(e.Fragment,{children:[i?e.jsxs(e.Fragment,{children:[e.jsx("div",{className:n.cardHeaderInSimple,children:e.jsxs("div",{className:n.cardHeaderContent,children:[e.jsx("div",{className:n.cardTitle,children:"التقرير"}),e.jsxs("button",{className:n.fullReportButton,onClick:m,title:"عرض التقرير كامل",children:[e.jsx(mt,{}),"عرض التقرير كامل"]})]})}),e.jsx("div",{className:n.cardPrimaryContent,children:B.length>0?e.jsx("div",{className:n.timelineScrollContainer,children:B.map((h,v)=>e.jsxs("div",{className:n.timelineEntry,children:[e.jsx("span",{className:n.timelineDate,children:h.date}),e.jsx("span",{className:n.timelineDescription,children:h.event})]},v))}):e.jsx("div",{className:n.noTimeline,children:"لا توجد أحداث زمنية مسجلة"})})]}):e.jsxs("div",{className:`${n.coloredCard} ${n.timelineCard}`,children:[e.jsx("div",{className:n.cardHeader,children:e.jsxs("div",{className:n.cardHeaderContent,children:[e.jsx("div",{className:n.cardTitle,children:"التقرير"}),e.jsxs("button",{className:n.fullReportButton,onClick:m,title:"عرض التقرير كامل",children:[e.jsx(mt,{}),"عرض التقرير كامل"]})]})}),e.jsx("div",{className:n.cardPrimaryContent,children:B.length>0?e.jsx("div",{className:n.timelineScrollContainer,children:B.map((h,v)=>e.jsxs("div",{className:n.timelineEntry,children:[e.jsx("span",{className:n.timelineDate,children:h.date}),e.jsx("span",{className:n.timelineDescription,children:h.event})]},v))}):e.jsx("div",{className:n.noTimeline,children:"لا توجد أحداث زمنية مسجلة"})})]}),o&&Bn.createPortal(e.jsx("div",{className:n.fullReportModalOverlay,onClick:w,style:{position:"fixed",top:0,left:0,right:0,bottom:0,width:"100vw",height:"100vh",zIndex:2147483647,background:"rgba(0, 0, 0, 0.8)",display:"flex",alignItems:"center",justifyContent:"center",padding:"20px",margin:0,boxSizing:"border-box"},children:e.jsxs("div",{className:n.fullReportModal,onClick:h=>h.stopPropagation(),style:{background:"white",borderRadius:"20px",maxWidth:"800px",width:"90%",maxHeight:"85vh",display:"flex",flexDirection:"column",boxShadow:"0 25px 80px rgba(0, 0, 0, 0.5)",position:"relative",margin:"auto",zIndex:2147483647,overflow:"hidden"},children:[e.jsxs("div",{className:n.fullReportModalHeader,children:[e.jsx("h3",{children:"التقرير الكامل"}),e.jsx("button",{className:n.closeModalButton,onClick:w,children:"×"})]}),e.jsx("div",{className:n.fullReportModalContent,children:l.length>0?e.jsx("div",{className:n.timelineScrollContainer,children:l.map((h,v)=>e.jsxs("div",{className:n.timelineEntry,children:[e.jsx("span",{className:n.timelineDate,children:h.date}),e.jsx("span",{className:n.timelineDescription,children:h.event})]},v))}):e.jsx("div",{className:n.noTimeline,children:"لا توجد أحداث زمنية مسجلة"})})]})}),document.body)]})}),rr=U.memo(({caseData:t,currentUser:i,onCaseDataUpdate:l,onJudgmentDetected:o})=>{const a=jt(),B=_t(),[m,w]=c.useState(null),[h,v]=c.useState(""),[T,V]=c.useState(""),[S,j]=c.useState(""),[F,x]=c.useState(!1),[P,J]=c.useState(!1),[L,M]=c.useState(""),[g,H]=c.useState(""),[G,A]=c.useState(!1),[O,K]=c.useState(!1),[ee,Z]=c.useState(!1),[$,te]=c.useState(""),[ae,ie]=c.useState(""),[oe,le]=c.useState(""),[k,R]=c.useState({parties:!1,identification:!1,location:!1}),d=t!=null&&t.caseDegree?xn[t.caseDegree]||[]:[],_=[{label:"اسم الموكل",value:t.clientName,field:"clientName",required:!0,type:"text",primary:!0},{label:"اسم الخصم",value:t.opponentName||"غير محدد",field:"opponentName",required:!1,type:"text",primary:!0},{label:"الوصف العام للقضية",value:t.caseDescription||"غير محدد",field:"caseDescription",required:!1,type:"textarea",primary:!1}],p=[...t.caseStatus!=="قيد النظر"?[{label:t.caseStatus==="محضر"?"رقم المحضر الكامل":"رقم القضية الكامل",value:t.fullCaseNumber||"—",field:"fullCaseNumber",required:!0,readOnly:!1,type:"text",primary:!0,isFullCaseNumber:!0}]:[],...t.caseStatus==="دعوى قضائية"?[{label:"نوع الدعوى",value:t.caseCategory||"غير محدد",field:"caseCategory",required:!0,type:"select",options:d,primary:!0}]:[],...t.caseStatus==="دعوى قضائية"?[{label:"درجة الدعوى",value:t.caseDegree||"غير محدد",field:"caseDegree",required:!0,type:"select",options:gn,primary:!1}]:[]],y=t.caseStatus==="قيد النظر"?[{label:"مكان الجهة المختصة",value:t.reportLocation||"غير محدد",field:"reportLocation",required:!0,type:"text",primary:!0}]:t.caseStatus==="محضر"?[{label:"مكان الجهة المختصة",value:t.reportLocation||"غير محدد",field:"reportLocation",required:!0,type:"text",primary:!0}]:t.caseStatus==="دعوى قضائية"?[{label:"رقم الدائرة",value:t.circleNumber||"غير محدد",field:"circleNumber",required:!1,type:"text",primary:!0},{label:"مكان المحكمة",value:t.courtLocation||"غير محدد",field:"courtLocation",required:!0,type:"text",primary:!0,isCourtReferral:!0},{label:"الإحالة للخبراء",value:t.expertReferralStatus||"لم يتم الإحالة للخبراء",field:"expertReferralStatus",required:!1,type:"text",primary:!1,isExpertReferral:!0}]:[],E=s=>{switch(s){case"parties":return t.caseStatus&&t.caseStatus!=="غير محدد"?t.caseStatus:"الأطراف والوصف";case"identification":return t.caseStatus==="محضر"?"ترقيم المحضر":t.caseStatus==="دعوى قضائية"?t.caseDegree&&t.caseDegree!=="غير محدد"?t.caseDegree:"ترقيم القضية":"تصنيف الملف";case"location":return t.caseStatus==="قيد النظر"||t.caseStatus==="محضر"?t.reportLocation&&t.reportLocation!=="غير محدد"?t.reportLocation:"الجهة المختصة":t.caseStatus==="دعوى قضائية"?t.courtLocation&&t.courtLocation!=="غير محدد"?t.courtLocation:"المحكمة المختصة":"معلومات المكان";default:return s}},ce=s=>{switch(s){case"parties":return e.jsx(vt,{});case"identification":return e.jsx(dn,{});case"location":return e.jsx(cn,{});default:return e.jsx(Nt,{})}},tt=[{title:E("parties"),icon:ce("parties"),items:_,type:"parties"},{title:E("identification"),icon:ce("identification"),items:p,type:"identification"},{title:E("location"),icon:ce("location"),items:y,type:"location"}].filter(s=>s.items.length>0),Bt=s=>{R(r=>({...r,[s]:!r[s]}))},ze=kn({mutationFn:async({caseId:s,updateData:r})=>{await ue(i.uid,s,r)},onSuccess:(s,{caseId:r,updateData:f})=>{B.setQueryData(["case",r],z=>({...z,...f})),l&&l(f),w(null),v(""),V(""),j("");const b=Ee();alert(`تم حفظ التعديل بنجاح في الحساب ${b==="online"?"الأونلاين":"المحلي"}!`)},onError:s=>{console.error("Error saving field:",s),j("حدث خطأ أثناء حفظ التعديل: "+s.message)},onSettled:()=>{x(!1)}}),Et=(s,r)=>{var f;if(s.field==="caseDegree"&&lt()){Tt();return}if(s.isExpertReferral){Rt();return}w({...s,groupType:(f=tt.find(b=>b.items.some(z=>z.field===s.field)))==null?void 0:f.type}),v(r==="غير محدد"||r==="—"?"":r||""),V(""),j(""),s.isFullCaseNumber&&t.caseStatus==="دعوى قضائية"?(J(!0),M(t.caseNumber||""),H(t.caseYear||"")):s.isFullCaseNumber&&t.caseStatus==="محضر"&&(J(!0),M(t.reportNumber||""),H(t.caseYear||""))},Ie=()=>{if(!m||!t||!i){j("لا يمكن حفظ التعديل حالياً. يرجى تحديث الصفحة.");return}if(m.isFullCaseNumber&&t.caseStatus==="دعوى قضائية"){if(!L.trim()){j("رقم القضية مطلوب");return}if(!g.trim()){j("السنة القضائية مطلوبة");return}if(!/^\d+$/.test(L.trim())){j("رقم القضية يجب أن يحتوي على أرقام فقط");return}if(!/^\d{4}$/.test(g.trim())){j("السنة القضائية يجب أن تتكون من 4 أرقام فقط");return}j(""),x(!0);const b={fullCaseNumber:`${L.trim()}/${g.trim()}`,caseNumber:L.trim(),caseYear:g.trim()};ze.mutate({caseId:t.id,updateData:b}),J(!1);return}if(m.isFullCaseNumber&&t.caseStatus==="محضر"){if(!L.trim()){j("رقم المحضر مطلوب");return}if(!g.trim()){j("سنة المحضر مطلوبة");return}if(!/^\d+$/.test(L.trim())){j("رقم المحضر يجب أن يحتوي على أرقام فقط");return}if(!/^\d{4}$/.test(g.trim())){j("سنة المحضر يجب أن تتكون من 4 أرقام فقط");return}j(""),x(!0);const b={fullCaseNumber:`${L.trim()}/${g.trim()}`,reportNumber:L.trim(),caseYear:g.trim()};ze.mutate({caseId:t.id,updateData:b}),J(!1);return}if(m.required&&!h.trim()&&m.type!=="select"){j(`${m.label} مطلوب`);return}let s=h.trim();if(m.type==="select"){if(h==="أخرى"){if(!T.trim()){j('يرجى إدخال قيمة مخصصة لـ "أخرى"');return}s=T.trim()}if(m.required&&!s){j(`${m.label} مطلوب`);return}m.field==="referredToExpert"&&(s=s==="نعم")}if(m.field==="reportNumber"&&s&&!/^\d+$/.test(s)){j("رقم المحضر يجب أن يحتوي على أرقام فقط");return}if(m.field==="caseNumber"&&s&&!/^\d+$/.test(s)){j("رقم القضية يجب أن يحتوي على أرقام فقط");return}if(m.field==="caseYear"&&s&&!/^\d{4}$/.test(s)){j("سنة القضية يجب أن تتكون من 4 أرقام فقط");return}j(""),x(!0);const r={[m.field]:s};if(m.field==="caseStatus"){const f=s;let b={};if(f==="محضر"||f==="قيد النظر")b={caseNumber:"",caseYear:"",circleNumber:"",caseDegree:"",caseCategory:"",courtLocation:"",fullCaseNumber:""};else if(f==="دعوى قضائية"){b={reportLocation:"",reportNumber:""};const z=r.hasOwnProperty("caseNumber")?r.caseNumber:t.caseNumber,C=r.hasOwnProperty("caseYear")?r.caseYear:t.caseYear;z&&C&&z!=="غير محدد"&&C!=="غير محدد"?r.fullCaseNumber=`${z}/${C}`:r.fullCaseNumber="",(!t.caseYear||t.caseYear==="غير محدد")&&(r.caseYear=new Date().getFullYear().toString())}Object.assign(r,b)}else if(m.field==="reportNumber"&&t.caseStatus==="محضر")r.fullCaseNumber=s;else if((m.field==="caseNumber"||m.field==="caseYear")&&t.caseStatus==="دعوى قضائية"){const f=m.field==="caseNumber"?s:t.caseNumber,b=m.field==="caseYear"?s:t.caseYear;f&&b&&f!=="غير محدد"&&b!=="غير محدد"?r.fullCaseNumber=`${f}/${b}`:r.fullCaseNumber=""}m.field==="caseDegree"&&t.caseStatus==="دعوى قضائية"&&(r.caseCategory=""),ze.mutate({caseId:t.id,updateData:r})},Me=()=>{w(null),v(""),V(""),j(""),J(!1),M(""),H("")},Tt=()=>{A(!0)},Lt=async()=>{try{const s={...t,isHidden:!0,hiddenAt:new Date().toISOString(),updatedAt:new Date().toISOString()};await ue(i.uid,t.id,s);const r={clientName:t.clientName,caseDescription:t.caseDescription,opponentName:t.opponentName,caseCategory:t.caseCategory,courtLocation:t.courtLocation,originalCaseId:t.id,originalCaseDegree:t.caseDegree,originalCaseNumber:t.fullCaseNumber,originalCourtLocation:t.courtLocation,originalCaseDate:t.caseDate,originalClientName:t.clientName,originalOpponentName:t.opponentName,originalCaseDescription:t.caseDescription,originalCaseCategory:t.caseCategory,originalCircleNumber:t.circleNumber};localStorage.setItem("transferData",JSON.stringify(r)),a("/cases")}catch(s){alert("خطأ في تحويل الدرجة: "+s.message)}},kt=()=>{A(!1)},$t=()=>{Z(!0),te(""),ie(""),le("")},Fe=s=>{te(s)},zt=async()=>{var s;try{if(!$){alert("يرجى اختيار الحالة الجديدة");return}if($==="دعوى قضائية"&&!ae){alert("يرجى إدخال تاريخ رفع الدعوى");return}if($==="محضر"&&!oe){alert("يرجى إدخال تاريخ كتابة المحضر");return}let r={...t,caseStatus:$,updatedAt:new Date().toISOString()};$==="محضر"?r={...r,fullCaseNumber:`محضر-${Date.now()}`,caseDate:oe,caseNumber:null,caseYear:null,circleNumber:null,caseDegree:null,caseCategory:null,courtLocation:null}:$==="دعوى قضائية"?r={...r,fullCaseNumber:`دعوى-${Date.now()}`,caseDate:ae}:$==="قيد النظر"&&(r={...r,fullCaseNumber:`قيد النظر-${Date.now()}`,reportNumber:null,reportLocation:null,caseNumber:null,caseYear:null,circleNumber:null,caseDegree:null,caseCategory:null,courtLocation:null,caseDate:null});let f=[...t.history||[]];if(f.length===0)if(t.caseStatus==="محضر"&&t.caseDate){const ne={type:"report_created",timestamp:new Date(t.caseDate).toISOString(),action:`تم تحرير المحضر بتاريخ ${new Date(t.caseDate).toLocaleDateString("ar-EG")}`,date:new Date(t.caseDate).toLocaleDateString("ar-EG"),reportDate:t.caseDate};f.push(ne)}else if(t.caseStatus==="دعوى قضائية"&&t.caseDate){const ne={type:"lawsuit_created",timestamp:new Date(t.caseDate).toISOString(),action:`تم رفع دعوى قضائية بتاريخ ${new Date(t.caseDate).toLocaleDateString("ar-EG")}`,date:new Date(t.caseDate).toLocaleDateString("ar-EG"),lawsuitDate:t.caseDate};f.push(ne)}else{const ne={type:"case_created",timestamp:t.createdAt||new Date().toISOString(),action:`تم إنشاء ${t.caseStatus}`,date:new Date(t.createdAt||new Date).toLocaleDateString("ar-EG")};f.push(ne)}const b=new Date().toLocaleDateString("ar-EG"),z={type:"status_transfer",timestamp:new Date().toISOString(),action:`تم تحويل حالة الملف من "${t.caseStatus}" إلى "${$}"`,date:b,oldStatus:t.caseStatus,newStatus:$},C=[...f,z];r.history=C,console.log("الأحداث الأصلية:",((s=t.history)==null?void 0:s.length)||0),console.log("الأحداث الأولية (مع الإنشاء):",f.length),console.log("الأحداث النهائية (مع التحويل):",C.length),console.log("البيانات المحدثة:",r),await ue(i.uid,t.id,r);const Le=`case_${t.id}`;localStorage.removeItem(Le),l&&l(r),alert("تم تحويل حالة الملف بنجاح"),Z(!1),te(""),ie(""),le("")}catch(r){alert("خطأ في تحويل الحالة: "+r.message)}},It=()=>{Z(!1),te(""),ie(""),le("")},[oa,nt]=c.useState(""),[pe,He]=c.useState(""),[st,xe]=c.useState(""),[Mt,Ae]=c.useState(!1),[la,rt]=c.useState(""),[at,ge]=c.useState(""),[Ft,Oe]=c.useState(!1),[de,it]=c.useState(""),[Te,Re]=c.useState(""),[ve,Ht]=c.useState(""),[De,Ve]=c.useState(null),[ot,he]=c.useState(""),At=s=>{console.log("🎯 CaseInfoGroups: تم استدعاء showJudgmentVerdictModalHandler"),console.log("📋 بيانات التأجيلة:",s),Ve(s),Oe(!0),console.log("✅ CaseInfoGroups: تم تفعيل نافذة منطوق الأحكام")};c.useEffect(()=>(console.log("🔧 تسجيل window.showJudgmentVerdictModal"),window.showJudgmentVerdictModal=At,()=>{console.log("🔧 إلغاء تسجيل window.showJudgmentVerdictModal"),delete window.showJudgmentVerdictModal}),[]);const Ot=()=>{if(!Pe(t)){alert("لا يمكن تنفيذ الإحالة للمحكمة. لا يوجد حكم بالإحالة للمحكمة مسجل في الأرشيف الزمني.");return}K(!0),nt(""),He(""),xe("")},Rt=()=>{if(!Pt(t)){alert("لا يمكن تنفيذ الإحالة للخبراء. لا يوجد حكم بالإحالة للخبراء مسجل في الأرشيف الزمني.");return}Ae(!0),rt(""),ge("")},Dt=async()=>{if(ge(""),!ve.trim()){ge("يرجى كتابة مكان/اسم الخبراء");return}try{const s=new Date().toLocaleDateString("ar-EG"),r={date:s,description:`تم إحالة القضية للخبراء - ${ve} بتاريخ ${s}`,type:"expert_referral",timestamp:new Date().toISOString()},f={...t,referredToExpert:!0,expertReferralStatus:`تم الإحالة للخبراء ${ve}`,timeline:[...t.timeline||[],r],updatedAt:new Date().toISOString()};await ue(i.uid,t.id,f),l&&l(f),Ae(!1),alert("تم إحالة القضية للخبراء وتحديث الأرشيف الزمني بنجاح")}catch(s){ge("خطأ في حفظ البيانات: "+s.message)}},Vt=()=>{Ae(!1),rt(""),ge("")},Pe=s=>!s||!s.timeline?!1:s.timeline.some(r=>r.type==="judgment_verdict"&&r.verdict==="الإحالة لمحكمة أخرى"),Pt=s=>!s||!s.timeline?!1:s.timeline.some(r=>r.type==="judgment_verdict"&&r.verdict==="الإحالة للخبراء"),Jt=async()=>{if(xe(""),!pe){xe("يرجى اختيار المحكمة الجديدة");return}try{const s=new Date().toLocaleDateString("ar-EG"),r={date:s,description:`تم إحالة القضية للمحكمة ${pe} بتاريخ ${s}`,type:"court_transfer",timestamp:new Date().toISOString()},f={...t,courtLocation:pe,timeline:[...t.timeline||[],r],courtReferral:{...t==null?void 0:t.courtReferral,transferDate:new Date().toISOString(),newCourtLocation:pe,previousCourtLocation:t.courtLocation,confirmationCompleted:!0,confirmationDate:new Date().toISOString()},updatedAt:new Date().toISOString()};await ue(i.uid,t.id,f),l&&l(f),K(!1),alert("تم إحالة القضية للمحكمة الجديدة وتحديث الأرشيف الزمني بنجاح")}catch(s){xe("خطأ في حفظ البيانات: "+s.message)}},Gt=()=>{K(!1),nt(""),He(""),xe("")},Je=s=>{it(s),he(""),s!=="أخرى"&&Re("")},Yt=async()=>{if(he(""),!de){he("يرجى اختيار منطوق الحكم");return}if(de==="أخرى"&&!Te.trim()){he("يرجى كتابة منطوق الحكم");return}try{let s="",r="";const f=De.originalDate,b=new Date(f).toLocaleDateString("ar-EG");switch(de){case"الإحالة لمحكمة أخرى":s="الإحالة لمحكمة أخرى",r=`صدر حكم بالإحالة لمحكمة أخرى بتاريخ ${b}`;break;case"الإحالة للخبراء":s="الإحالة للخبراء",r=`صدر حكم بالإحالة للخبراء بتاريخ ${b}`;break;case"أخرى":s=Te.trim(),r=`صدر حكم بـ ${Te.trim()} بتاريخ ${b}`;break}const z={date:b,description:r,type:"judgment_verdict",timestamp:new Date().toISOString(),verdict:s,sessionDate:f};let C={...t,timeline:[...t.timeline||[],z],updatedAt:new Date().toISOString()};await ue(i.uid,t.id,C),l&&l(C),Oe(!1),alert("تم تسجيل منطوق الحكم وتحديث الأرشيف الزمني بنجاح"),await Wt()}catch(s){he("خطأ في حفظ البيانات: "+s.message)}},qt=()=>{Oe(!1),it(""),Re(""),he(""),Ve(null)},Wt=async()=>{if(De)try{const{deferralIndex:s,deferrals:r,setDeferrals:f,caseItem:b,setHistory:z}=De,C=[...r];C.splice(s,1),f(C),await ue(i.uid,b.id,{deferrals:C}),Ve(null)}catch(s){console.error("خطأ في إكمال عملية الحضور:",s)}},lt=()=>(t==null?void 0:t.caseStatus)==="دعوى قضائية"&&!(t!=null&&t.isHidden),Ge=()=>(t==null?void 0:t.caseStatus)&&(t==null?void 0:t.caseStatus)!=="غير محدد"&&(t==null?void 0:t.caseStatus)!=="دعوى قضائية"&&!(t!=null&&t.isHidden),Kt=()=>{switch(t==null?void 0:t.caseStatus){case"قيد النظر":return"هل تريد عمل محضر أم رفع دعوى قضائية؟";case"محضر":return"هل تريد رفع دعوى قضائية؟";default:return"تحويل حالة الملف"}};if(!t)return e.jsx("div",{children:"جاري تحميل بيانات القضية..."});const Ye=(s,r=100)=>s&&typeof s=="string"&&s.length>r,Qt=(s,r=100)=>!s||typeof s!="string"||s.length<=r?s:s.substring(0,r).trim()+"...",qe=s=>{const r=["caseDescription","notes","comments","details"];return s.items.some(f=>(r.includes(f.field)||f.type==="textarea")&&Ye(f.value))},Xt=(s,r,f=!1)=>(["caseDescription","notes","comments","details"].includes(s.field)||s.type==="textarea")&&Ye(r)?f?e.jsx("div",{className:n.longTextContainer,children:e.jsx("span",{className:n.longTextFull,children:r})}):e.jsxs("div",{className:n.longTextContainer,children:[e.jsx("span",{className:n.longTextPreview,children:Qt(r,80)}),e.jsx("div",{className:n.moreTextIndicator,children:"اضغط لعرض المزيد..."})]}):e.jsx("span",{className:n.valueTextHorizontal,children:r}),We=(s,r=!1)=>{const f=m&&m.field===s.field,b=s.value&&s.value!=="غير محدد"&&s.value!=="—"&&s.value!=="";let z=s.value;return s.field==="referredToExpert"&&(z=s.value===!0||s.value==="نعم"?"نعم":"لا"),e.jsxs("div",{className:n.dataFieldHorizontal,children:[e.jsxs("span",{className:n.fieldLabelHorizontal,children:[s.label,":"]}),e.jsx("div",{className:n.fieldValueContainerHorizontal,children:f?e.jsx("div",{className:n.editFieldContainerHorizontal,children:s.isFullCaseNumber&&t.caseStatus==="دعوى قضائية"&&P?e.jsxs("div",{className:n.fullCaseNumberEditContainer,children:[e.jsxs("div",{className:n.caseNumberFieldsContainer,children:[e.jsxs("div",{className:n.caseNumberField,children:[e.jsx("label",{className:n.inlineLabel,children:"رقم القضية:"}),e.jsx("input",{type:"text",value:L,onChange:C=>{M(C.target.value),j("")},className:`${n.editInput} ${S?n.inputError:""}`,placeholder:"أدخل رقم القضية"})]}),e.jsxs("div",{className:n.caseNumberField,children:[e.jsx("label",{className:n.inlineLabel,children:"السنة القضائية:"}),e.jsx("input",{type:"text",value:g,onChange:C=>{H(C.target.value),j("")},className:`${n.editInput} ${S?n.inputError:""}`,placeholder:"أدخل السنة القضائية"})]})]}),S&&e.jsx("span",{className:n.errorText,children:S}),e.jsxs("div",{className:n.editActionsHorizontal,children:[e.jsx("button",{onClick:Ie,className:n.saveEditButton,disabled:F,children:F?"...":"حفظ التعديل"}),e.jsx("button",{onClick:Me,className:n.cancelEditButton,disabled:F,children:"إلغاء"})]})]}):s.isFullCaseNumber&&t.caseStatus==="محضر"&&P?e.jsxs("div",{className:n.fullCaseNumberEditContainer,children:[e.jsxs("div",{className:n.caseNumberFieldsContainer,children:[e.jsxs("div",{className:n.caseNumberField,children:[e.jsx("label",{className:n.inlineLabel,children:"رقم المحضر:"}),e.jsx("input",{type:"text",value:L,onChange:C=>{M(C.target.value),j("")},className:`${n.editInput} ${S?n.inputError:""}`,placeholder:"أدخل رقم المحضر"})]}),e.jsxs("div",{className:n.caseNumberField,children:[e.jsx("label",{className:n.inlineLabel,children:"سنة المحضر:"}),e.jsx("input",{type:"text",value:g,onChange:C=>{H(C.target.value),j("")},className:`${n.editInput} ${S?n.inputError:""}`,placeholder:"أدخل سنة المحضر"})]})]}),S&&e.jsx("span",{className:n.errorText,children:S}),e.jsxs("div",{className:n.editActionsHorizontal,children:[e.jsx("button",{onClick:Ie,className:n.saveEditButton,disabled:F,children:F?"...":"حفظ التعديل"}),e.jsx("button",{onClick:Me,className:n.cancelEditButton,disabled:F,children:"إلغاء"})]})]}):e.jsxs(e.Fragment,{children:[(()=>{switch(s.type){case"select":return e.jsxs(e.Fragment,{children:[e.jsxs("select",{value:h,onChange:C=>{v(C.target.value),j("")},className:`${n.editInput} ${S?n.inputError:""}`,children:[e.jsxs("option",{value:"",children:["اختر ",s.label]}),(s.options||[]).map(C=>e.jsx("option",{value:C,children:C},C))]}),h==="أخرى"&&e.jsx("input",{type:"text",value:T,onChange:C=>{V(C.target.value),j("")},className:`${n.editInput} ${S?n.inputError:""}`,placeholder:"أدخل قيمة مخصصة"})]});case"textarea":return e.jsx("textarea",{value:h,onChange:C=>{v(C.target.value),j("")},className:`${n.editInput} ${n.textareaInput} ${S?n.inputError:""}`,placeholder:`أدخل ${s.label}`});case"date":return e.jsx("input",{type:"date",value:h,onChange:C=>{v(C.target.value),j("")},className:`${n.editInput} ${S?n.inputError:""}`});default:return e.jsx("input",{type:"text",value:h,onChange:C=>{v(C.target.value),j("")},className:`${n.editInput} ${S?n.inputError:""}`,placeholder:`أدخل ${s.label}`,disabled:s.readOnly})}})(),S&&e.jsx("span",{className:n.errorText,children:S}),e.jsxs("div",{className:n.editActionsHorizontal,children:[e.jsx("button",{onClick:Ie,className:n.saveEditButton,disabled:F,children:F?"...":"حفظ التعديل"}),e.jsx("button",{onClick:Me,className:n.cancelEditButton,disabled:F,children:"إلغاء"})]})]})}):e.jsxs("div",{className:n.valueWithActionHorizontal,children:[s.field==="referredToExpert"?e.jsxs("span",{className:`${n.booleanValue} ${z==="نعم"?n.trueValue:n.falseValue}`,children:[z==="نعم"?e.jsx(bt,{}):e.jsx(Ne,{}),e.jsx("span",{children:z})]}):Xt(s,z,r),!s.readOnly&&e.jsx(e.Fragment,{children:s.isCourtReferral?e.jsx("button",{onClick:C=>{C.preventDefault(),C.stopPropagation(),Ot()},className:`${n.referralButtonHorizontal} ${Pe(t)?n.enabledReferralButton:n.disabledReferralButton}`,title:Pe(t)?"إحالة لمحكمة أخرى":"لا يوجد حكم بالإحالة للمحكمة",type:"button",children:"إحالة"}):e.jsx("button",{onClick:()=>Et(s,z),className:n.editIconButtonHorizontal,title:s.isExpertReferral?"إحالة للخبراء":s.field==="caseDegree"?"تحويل الدرجة":b?"تعديل":"إضافة",children:s.isExpertReferral?"إحالة":s.field==="caseDegree"?"تحويل":b?"تعديل":"إضافة"})})]})})]},s.field)};return e.jsxs(e.Fragment,{children:[e.jsxs("div",{className:n.coloredCardsContainerHorizontal,children:[tt.map(s=>e.jsxs("div",{className:n.simpleCard,children:[e.jsx("div",{className:n.simpleCardHeader,children:s.title}),e.jsxs("div",{className:n.cardPrimaryContent,children:[s.items.filter(r=>r.primary).map(r=>We(r,!1)),s.type==="location"&&t.caseStatus==="دعوى قضائية"&&!t.isHidden&&e.jsx(e.Fragment,{})]}),(s.items.filter(r=>!r.primary).length>0||s.type==="identification"&&lt()||s.type==="parties"&&Ge()||qe(s))&&e.jsxs("div",{className:`${n.cardExpandedContent} ${k[s.type]?n.expanded:""}`,children:[e.jsx("div",{className:n.expandedContentDivider}),s.items.filter(r=>!r.primary).map(r=>We(r,!0)),qe(s)&&s.items.filter(r=>r.primary).map(r=>(["caseDescription","notes","comments","details"].includes(r.field)||r.type==="textarea")&&Ye(r.value)?We(r,!0):null),s.type==="parties"&&Ge()&&e.jsx("div",{className:n.transferDegreeSection,children:e.jsxs("div",{className:`${n.transferDegreeBox} ${n.partiesTheme}`,children:[e.jsx("div",{className:n.transferHeader,children:e.jsxs("div",{className:n.transferTitleSection,children:[e.jsx(ye,{className:n.transferIcon}),e.jsx("span",{className:n.transferTitle,children:"تحويل حالة الملف"})]})}),e.jsxs("div",{className:n.transferContent,children:[e.jsx("div",{className:n.transferInfoRow,children:e.jsx("span",{className:n.transferValue,children:Kt()})}),e.jsx("button",{className:n.transferButton,onClick:$t,title:"تحويل حالة الملف",children:"تحويل"})]})]})}),s.type==="parties"&&t.caseStatus==="دعوى قضائية"&&(t.reportNumber||t.reportLocation)&&e.jsx("div",{className:n.transferDegreeSection,children:e.jsxs("div",{className:`${n.transferDegreeBox} ${n.partiesTheme}`,children:[e.jsx("div",{className:n.transferHeader,children:e.jsxs("div",{className:n.transferTitleSection,children:[e.jsx(Xe,{className:n.transferIcon}),e.jsx("span",{className:n.transferTitle,children:"بيانات المحضر السابق"})]})}),e.jsxs("div",{className:n.transferContent,children:[t.reportNumber&&e.jsxs("div",{className:n.transferInfoRow,children:[e.jsx("span",{className:n.transferLabel,children:"رقم المحضر:"}),e.jsxs("span",{className:n.transferValue,children:[t.reportNumber,t.caseYear?`/${t.caseYear}`:""]})]}),t.reportLocation&&e.jsxs("div",{className:n.transferInfoRow,children:[e.jsx("span",{className:n.transferLabel,children:"الجهة المختصة:"}),e.jsx("span",{className:n.transferValue,children:t.reportLocation})]})]})]})}),s.type==="identification"&&t.originalCaseId&&(()=>{const r=Q=>Q&&{ابتدائي:"بيانات ملف أول درجة",ابتدائية:"بيانات ملف أول درجة",جنايات:"بيانات ملف أول درجة",جنح:"بيانات ملف أول درجة",مخالفات:"بيانات ملف أول درجة","أحوال شخصية":"بيانات ملف أول درجة",تجاري:"بيانات ملف أول درجة",تجارية:"بيانات ملف أول درجة",عمالي:"بيانات ملف أول درجة",عمالية:"بيانات ملف أول درجة",إداري:"بيانات ملف أول درجة",إدارية:"بيانات ملف أول درجة",مدني:"بيانات ملف أول درجة",مدنية:"بيانات ملف أول درجة",استئناف:"بيانات ملف ثاني درجة",استئنافي:"بيانات ملف ثاني درجة",استئنافية:"بيانات ملف ثاني درجة","عليا إدارية":"بيانات ملف ثاني درجة","محكمة الاستئناف":"بيانات ملف ثاني درجة",نقض:"بيانات ملف ثالث درجة","محكمة النقض":"بيانات ملف ثالث درجة","دستورية عليا":"بيانات ملف ثالث درجة","المحكمة الدستورية العليا":"بيانات ملف ثالث درجة"}[Q]||"بيانات الملف القديم",f=async()=>{const Q=[];try{const{getAllCases:Y}=await Be(async()=>{const{getAllCases:N}=await import("./PermissionsService-Dhc9PbZp.js").then(X=>X.S);return{getAllCases:N}},__vite__mapDeps([0,1,2]),import.meta.url),q=await Y(i.uid),be=[];let Ke=t.originalCaseId;for(;Ke;){const N=q.find(X=>X.id===Ke);if(N)be.unshift(N),Ke=N.originalCaseId;else break}console.log("القضايا المرتبطة الموجودة:",be.map(N=>({id:N.id,degree:N.caseDegree,number:N.fullCaseNumber,clientName:N.clientName,opponentName:N.opponentName}))),be.forEach(N=>{const X=[];N.fullCaseNumber&&N.fullCaseNumber!==t.fullCaseNumber&&X.push({label:"رقم القضية",value:N.fullCaseNumber}),N.courtLocation&&N.courtLocation!==t.courtLocation&&X.push({label:"المحكمة",value:N.courtLocation}),N.clientName&&N.clientName!==t.clientName&&X.push({label:"اسم الموكل",value:N.clientName}),N.opponentName&&N.opponentName!==t.opponentName&&X.push({label:"اسم الخصم",value:N.opponentName}),N.caseDescription&&N.caseDescription!==t.caseDescription&&X.push({label:"وصف القضية",value:N.caseDescription}),N.caseCategory&&N.caseCategory!==t.caseCategory&&X.push({label:"نوع الدعوى",value:N.caseCategory}),N.circleNumber&&N.circleNumber!==t.circleNumber&&X.push({label:"رقم الدائرة",value:N.circleNumber}),X.length>0&&Q.push({degree:N.caseDegree,title:r(N.caseDegree),changedFields:X,caseId:N.id})})}catch(Y){console.error("خطأ في جلب القضايا المرتبطة:",Y);const q=[];t.originalCaseNumber&&t.originalCaseNumber!==t.fullCaseNumber&&q.push({label:"رقم القضية",value:t.originalCaseNumber}),t.originalCourtLocation&&t.originalCourtLocation!==t.courtLocation&&q.push({label:"المحكمة",value:t.originalCourtLocation}),t.originalClientName&&t.originalClientName!==t.clientName&&q.push({label:"اسم الموكل",value:t.originalClientName}),t.originalOpponentName&&t.originalOpponentName!==t.opponentName&&q.push({label:"اسم الخصم",value:t.originalOpponentName}),t.originalCaseDescription&&t.originalCaseDescription!==t.caseDescription&&q.push({label:"وصف القضية",value:t.originalCaseDescription}),t.originalCaseCategory&&t.originalCaseCategory!==t.caseCategory&&q.push({label:"نوع الدعوى",value:t.originalCaseCategory}),t.originalCircleNumber&&t.originalCircleNumber!==t.circleNumber&&q.push({label:"رقم الدائرة",value:t.originalCircleNumber}),q.length>0&&Q.push({degree:t.originalCaseDegree,title:r(t.originalCaseDegree),changedFields:q})}return Q},[b,z]=U.useState([]),[C,Le]=U.useState(!0),[ne,ct]=U.useState(null);if(U.useEffect(()=>{(async()=>{Le(!0);try{const Y=await f();z(Y),console.log("جميع بيانات الدرجات السابقة:",Y)}catch(Y){console.error("خطأ في تحميل بيانات الدرجات السابقة:",Y),z([])}finally{Le(!1)}})()},[t.id,t.originalCaseId]),C)return e.jsx("div",{className:n.transferDegreeSection,children:e.jsx("div",{className:`${n.transferDegreeBox} ${n.identificationTheme}`,children:e.jsx("div",{className:n.transferContent,children:e.jsx("div",{className:n.transferInfoRow,children:e.jsx("span",{className:n.transferValue,children:"جاري تحميل بيانات الملفات السابقة..."})})})})});if(b.length===0)return null;const Zt=Q=>{ct(ne===Q?null:Q)};return e.jsx(e.Fragment,{children:b.map((Q,Y)=>e.jsx("div",{className:n.transferDegreeSection,children:e.jsxs("div",{className:`${n.transferDegreeBox} ${n.identificationTheme}`,children:[e.jsxs("div",{className:n.transferHeader,onClick:()=>Zt(Y),style:{cursor:"pointer"},children:[e.jsxs("div",{className:n.transferTitleSection,children:[e.jsx(Xe,{className:n.transferIcon}),e.jsx("span",{className:n.transferTitle,children:Q.title})]}),e.jsx("div",{className:n.expandButton,children:ne===Y?e.jsx(ft,{}):e.jsx(ht,{})})]}),ne===Y&&e.jsx("div",{className:n.transferContent,children:Q.changedFields.map((q,be)=>e.jsxs("div",{className:n.transferInfoRow,children:[e.jsxs("span",{className:n.transferLabel,children:[q.label,":"]}),e.jsx("span",{className:n.transferValue,children:q.value})]},be))})]})},Y))})})()]}),(s.items.filter(r=>!r.primary).length>0||s.type==="identification"&&t.originalCaseId||s.type==="parties"&&Ge()||qe(s))&&e.jsxs("div",{className:n.expandToggle,onClick:()=>Bt(s.type),children:[k[s.type]?"عرض أقل":"عرض المزيد",k[s.type]?e.jsx(ft,{}):e.jsx(ht,{})]})]},s.type)),e.jsx("div",{className:n.simpleCard,children:e.jsx(sr,{caseData:t,cardClassName:n.simpleCard})})]}),G&&e.jsx("div",{className:n.transferConfirmationOverlay,children:e.jsxs("div",{className:n.transferConfirmationDialog,children:[e.jsx("div",{className:n.transferConfirmationIcon,children:e.jsx(ye,{})}),e.jsx("h3",{className:n.transferConfirmationTitle,children:"تأكيد تحويل الدرجة القضائية"}),e.jsxs("p",{className:n.transferConfirmationMessage,children:["هل أنت متأكد من تحويل هذه القضية إلى درجة قضائية جديدة؟",e.jsx("br",{}),"سيتم إخفاء القضية الحالية من القائمة الرئيسية والانتقال لإضافة دعوى جديدة مع الاحتفاظ بالبيانات الأساسية."]}),e.jsxs("div",{className:n.transferConfirmationActions,children:[e.jsxs("button",{onClick:Lt,className:n.transferConfirmButton,children:[e.jsx(ye,{}),"تأكيد التحويل"]}),e.jsxs("button",{onClick:kt,className:n.transferCancelButton,children:[e.jsx(Ne,{}),"إلغاء"]})]})]})}),ee&&e.jsx("div",{className:n.transferConfirmationOverlay,children:e.jsxs("div",{className:n.transferConfirmationDialog,children:[e.jsx("div",{className:n.transferConfirmationIcon,children:e.jsx(ye,{})}),e.jsx("h3",{className:n.transferConfirmationTitle,children:"تحويل حالة الملف"}),e.jsxs("p",{className:n.transferConfirmationMessage,children:['الحالة الحالية: "',t.caseStatus,'"']}),e.jsxs("div",{className:n.statusOptionsContainer,children:[t.caseStatus==="قيد النظر"&&e.jsxs(e.Fragment,{children:[e.jsx("p",{className:n.questionText,children:"اختر الحالة الجديدة:"}),e.jsxs("div",{className:n.statusOptions,children:[e.jsx("button",{className:`${n.statusOptionButton} ${$==="محضر"?n.selected:""}`,onClick:()=>Fe("محضر"),children:"محضر"}),e.jsx("button",{className:`${n.statusOptionButton} ${$==="دعوى قضائية"?n.selected:""}`,onClick:()=>Fe("دعوى قضائية"),children:"دعوى قضائية"})]})]}),t.caseStatus==="محضر"&&e.jsxs(e.Fragment,{children:[e.jsx("p",{className:n.questionText,children:"هل تريد رفع دعوى قضائية؟"}),e.jsx("div",{className:n.statusOptions,children:e.jsx("button",{className:`${n.statusOptionButton} ${$==="دعوى قضائية"?n.selected:""}`,onClick:()=>Fe("دعوى قضائية"),children:"نعم، رفع دعوى قضائية"})})]}),$==="دعوى قضائية"&&e.jsxs("div",{className:n.dateInputContainer,children:[e.jsxs("label",{className:n.dateLabel,children:["تاريخ رفع الدعوى ",e.jsx("span",{style:{color:"red"},children:"*"})]}),e.jsx("input",{type:"date",value:ae,onChange:s=>ie(s.target.value),className:n.dateInput})]}),$==="محضر"&&e.jsxs("div",{className:n.dateInputContainer,children:[e.jsxs("label",{className:n.dateLabel,children:["تاريخ كتابة المحضر ",e.jsx("span",{style:{color:"red"},children:"*"})]}),e.jsx("input",{type:"date",value:oe,onChange:s=>le(s.target.value),className:n.dateInput})]})]}),e.jsxs("div",{className:n.transferConfirmationActions,children:[e.jsxs("button",{onClick:zt,className:n.transferConfirmButton,disabled:!$||$==="دعوى قضائية"&&!ae||$==="محضر"&&!oe,children:[e.jsx(ye,{}),"تأكيد التحويل"]}),e.jsxs("button",{onClick:It,className:n.transferCancelButton,children:[e.jsx(Ne,{}),"إلغاء"]})]})]})}),O&&e.jsx("div",{className:n.transferConfirmationOverlay,children:e.jsxs("div",{className:n.transferConfirmationDialog,children:[e.jsx("div",{className:n.transferConfirmationIcon,children:e.jsx(Se,{})}),e.jsx("h3",{className:n.transferConfirmationTitle,children:"إحالة لمحكمة أخرى"}),e.jsx("p",{className:n.transferConfirmationMessage,children:"هل تم الحكم بالإحالة لمحكمة أخرى؟"}),e.jsxs("div",{className:n.statusOptionsContainer,children:[e.jsxs("div",{className:n.dateInputContainer,children:[e.jsxs("label",{className:n.dateLabel,children:["المحكمة الجديدة ",e.jsx("span",{style:{color:"red"},children:"*"})]}),e.jsxs("select",{value:pe,onChange:s=>{He(s.target.value),xe("")},className:n.dateInput,children:[e.jsx("option",{value:"",children:"اختر المحكمة"}),Cn.map(s=>e.jsx("option",{value:s.name,children:s.name},s.name))]})]}),st&&e.jsx("div",{className:n.errorMessage,children:st})]}),e.jsxs("div",{className:n.transferConfirmationActions,children:[e.jsxs("button",{onClick:Jt,className:n.transferConfirmButton,disabled:!pe,children:[e.jsx(Se,{}),"تأكيد الإحالة"]}),e.jsxs("button",{onClick:Gt,className:n.transferCancelButton,children:[e.jsx(Ne,{}),"إلغاء"]})]})]})}),Mt&&e.jsx("div",{className:n.transferConfirmationOverlay,children:e.jsxs("div",{className:n.transferConfirmationDialog,children:[e.jsx("div",{className:n.transferConfirmationIcon,children:e.jsx(Ze,{})}),e.jsx("h3",{className:n.transferConfirmationTitle,children:"إحالة للخبراء"}),e.jsx("p",{className:n.transferConfirmationMessage,children:"هل تم الحكم بالإحالة للخبراء؟"}),e.jsxs("div",{className:n.statusOptionsContainer,children:[e.jsxs("div",{className:n.dateInputContainer,children:[e.jsxs("label",{className:n.dateLabel,children:["مكان/اسم الخبراء ",e.jsx("span",{style:{color:"red"},children:"*"})]}),e.jsx("input",{type:"text",value:ve,onChange:s=>{Ht(s.target.value),ge("")},className:n.dateInput,placeholder:"أدخل مكان أو اسم الخبراء"})]}),at&&e.jsx("div",{className:n.errorMessage,children:at})]}),e.jsxs("div",{className:n.transferConfirmationActions,children:[e.jsxs("button",{onClick:Dt,className:n.transferConfirmButton,disabled:!ve.trim(),children:[e.jsx(Ze,{}),"تأكيد الإحالة للخبراء"]}),e.jsxs("button",{onClick:Vt,className:n.transferCancelButton,children:[e.jsx(Ne,{}),"إلغاء"]})]})]})}),Ft&&e.jsx("div",{className:n.transferConfirmationOverlay,children:e.jsxs("div",{className:n.transferConfirmationDialog,children:[e.jsx("div",{className:n.transferConfirmationIcon,children:e.jsx(Se,{})}),e.jsx("h3",{className:n.transferConfirmationTitle,children:"تسجيل منطوق الحكم"}),e.jsx("p",{className:n.transferConfirmationMessage,children:"ما هو منطوق الحكم الصادر في هذه الجلسة؟"}),e.jsxs("div",{className:n.statusOptionsContainer,children:[e.jsxs("div",{className:n.verdictButtonsContainer,children:[e.jsxs("button",{className:`${n.verdictButton} ${de==="الإحالة لمحكمة أخرى"?n.selectedButton:""}`,onClick:()=>Je("الإحالة لمحكمة أخرى"),type:"button",children:[e.jsx(Se,{className:n.verdictButtonIcon}),"الإحالة لمحكمة أخرى"]}),e.jsxs("button",{className:`${n.verdictButton} ${de==="الإحالة للخبراء"?n.selectedButton:""}`,onClick:()=>Je("الإحالة للخبراء"),type:"button",children:[e.jsx(Ze,{className:n.verdictButtonIcon}),"الإحالة للخبراء"]}),e.jsxs("button",{className:`${n.verdictButton} ${de==="أخرى"?n.selectedButton:""}`,onClick:()=>Je("أخرى"),type:"button",children:[e.jsx(Xe,{className:n.verdictButtonIcon}),"أخرى"]})]}),de==="أخرى"&&e.jsxs("div",{className:n.dateInputContainer,children:[e.jsxs("label",{className:n.dateLabel,children:["منطوق الحكم ",e.jsx("span",{style:{color:"red"},children:"*"})]}),e.jsx("textarea",{value:Te,onChange:s=>{Re(s.target.value),he("")},className:`${n.dateInput} ${n.textareaInput}`,placeholder:"أدخل منطوق الحكم",rows:"3"})]}),ot&&e.jsx("div",{className:n.errorMessage,children:ot})]}),e.jsxs("div",{className:n.transferConfirmationActions,children:[e.jsxs("button",{onClick:Yt,className:n.transferConfirmButton,disabled:!de,children:[e.jsx(Se,{}),"تأكيد منطوق الحكم"]}),e.jsxs("button",{onClick:qt,className:n.transferCancelButton,children:[e.jsx(Ne,{}),"إلغاء"]})]})]})})]})}),ar="_historyContainer_eubkd_4",ir="_historyContent_eubkd_12",or="_itemsList_eubkd_19",lr="_simpleHistoryHeader_eubkd_25",cr="_historyItem_eubkd_37",dr="_itemContent_eubkd_60",ur="_itemDateAndBadge_eubkd_68",mr="_itemDate_eubkd_68",fr="_itemBadge_eubkd_90",hr="_typeBadge_eubkd_94",pr="_deferralBadge_eubkd_107",xr="_actionBadge_eubkd_113",gr="_itemMain_eubkd_119",Cr="_itemText_eubkd_126",_r="_dateIcon_eubkd_134",jr="_itemActions_eubkd_141",Nr="_completeButton_eubkd_147",vr="_deleteButton_eubkd_148",br="_linkIndicator_eubkd_192",yr="_linkIcon_eubkd_204",Sr="_noReports_eubkd_209",wr="_actionButton_eubkd_235",Br="_buttonIcon_eubkd_254",Er="_pageCardContainer_eubkd_273",Tr="_actionItem_eubkd_286",Lr="_deleteModalOverlay_eubkd_420",kr="_deleteModal_eubkd_420",$r="_deleteModalHeader_eubkd_449",zr="_deleteModalContent_eubkd_462",Ir="_itemPreview_eubkd_473",Mr="_previewHeader_eubkd_485",Fr="_previewBadge_eubkd_493",Hr="_deferralPreviewBadge_eubkd_501",Ar="_actionPreviewBadge_eubkd_506",Or="_previewDate_eubkd_511",Rr="_previewContent_eubkd_517",Dr="_deleteModalActions_eubkd_527",Vr="_cancelButton_eubkd_533",Pr="_confirmDeleteButton_eubkd_534",Jr="_noPermissionsItem_eubkd_631",Gr="_noPermissionsMessage_eubkd_635",u={historyContainer:ar,historyContent:ir,itemsList:or,simpleHistoryHeader:lr,historyItem:cr,itemContent:dr,itemDateAndBadge:ur,itemDate:mr,itemBadge:fr,typeBadge:hr,deferralBadge:pr,actionBadge:xr,itemMain:gr,itemText:Cr,dateIcon:_r,itemActions:jr,completeButton:Nr,deleteButton:vr,linkIndicator:br,linkIcon:yr,noReports:Sr,actionButton:wr,buttonIcon:Br,pageCardContainer:Er,actionItem:Tr,deleteModalOverlay:Lr,deleteModal:kr,deleteModalHeader:$r,deleteModalContent:zr,itemPreview:Ir,previewHeader:Mr,previewBadge:Fr,deferralPreviewBadge:Hr,actionPreviewBadge:Ar,previewDate:Or,previewContent:Rr,deleteModalActions:Dr,cancelButton:Vr,confirmDeleteButton:Pr,noPermissionsItem:Jr,noPermissionsMessage:Gr},Yr=U.memo(({currentUser:t,actions:i,deferrals:l,history:o,caseItem:a})=>{var R;const[B,m]=c.useState(null),[w,h]=c.useState(l||[]),[v,T]=c.useState(i||[]),[V,S]=c.useState(o||[]),[j,F]=c.useState(!1),[x,P]=c.useState(null),[J,L]=c.useState(!1),[M,g]=c.useState(!1),[H,G]=c.useState(!1),A=c.useMemo(()=>!!(a!=null&&a.id&&(t!=null&&t.uid)),[a==null?void 0:a.id,t==null?void 0:t.uid]),O=c.useMemo(()=>l||[],[l]),K=c.useMemo(()=>i||[],[i]),ee=c.useMemo(()=>o||[],[o]);U.useEffect(()=>{h(O)},[O]),U.useEffect(()=>{T(K)},[K]),U.useEffect(()=>{S(ee)},[ee]),U.useEffect(()=>{(()=>{if(t!=null&&t.uid){const _=Ue.getCurrentUserRole(t.uid),p=Ue.hasPermission(_,"deleteData"),y=Ue.hasPermission(_,"addData");g(p),G(y)}})()},[t==null?void 0:t.uid]);const Z=d=>d?new Date(d).toLocaleDateString("ar-EG",{year:"numeric",month:"long",day:"numeric"}):"",$=c.useMemo(()=>{const d=w.map((p,y)=>({type:"deferral",data:p,date:new Date(p.date||new Date),index:y})),_=v.map((p,y)=>({type:"action",data:p,date:new Date(p.deadline||new Date),index:y}));return[...d,..._].sort((p,y)=>y.date-p.date)},[w,v]),te=c.useCallback(async d=>{if(!A){alert("خطأ: بيانات القضية أو المستخدم غير متوفرة");return}try{if(d.type==="deferral"){const _=w.findIndex(E=>E.id===d.data.id||E.date===d.data.date&&JSON.stringify(E.reasons)===JSON.stringify(d.data.reasons));if(_===-1){alert("خطأ: لم يتم العثور على التأجيلة في القائمة");return}await vn(_,w,h,a,S,E=>{setTimeout(()=>{window.showJudgmentVerdictModal&&window.showJudgmentVerdictModal(E)},100)})!=="judgment_detected"&&(console.log("✅ تم إكمال التأجيلة وإضافتها للأرشيف الزمني"),alert("تم تسجيل الحضور بنجاح وإضافة الحدث للأرشيف الزمني"))}else{const _=v.findIndex(y=>y.id===d.data.id||y.description===d.data.description&&y.deadline===d.data.deadline);if(_===-1){alert("خطأ: لم يتم العثور على الإجراء في القائمة");return}const p=d.data.id||`${a.id}-action-${_}`;await bn(p,v,T,a,S),console.log("✅ تم إكمال الإجراء وإضافته للأرشيف الزمني"),alert("تم تسجيل إنجاز المهمة بنجاح وإضافة الحدث للأرشيف الزمني")}}catch(_){console.error("خطأ في إكمال المهمة:",_),alert("حدث خطأ أثناء إكمال المهمة: "+_.message)}},[w,v,a,t,A]),ae=c.useCallback(d=>{if(!A){alert("خطأ: بيانات القضية أو المستخدم غير متوفرة");return}P(d),F(!0)},[A]),ie=c.useCallback(async()=>{if(x){L(!0);try{const _=Ee()==="online"?"أونلاين":"محلي";if(x.type==="deferral"){const p=w.findIndex(y=>y.id===x.data.id||y.date===x.data.date&&JSON.stringify(y.reasons)===JSON.stringify(x.data.reasons));if(p===-1){alert("خطأ: لم يتم العثور على التأجيلة في القائمة");return}await yn(p,w,h,V,S,a,v,T),console.log("✅ تم حذف التأجيلة بنجاح")}else{const p=v.findIndex(E=>E.id===x.data.id||E.description===x.data.description&&E.deadline===x.data.deadline);if(p===-1){alert("خطأ: لم يتم العثور على الإجراء في القائمة");return}const y=x.data.id||`${a.id}-action-${p}`;await Sn(y,v,T,a,S),console.log("✅ تم حذف الإجراء بنجاح")}F(!1),P(null),alert(`تم حذف ${x.type==="deferral"?"التأجيلة":"الإجراء"} بنجاح من الحساب ${_}`)}catch(d){const p=Ee()==="online"?"الأونلاين":"المحلي";console.error("خطأ في حذف المهمة:",d),alert(`حدث خطأ أثناء حذف ${x.type==="deferral"?"التأجيلة":"الإجراء"} من الحساب ${p}. يرجى المحاولة مرة أخرى.`)}finally{L(!1)}}},[x,w,v,V,a]),oe=c.useCallback(()=>{F(!1),P(null)},[]),le=async(d,_,p,y)=>{try{if(!a||!a.id){y("خطأ: بيانات القضية غير متوفرة");return}if(!t||!t.uid){y("خطأ: بيانات المستخدم غير متوفرة");return}if(!d||isNaN(new Date(d).getTime())||_.length===0){y("يرجى إدخال تاريخ التأجيلة واختيار سبب واحد على الأقل");return}const E={id:`${a.id}-defer-${Date.now()}`,date:d,reasons:_,description:p||"",createdAt:new Date().toISOString(),isDeleted:!1,isArchived:!1},ce=[...w,E];h(ce),await ue(t.uid,a.id,{deferrals:ce}),alert("تم إضافة التنبيه بتاريخ الجلسة بنجاح"),$e(t.uid),m(null)}catch(E){console.error("خطأ في حفظ التأجيل:",E),y(E.message)}},k=async(d,_,p,y)=>{try{await yt(d,_,p,a,v,T,S),$e(t.uid),m(null)}catch(E){console.error("خطأ في حفظ الإجراء:",E),y(E.message)}};return e.jsxs("div",{className:u.pageCardContainer,children:[e.jsxs("div",{className:u.historyContainer,children:[e.jsx("div",{className:u.simpleHistoryHeader,children:"سجل المهام"}),e.jsx("div",{className:u.historyContent,children:e.jsxs("div",{className:u.itemsList,children:[$.length>0?$.map((d,_)=>{var p;return e.jsx("div",{className:`${u.historyItem} ${d.type==="action"?u.actionItem:u.deferralItem}`,children:e.jsxs("div",{className:u.itemContent,children:[e.jsxs("div",{className:u.itemDateAndBadge,children:[e.jsxs("div",{className:u.itemDate,children:[e.jsx(pt,{className:u.dateIcon}),Z(d.type==="deferral"?d.data.date:d.data.deadline)]}),e.jsx("div",{className:u.itemBadge,children:e.jsx("span",{className:`${u.typeBadge} ${d.type==="deferral"?u.deferralBadge:u.actionBadge}`,children:d.type==="deferral"?"تأجيل":"إجراء"})})]}),e.jsx("div",{className:u.itemMain,children:e.jsx("div",{className:u.itemText,children:d.type==="deferral"?e.jsxs(e.Fragment,{children:[((p=d.data.reasons)==null?void 0:p.join("، "))||"تأجيل بدون تفاصيل",d.data.description&&` - ${d.data.description}`]}):e.jsxs(e.Fragment,{children:[d.data.description,d.data.linkedDeferralId&&e.jsxs("span",{className:u.linkIndicator,children:[e.jsx(un,{className:u.linkIcon}),"مرتبط بتأجيل"]})]})})}),e.jsxs("div",{className:u.itemActions,children:[e.jsx("button",{onClick:()=>te(d),className:u.completeButton,title:d.type==="deferral"?"تم الحضور":"تم الإنجاز",children:e.jsx(bt,{})}),M&&e.jsx("button",{onClick:()=>ae(d),className:u.deleteButton,title:"حذف",children:e.jsx(mn,{})})]})]})},_)}):e.jsx("div",{className:u.noReports,children:"لا توجد مهام مسجلة"}),!B&&H&&e.jsx("div",{className:`${u.historyItem} ${u.actionItem}`,children:e.jsx("div",{className:u.itemContent,children:e.jsxs("div",{className:u.itemMain,children:[(a==null?void 0:a.caseStatus)!=="قيد النظر"&&e.jsxs("button",{onClick:()=>m("deferral"),className:u.actionButton,children:[e.jsx(pt,{className:u.buttonIcon}),"إضافة تنبيه بتاريخ جلسة"]}),e.jsxs("button",{onClick:()=>m("action"),className:u.actionButton,children:[e.jsx(fn,{className:u.buttonIcon}),"إضافة تنبيه بإجراء"]})]})})}),!B&&!H&&e.jsx("div",{className:`${u.historyItem} ${u.noPermissionsItem}`,children:e.jsx("div",{className:u.itemContent,children:e.jsx("div",{className:u.noPermissionsMessage,children:"🔒 ليس لديك صلاحية إضافة تنبيهات جديدة"})})}),B==="deferral"&&e.jsx(St,{currentUser:t,caseItem:a,onClose:()=>m(null),onSave:le,isUnderConsideration:(a==null?void 0:a.caseStatus)==="قيد النظر"}),B==="action"&&e.jsx(wt,{currentUser:t,caseItem:a,deferrals:w,actions:v,onClose:()=>m(null),onSave:k})]})})]}),j&&e.jsx("div",{className:u.deleteModalOverlay,children:e.jsxs("div",{className:u.deleteModal,children:[e.jsx("div",{className:u.deleteModalHeader,children:e.jsx("h3",{children:"تأكيد الحذف"})}),e.jsxs("div",{className:u.deleteModalContent,children:[e.jsxs("p",{children:["هل أنت متأكد من حذف هذا ",(x==null?void 0:x.type)==="deferral"?"التأجيل":"الإجراء","؟"]}),x&&e.jsxs("div",{className:u.itemPreview,children:[e.jsxs("div",{className:u.previewHeader,children:[e.jsx("span",{className:`${u.previewBadge} ${x.type==="deferral"?u.deferralPreviewBadge:u.actionPreviewBadge}`,children:x.type==="deferral"?"تأجيل":"إجراء"}),e.jsx("span",{className:u.previewDate,children:Z(x.type==="deferral"?x.data.date:x.data.deadline)})]}),e.jsx("div",{className:u.previewContent,children:x.type==="deferral"?e.jsxs(e.Fragment,{children:[e.jsx("strong",{children:"الأسباب: "}),((R=x.data.reasons)==null?void 0:R.join("، "))||"تأجيل بدون تفاصيل",x.data.description&&e.jsxs(e.Fragment,{children:[e.jsx("br",{}),e.jsx("strong",{children:"الوصف: "}),x.data.description]})]}):e.jsxs(e.Fragment,{children:[e.jsx("strong",{children:"الوصف: "}),x.data.description,x.data.linkedDeferralId&&e.jsxs(e.Fragment,{children:[e.jsx("br",{}),e.jsx("strong",{children:"مرتبط بتأجيل"})]})]})})]})]}),e.jsxs("div",{className:u.deleteModalActions,children:[e.jsx("button",{className:u.cancelButton,onClick:oe,disabled:J,children:"إلغاء"}),e.jsx("button",{className:u.confirmDeleteButton,onClick:ie,disabled:J,children:J?"جاري الحذف...":"حذف"})]})]})})]})}),qr="_pageWrapper_6uumw_12",Wr="_mainContainer_6uumw_20",Kr="_titleSection_6uumw_73",Qr="_caseSubtitle_6uumw_113",Xr="_subtitleIcon_6uumw_130",Zr="_caseInfoGrid_6uumw_149",Ur="_buttonIcon_6uumw_365",ea="_buttonsSection_6uumw_369",ta="_backButton_6uumw_376",na="_editButton_6uumw_376",sa="_errorMessage_6uumw_419",ra="_errorIcon_6uumw_436",aa="_twoColumnLayout_6uumw_571",D={pageWrapper:qr,mainContainer:Wr,titleSection:Kr,caseSubtitle:Qr,subtitleIcon:Xr,caseInfoGrid:Zr,buttonIcon:Ur,buttonsSection:ea,backButton:ta,editButton:na,errorMessage:sa,errorIcon:ra,twoColumnLayout:aa};function ia(t,i,l=3e4){const[o,a]=c.useState(null),[B,m]=c.useState(!0),[w,h]=c.useState(null),[v,T]=c.useState([]),[V,S]=c.useState([]),[j,F]=c.useState([]),x=c.useMemo(()=>`case_${t}`,[t]),P=c.useCallback(async(L=!1)=>{if(m(!0),h(null),!t){h("لم يتم تحديد معرف القضية."),m(!1);return}if(!i){h("يجب تسجيل الدخول لعرض تفاصيل القضايا."),m(!1);return}const M=Ee();if(M==="online"&&!navigator.onLine){h("غير متصل بالإنترنت. سيتم تحميل البيانات عند استعادة الاتصال أو يمكنك التبديل إلى الحساب المحلي."),m(!1);return}if(!L){const g=we.getCache(x);if(g){a(g),T(g.deferrals||[]),S(g.actions||[]),F(g.history||[]),m(!1);return}}try{let g=await _n(i.uid,t);if(!g&&M==="online")try{const H=sn(rn,"cases",t),G=await an(H);G.exists()&&(g={id:G.id,...G.data()})}catch(H){console.warn("خطأ في جلب البيانات من Firebase:",H)}g?(g.id||(g.id=t),g.userId===i.uid?(a(g),T(g.deferrals||[]),S(g.actions||[]),F(g.history||[]),we.updateCache(x,g,l)):(h("لا تمتلك صلاحية عرض تفاصيل هذه القضية."),a(null))):(h(M==="online"?"لم يتم العثور على بيانات القضية في الحساب الأونلاين.":"لم يتم العثور على بيانات القضية في الحساب المحلي."),a(null))}catch(g){h("حدث خطأ أثناء جلب بيانات القضية: "+g.message),a(null)}finally{m(!1)}},[t,i,l]),J=c.useCallback(L=>{a(M=>{if(!M||!Object.keys(L).some(G=>{const A=M[G],O=L[G];if(Array.isArray(A)&&Array.isArray(O))return A.length!==O.length?!0:A.some((K,ee)=>K!==O[ee]);if(typeof A=="object"&&typeof O=="object"&&A!==null&&O!==null){const K=Object.keys(A),ee=Object.keys(O);return K.length!==ee.length?!0:K.some(Z=>A[Z]!==O[Z])}return A!==O}))return M;const H={...M,...L};try{we.updateCache(x,H,l)}catch(G){console.warn("فشل في تحديث الكاش:",G)}return H})},[x,l]);return c.useEffect(()=>{P();const L=({caseId:g})=>{g===t&&P(!0)},M=()=>P(!0);return we.addListener("case_refresh",L),window.addEventListener("focus",M),()=>{we.removeListener("case_refresh",L),window.removeEventListener("focus",M)}},[t,i,P]),{caseData:o,setCaseData:J,loading:B,error:w,deferrals:v,setDeferrals:T,actions:V,setActions:S,history:j,setHistory:F,refetch:()=>P(!0)}}const ba=({currentUser:t})=>{const{caseNumber:i}=on(),l=i,o=jt(),{caseData:a,setCaseData:B,loading:m,error:w,deferrals:h,setDeferrals:v,actions:T,setActions:V,history:S,setHistory:j,refetch:F}=ia(l,t),[x,P]=c.useState(null),[J,L]=c.useState(window.innerWidth<=768),[M,g]=c.useState(!1),H=c.useRef(),G=()=>{window.location.href="/reports"},A=()=>{F()},O=()=>{P(null)},K=k=>{window.showJudgmentVerdictModal&&window.showJudgmentVerdictModal(k)},ee=async(k,R,d,_)=>{try{if(!k||isNaN(new Date(k).getTime())||R.length===0){_("يرجى إدخال تاريخ التأجيلة واختيار سبب واحد على الأقل");return}const p={id:`${a.id}-defer-${Date.now()}`,date:k,reasons:R,description:d||"",createdAt:new Date().toISOString(),isDeleted:!1,isArchived:!1},y=[...h,p];v(y),await Be(async()=>{const{updateCase:E}=await import("./PermissionsService-Dhc9PbZp.js").then(ce=>ce.S);return{updateCase:E}},__vite__mapDeps([0,1,2]),import.meta.url).then(({updateCase:E})=>E(t.uid,a.id,{deferrals:y})),alert("تم إضافة التنبيه بتاريخ الجلسة بنجاح"),$e(t.uid),O()}catch(p){_(p.message)}},Z=async(k,R,d,_)=>{try{await yt(k,R,d,a,T,V,j),$e(t.uid),O()}catch(p){_(p.message)}},$=()=>{H.current=setTimeout(()=>{g(!0)},700)},te=()=>{clearTimeout(H.current)},ae=()=>{clearTimeout(H.current)},ie=()=>g(!1),oe=async()=>{try{if(jn(t.uid,a.id),Ee()==="online"){const{db:k}=await Be(async()=>{const{db:_}=await import("./index-HUlFBKIW.js").then(p=>p.af);return{db:_}},__vite__mapDeps([1,2]),import.meta.url),{doc:R,deleteDoc:d}=await Be(async()=>{const{doc:_,deleteDoc:p}=await import("./CaseFilters-CPt9izwP.js").then(y=>y.i);return{doc:_,deleteDoc:p}},__vite__mapDeps([3,1,2]),import.meta.url);await d(R(k,"cases",a.id))}g(!1),o("/reports")}catch(k){alert("حدث خطأ أثناء حذف القضية: "+k.message),g(!1)}};if(c.useEffect(()=>{const k=()=>L(window.innerWidth<=768);return window.addEventListener("resize",k),()=>window.removeEventListener("resize",k)},[]),m)return e.jsx(ln,{message:"جاري تحميل تفاصيل القضية..."});if(w)return e.jsxs("div",{className:D.pageWrapper,children:[e.jsx(xt,{currentUser:t,casesList:[]}),e.jsxs("div",{className:D.mainContainer,children:[e.jsxs("div",{className:D.errorMessage,children:[e.jsx(Nt,{className:D.errorIcon}),w]}),e.jsxs("div",{className:D.buttonsSection,children:[e.jsxs("button",{onClick:G,className:D.backButton,children:[e.jsx(hn,{className:D.buttonIcon}),"العودة"]}),e.jsxs("button",{onClick:A,className:D.editButton,children:[e.jsx(pn,{className:D.buttonIcon}),"إعادة المحاولة"]})]})]})]});a.caseStatus==="قيد النظر"||a.caseStatus==="محضر"||a.caseStatus;const le=(a==null?void 0:a.caseStatus)==="قيد النظر";return e.jsxs("div",{className:D.pageWrapper,id:"case-details-page",children:[e.jsx(xt,{currentUser:t,casesList:[]}),e.jsxs("div",{className:D.mainContainer,children:[e.jsx("div",{className:D.titleSection,children:a.clientName&&e.jsxs("p",{className:D.caseSubtitle,onMouseDown:$,onMouseUp:te,onMouseLeave:ae,onTouchStart:$,onTouchEnd:te,style:{cursor:"pointer",userSelect:"none"},title:"اضغط مطولًا لحذف القضية",children:[e.jsx(vt,{className:D.subtitleIcon})," الموكل: ",a.clientName]})}),M&&e.jsx("div",{style:{position:"fixed",top:0,left:0,width:"100vw",height:"100vh",background:"rgba(0,0,0,0.4)",display:"flex",alignItems:"center",justifyContent:"center",zIndex:9999},children:e.jsxs("div",{style:{background:"#fff",borderRadius:"12px",padding:"2rem",minWidth:"320px",boxShadow:"0 2px 16px rgba(0,0,0,0.18)",textAlign:"center",direction:"rtl"},children:[e.jsx("h3",{style:{marginBottom:"1rem",color:"#b91c1c"},children:"تأكيد حذف القضية"}),e.jsx("p",{style:{marginBottom:"2rem"},children:"هل أنت متأكد أنك تريد حذف هذه القضية نهائيًا؟"}),e.jsxs("div",{style:{display:"flex",gap:"1rem",justifyContent:"center"},children:[e.jsx("button",{onClick:oe,style:{background:"#b91c1c",color:"#fff",border:"none",borderRadius:"6px",padding:"0.5rem 1.5rem",fontWeight:"bold",cursor:"pointer"},children:"نعم، حذف"}),e.jsx("button",{onClick:ie,style:{background:"#e5e7eb",color:"#222",border:"none",borderRadius:"6px",padding:"0.5rem 1.5rem",fontWeight:"bold",cursor:"pointer"},children:"إلغاء"})]})]})}),e.jsxs("div",{className:D.twoColumnLayout,children:[e.jsxs("div",{className:D.rightColumn,children:[e.jsx("div",{className:D.caseInfoGrid,children:e.jsx(rr,{caseData:a,currentUser:t,onCaseDataUpdate:B,onJudgmentDetected:K})}),e.jsxs("div",{style:{display:"flex",gap:J?"1.5rem":"2.5rem",alignItems:"stretch",marginTop:"1.5rem",flexDirection:J?"column":"row"},children:[e.jsx("div",{style:{flex:2,width:"100%"},children:e.jsx(Yr,{currentUser:t,actions:T,deferrals:h,history:S,caseItem:a})}),e.jsx("div",{style:{flex:1,width:"100%"},children:e.jsx(wn,{caseId:l,userId:t.uid,savedNotes:(a==null?void 0:a.notes)||[],onClose:()=>{},onSave:async k=>{try{let R;k.deleted?R=k.notes:R=[...a.notes||[],k],B(_=>({..._,notes:R}));const{updateCase:d}=await Be(async()=>{const{updateCase:_}=await import("./PermissionsService-Dhc9PbZp.js").then(p=>p.S);return{updateCase:_}},__vite__mapDeps([0,1,2]),import.meta.url);await d(t.uid,a.id,{notes:R,updatedAt:new Date().toISOString()})}catch(R){console.error("خطأ في حفظ الملاحظة:",R),alert("حدث خطأ أثناء حفظ الملاحظة. يرجى المحاولة مرة أخرى.")}}})})]})]}),e.jsx("div",{className:D.leftColumn})]}),x==="deferral"&&e.jsx(St,{currentUser:t,onSave:ee,onCancel:O,isUnderConsideration:le}),x==="action"&&e.jsx(wt,{currentUser:t,caseItem:a,deferrals:h,actions:T,setActions:V,history:S,setHistory:j,onSave:Z,onCancel:O})]})]})};export{ba as default};
