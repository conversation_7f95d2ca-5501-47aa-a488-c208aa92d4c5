import React from 'react';
import { Navigate, useLocation } from 'react-router-dom';
import { useSubscription } from '../hooks/useSubscription';
import LoadingSpinner from './ui/LoadingSpinner';

const ProtectedRoute = ({ children, user, loading }) => {
  const subscription = useSubscription();
  const location = useLocation();

  if (loading || subscription.status === 'loading') {
    return <LoadingSpinner message="جاري التحقق من حالة المصادقة والاشتراك..." />;
  }

  if (!user) {
    return <Navigate to="/login" replace />;
  }

  // If subscription is expired, redirect to profile page, UNLESS they are already on the profile page.
  if ((subscription.status === 'expired' || subscription.status === 'local_locked') && location.pathname !== '/profile') {
    return <Navigate to="/profile" replace />;
  }

  return children;
};

export default ProtectedRoute;
