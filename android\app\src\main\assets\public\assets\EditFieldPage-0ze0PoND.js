import{i as k,c as R,r as d,j as e,d as h,b as j,g as w,u as T}from"./index-HUlFBKIW.js";import{F as v,l as z,m as A,b as E,k as B,h as O,n as f,i as H}from"./index-DhfUDJs-.js";import{T as y}from"./TopBar-rd4Lqjij.js";import{s}from"./ProfilePage.module-DwB6uXPZ.js";import{g as J}from"./PermissionsService-Dhc9PbZp.js";import"./iconBase-BPj5F03O.js";const q=({currentUser:a})=>{const{fieldName:t}=k(),m=R(),[c,p]=d.useState(""),[u,o]=d.useState(!0),[g,l]=d.useState(null),[N,S]=d.useState(null),x=J();d.useEffect(()=>{(async()=>{if(!a||!a.uid){l("المستخدم غير مسجل الدخول. يرجى تسجيل الدخول مرة أخرى."),o(!1);return}o(!0),l(null);try{if(x==="online"){const i=h(j,"users",a.uid),r=await w(i);if(r.exists()){const C=r.data();p(C[t]||"")}}else{const i=localStorage.getItem("localUserData_"+a.uid);if(i){const r=JSON.parse(i);S(r),p(r[t]||"")}}}catch(i){console.error("خطأ في جلب بيانات المستخدم:",i.message),l("حدث خطأ في جلب البيانات. يرجى المحاولة مرة أخرى.")}o(!1)})()},[a,t,x]);const b=n=>{p(n.target.value)},F=async()=>{if(!(a!=null&&a.uid)){l("المستخدم غير مسجل الدخول.");return}if(t==="name"&&!c.trim()){l("الاسم مطلوب.");return}try{o(!0),l(null);const n={[t]:c};if(x==="online")if(navigator.onLine){const i=h(j,"users",a.uid);await T(i,{...n,lastUpdateTime:new Date().toISOString()}),alert("تم حفظ البيانات بنجاح في الحساب الأونلاين.")}else{l("لا يمكن حفظ البيانات في الحساب الأونلاين بدون اتصال بالإنترنت."),o(!1);return}else{const i={...N||{},uid:a.uid,...n,lastUpdatedAt:new Date().toISOString()};localStorage.setItem("localUserData_"+a.uid,JSON.stringify(i)),alert("تم حفظ البيانات بنجاح في الحساب المحلي.")}m("/profile")}catch(n){l("خطأ في تحديث البيانات: "+n.message)}finally{o(!1)}},D=()=>{m("/profile")},L=()=>{switch(t){case"name":return e.jsxs("div",{className:"field-description",children:[e.jsx(f,{style:{color:"#1a73e8",marginLeft:"8px"}}),e.jsx("span",{children:"إنّ التغييرات التي يتم إجراؤها على لقبكَ ستظهر في حسابكَ على تطبيق الاجندة القضائية."})]});case"phone":return e.jsxs("div",{className:"field-description",children:[e.jsx(O,{style:{color:"#1a73e8",marginLeft:"8px"}}),e.jsx("span",{children:"يستخدم رقم الهاتف في استرجاع حسابك وتأمين بياناتك."})]});case"company":return e.jsxs("div",{className:"field-description",children:[e.jsx(B,{style:{color:"#1a73e8",marginLeft:"8px"}}),e.jsx("span",{children:"معلومات الشركة تساعدنا في تقديم خدمة أفضل لك."})]});case"email":return e.jsxs("div",{className:"field-description",children:[e.jsx(E,{style:{color:"#1a73e8",marginLeft:"8px"}}),e.jsx("span",{children:"البريد الإلكتروني لحساب تطبيق الاجندة القضائية. العنوان المُستخدم لمساعدتك في التعرّف على حسابك."})]});default:return null}},I=()=>{switch(t){case"name":return e.jsxs("div",{className:"field-footer",children:[e.jsx(v,{style:{color:"#5f6368",marginLeft:"8px"}}),e.jsx("span",{children:"المستخدمون الذين يمكنهم الاطّلاع على اسمك: يمكن لأي مستخدم الاطّلاع على هذه المعلومات عندما يتواصل معك أو يعرض المحتوى الذي تنشئه في خدمات تطبيق الاجندة القضائية."})]});case"phone":return e.jsxs("div",{className:"field-footer",children:[e.jsx(H,{style:{color:"#5f6368",marginLeft:"8px"}}),e.jsx("span",{children:"لا يمكن لأحد أن يرى رقم هاتفك. يتم استخدامه فقط لأغراض الأمان والتحقق."})]});case"company":return e.jsxs("div",{className:"field-footer",children:[e.jsx(f,{style:{color:"#5f6368",marginLeft:"8px"}}),e.jsx("span",{children:"معلومات الشركة تظهر في ملفك الشخصي وتساعد في تخصيص تجربتك."})]});case"email":return e.jsxs("div",{className:"field-footer",children:[e.jsx(f,{style:{color:"#5f6368",marginLeft:"8px"}}),e.jsx("span",{children:"لا يمكنك تغيير هذا العنوان. يرجى التواصل مع الدعم الفني إذا كنت بحاجة إلى تغيير بريدك الإلكتروني."})]});default:return null}};return u?e.jsxs("div",{className:s.pageWrapper,children:[e.jsx(y,{currentUser:a}),e.jsxs("div",{className:s.loadingContainer,children:[e.jsx("div",{className:s.spinner}),e.jsx("p",{children:"جاري التحميل..."})]})]}):e.jsxs("div",{className:s.pageWrapper,children:[e.jsx(y,{currentUser:a}),e.jsxs("div",{className:s.mainContainer,children:[e.jsx("div",{className:s.accountManagementSection,children:e.jsxs("div",{className:s.sidebarNavItem,style:{visibility:"hidden"},children:[e.jsx("div",{className:s.sidebarNavIcon,children:e.jsx(v,{})}),e.jsx("span",{children:"المعلومات الشخصية"})]})}),e.jsxs("div",{className:s.mainContentArea,children:[e.jsx("div",{className:s.pageHeader,children:e.jsx("h2",{className:s.pageTitle,children:"تعديل"})}),g&&e.jsx("div",{className:s.errorMessage,children:g}),e.jsx("div",{className:s.personalInfoSection,children:e.jsxs("div",{className:s.editFieldForm,children:[e.jsx("h3",{className:s.sectionTitle,children:"تعديل البيانات"}),e.jsx("div",{style:{color:"#5f6368",fontSize:"14px",marginBottom:"15px",lineHeight:"1.5",display:"flex",alignItems:"flex-start",backgroundColor:"#f8f9fa",padding:"12px 15px",borderRadius:"8px"},children:L()}),e.jsx("div",{className:s.formGroup,children:e.jsx("input",{type:"text",value:c,onChange:b,className:s.formInput,autoFocus:!0,style:{marginBottom:"10px"},disabled:t==="email"})}),e.jsx("div",{style:{color:"#5f6368",fontSize:"12px",marginBottom:"20px",lineHeight:"1.5",display:"flex",alignItems:"flex-start",backgroundColor:"#f1f3f4",padding:"10px 12px",borderRadius:"8px"},children:I()}),e.jsxs("div",{className:s.buttonRow,children:[e.jsxs("button",{onClick:F,style:{background:"#1a73e8",color:"#fff",border:"none",borderRadius:"20px",padding:"8px 16px",fontSize:"14px",cursor:"pointer",display:"flex",alignItems:"center",gap:"5px"},disabled:u||t==="email",children:[e.jsx(z,{style:{fontSize:"12px"}})," حفظ"]}),e.jsxs("button",{onClick:D,style:{background:"#f1f3f4",color:"#5f6368",border:"1px solid #dadce0",borderRadius:"20px",padding:"8px 16px",fontSize:"14px",cursor:"pointer",display:"flex",alignItems:"center",gap:"5px"},children:[e.jsx(A,{style:{fontSize:"12px"}})," إلغاء"]})]})]})})]})]}),e.jsx("style",{jsx:!0,children:`
        .field-description {
          display: flex;
          align-items: flex-start;
          gap: 8px;
        }
        .field-description span {
          flex: 1;
        }
        .field-footer {
          display: flex;
          align-items: flex-start;
          gap: 8px;
        }
        .field-footer span {
          flex: 1;
        }
      `})]})};export{q as default};
