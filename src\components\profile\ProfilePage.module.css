/* ProfilePage.module.css */
@import '../../styles/variables.css';

.pageWrapper {
  display: flex;
  flex-direction: column;
  align-items: center;
  padding: 20px;
  min-height: calc(100vh - var(--topbar-height));
  background-color: #f1f5f9; /* Your desired light background color */
  background : var(--neutral-100);
  direction: rtl;
  text-align: right;
}

.content {
  width: 100%;
  max-width: 900px;
  background: none;
  border-radius: 20px;
  box-shadow: var(--shadow-medium);
  padding: 30px;
  margin-top: 30px;
  display: flex;
  flex-direction: column;
  gap: 25px;
}

.profileHeader {
  display: flex;
  flex-direction: column;
  align-items: center;
  gap: 5px;
  margin-bottom: 20px;
  padding-bottom: 20px;
  border-bottom: 1px solid var(--neutral-200);
}

.userName {
  font-size: 2.2rem;
  font-weight: 700;
  background: linear-gradient(135deg, var(--primary-dark-Violet) 0%, var(--primary-medium-Violet) 100%);
  -webkit-background-clip: text;
  -webkit-text-fill-color: transparent;
  background-clip: text;
  margin: 0;
}

.welcomeMessage {
  font-size: 1.1rem;
  color: var(--neutral-600);
  margin: 0;
}

.sectionCard {
  border-radius: 15px;
  box-shadow: 
    0 20px 60px var(--shadow-medium),
    0 10px 30px var(--shadow-light);

  padding: 25px;
  display: flex;
  flex-direction: column;
  gap: 20px;
  
}

.sectionHeader {
  display: flex;
  align-items: center;
  gap: 10px;
  margin-bottom: 15px;
  padding-bottom: 10px;
  border-bottom: 1px solid var(--neutral-200);
}

.sectionTitle {
  font-size: 1.4rem;
  font-weight: 600;
  background: linear-gradient(135deg, var(--primary-dark-blue) 0%, var( --primary-medium-Violet) 100%);
  -webkit-background-clip: text;
  -webkit-text-fill-color: transparent;
  background-clip: text;
  margin: 0;
}

.infoItem {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 12px 0;
  border-bottom: 1px dashed var(--neutral-100);
}

.infoItem:last-child {
  border-bottom: none;
}

.infoLabel {
  font-weight: 500;
  color: var(--neutral-700);
  display: flex;
  align-items: center;
  gap: 8px;
  font-size: 1rem;
}

.infoValue {
  color: var(--neutral-800);
  font-weight: 600;
  font-size: 1rem;
  display: flex;
  align-items: center;
  gap: 8px;
}

.editButton {
  background: var(--primary-light);
  border: 1px solid var(--primary-color);
  color: var(--primary-color);
  padding: 5px 10px;
  border-radius: 8px;
  cursor: pointer;
  font-size: 0.9rem;
  margin-right: 10px;
  transition: all 0.2s ease;
  display: inline-flex;
  align-items: center;
  justify-content: center;
  gap: 5px;
}

.editButton:hover {
  background: var(--primary-color);
  color: white;
}

.accountCardsContainer {
  display: flex;
  flex-wrap: wrap;
  gap: 20px;
  justify-content: center;
  margin-top: 20px;
}

.accountCard {
  background: var(--card-bg);
  border-radius: 15px;
   box-shadow: 
    0 20px 60px var(--shadow-medium),
    0 10px 30px var(--shadow-light);
  border: 1px solid var(--neutral-200);
  padding: 20px;
  display: flex;
  flex-direction: column;
  align-items: center;
  gap: 15px;
  width: 250px;
  text-align: center;
  transition: all 0.2s ease;
}

.accountCard.active {
  border-color: var(--primary-color);
  box-shadow: 0 0 0 3px rgba(59, 130, 246, 0.2);
}

.accountCard:hover {
  transform: translateY(-5px);
  box-shadow: var(--shadow-medium);
}

.accountIcon {
  font-size: 2.5rem;
  color: var(--primary-dark-blue);
}

.accountType {
  font-size: 1.2rem;
  font-weight: 600;
  color: var(--neutral-800);
}

.accountEmail {
  font-size: 0.9rem;
  color: var(--neutral-600);
  word-break: break-all;
}

.accountActions {
  display: flex;
  gap: 10px;
  margin-top: 10px;
}

.actionButton {
  padding: 8px 15px;
  border-radius: 8px;
  font-size: 0.9rem;
  font-weight: 600;
  cursor: pointer;
  transition: all 0.2s ease;
}

.switchButton {
  background: var(--primary-color);
  color: white;
  border: none;
}

.switchButton:hover {
  background: var(--primary-dark);
}

.deleteButton {
  background: var(--danger-color);
  color: rgb(207, 0, 0);
  border: none;
}

.deleteButton:hover {
  background: var(--danger-dark);
}

.addButton {
  background: var(--success-color);
  color: white;
  border: none;
}

.addButton:hover {
  background: var(--success-dark);
}

.modalOverlay {
  position: fixed;
  top: 0;
  left: 0;
  width: 100vw;
  height: 100vh;
  background: rgba(0, 0, 0, 0.5);
  display: flex;
  align-items: center;
  justify-content: center;
  z-index: 1000;
  backdrop-filter: blur(5px);
}

.modal {
  background: var(--card-background);
  border-radius: 20px;
  padding: 30px;
  box-shadow: var(--shadow-medium);
  max-width: 500px;
  width: 90%;
  text-align: center;
  direction: rtl;
}

.modalTitle {
  font-size: 1.5rem;
  font-weight: 700;
  color: var(--primary-color);
  margin-bottom: 15px;
}

.modalText {
  font-size: 1rem;
  color: var(--neutral-700);
  margin-bottom: 20px;
  line-height: 1.6;
}

.modalInput {
  width: calc(100% - 20px);
  padding: 10px;
  border: 1px solid var(--neutral-300);
  border-radius: 8px;
  margin-bottom: 20px;
  font-size: 1rem;
  direction: rtl;
  text-align: right;
}

.modalButtons {
  display: flex;
  justify-content: center;
  gap: 15px;
}

.modalButton {
  padding: 10px 20px;
  border-radius: 8px;
  font-size: 1rem;
  font-weight: 600;
  cursor: pointer;
  border: none;
  transition: all 0.2s ease;
}

.modalConfirmButton {
  background: var(--primary-color);
  color: white;
}

.modalConfirmButton:hover {
  background: var(--primary-dark);
}

.modalCancelButton {
  background: var(--neutral-200);
  color: var(--neutral-700);
}

.modalCancelButton:hover {
  background: var(--neutral-300);
}

/* SweetAlert2 Custom Styles */
.swal2-popup {
  background: var(--card-bg) !important;
  border-radius: 20px !important;
  box-shadow: var(--shadow-medium) !important;
  padding: 30px !important;
  direction: rtl !important;
  text-align: right !important;
}

.swal2-title {
  font-size: 1.5rem !important;
  font-weight: 700 !important;
  background: linear-gradient(135deg, var(--primary-dark-blue) 0%, var(--primary-medium-blue) 100%) !important;
  -webkit-background-clip: text !important;
  -webkit-text-fill-color: transparent !important;
  background-clip: text !important;
  margin-bottom: 15px !important;
}

.swal2-html-container {
  font-size: 1rem !important;
  color: var(--neutral-700) !important;
  margin-bottom: 20px !important;
  line-height: 1.6 !important;
}

.swal2-input {
  padding: 10px !important;
  border: 1px solid var(--neutral-300) !important;
  border-radius: 8px !important;
  margin-bottom: 20px !important;
  font-size: 1rem !important;
  direction: rtl !important;
  text-align: center !important;
}

.swal2-actions {
  display: flex !important;
  justify-content: center !important;
  gap: 15px !important;
  margin-top: 20px !important;
  flex-wrap: nowrap !important; /* Added to prevent wrapping */
}

.swal2-confirm,
.swal2-cancel {
  padding: 10px 20px !important;
  border-radius: 8px !important;
  font-size: 1rem !important;
  font-weight: 600 !important;
  cursor: pointer !important;
  border: none !important;
  transition: all 0.2s ease !important;
}

.swal2-confirm {
  background: var(--primary-color) !important;
  color: white !important;
}

.swal2-confirm:hover {
  background: var(--primary-dark) !important;
}

.swal2-cancel {
  background: var(--neutral-200) !important;
  color: var(--neutral-700) !important;
}

.swal2-cancel:hover {
  background: var(--neutral-300) !important;
}

.paymentInfoCard {
  background-color: var(--primary-light-blue);
  border: 1px solid var(--primary-border-blue);
  border-radius: 12px;
  padding: 20px;
  display: flex;
  flex-direction: column;
  align-items: center;
  text-align: center;
  gap: 15px;
  margin-top: 10px;
}

.paymentInfoText {
  font-size: 1rem;
  color: var(--neutral-700);
  line-height: 1.6;
}

.whatsappLink {
  display: inline-flex;
  align-items: center;
  gap: 8px;
  color: #25D366;
  font-weight: 600;
  text-decoration: none;
  transition: transform 0.2s ease;
}

.whatsappLink:hover {
  transform: scale(1.05);
}

.subscriptionCard {
  background-color: var(--primary-light-blue);
  border: 1px solid var(--primary-border-blue);
  border-radius: 12px;
  padding: 20px;
  text-align: center;
  margin-top: 10px;
}

.subscriptionCard.trial {
  background-color: #e0f7fa;
  border-color: #4dd0e1;
}

.subscriptionCard.expired {
  background-color: #ffebee;
  border-color: #ef5350;
}

.subscriptionText {
  font-size: 1.1rem;
  font-weight: 500;
  margin-bottom: 10px;
}

.daysRemaining {
  font-size: 1.5rem;
  font-weight: 700;
  color: var(--primary-dark-blue);
}

.expiredText {
    font-size: 1.2rem;
    font-weight: 600;
    color: #d32f2f;
}

.whatsappLink {
  display: inline-flex;
  align-items: center;
  gap: 8px;
  color: #25D366;
  font-weight: 600;
  text-decoration: none;
  transition: transform 0.2s ease;
  margin-top: 15px;
}

.whatsappLink:hover {
  transform: scale(1.05);
}

/* Responsive adjustments */
@media (max-width: 768px) {
  .content {
    padding: 0px;
    margin-top: 20px;
  }

  .profileHeader {
    gap: 10px;
  }

  .profilePhotoContainer {
    width: 100px;
    height: 100px;
  }

  .initialsPlaceholder {
    font-size: 2.5rem;
  }

  .userName {
    font-size: 2rem;
  }

  .welcomeMessage {
    font-size: 0.9rem;
  }

  .sectionCard {
    padding: 20px;
  }

  .sectionTitle {
    font-size: 1.2rem;
  }

  .infoItem {
    flex-direction: column;
    align-items: flex-start;
    gap: 5px;
  }

  .infoLabel {
    font-size: 0.95rem;
  }

  .infoValue {
    font-size: 0.95rem;
  }

  .accountCard {
    width: 100%;
  }

  .modal {
    padding: 20px;
  }

  .modalTitle {
    font-size: 1.3rem;
  }

  .modalText {
    font-size: 0.9rem;
  }

  .modalInput {
    /* width: calc(100% - 20px); */ /* Removed as swal2-input handles width */
  }

  .modalButtons {
    flex-direction: column;
    gap: 10px;
  }

  .modalButton {
    width: 100%;
  }
}

/* قسم تسجيل الخروج */
.logoutSection {
  margin-top: 30px;
  padding-top: 20px;
  border-top: 1px solid var(--neutral-200);
  display: flex;
  justify-content: center;
}

.logoutButton {
  display: flex;
  align-items: center;
  gap: 12px;
  padding: 14px 28px;
  background: linear-gradient(135deg, #ef4444 0%, #dc2626 100%);
  color: white;
  border: none;
  border-radius: 12px;
  font-size: 16px;
  font-weight: 600;
  cursor: pointer;
  transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
  box-shadow: 0 4px 15px rgba(239, 68, 68, 0.3);
  min-width: 160px;
  justify-content: center;
}

.logoutButton:hover {
  transform: translateY(-2px);
  box-shadow: 0 6px 20px rgba(239, 68, 68, 0.4);
  background: linear-gradient(135deg, #dc2626 0%, #b91c1c 100%);
}

.logoutButton:active {
  transform: translateY(0);
  box-shadow: 0 2px 10px rgba(239, 68, 68, 0.3);
}

/* موبايل */
@media (max-width: 768px) {
  .logoutButton {
    font-size: 14px;
    padding: 12px 24px;
    min-width: 140px;
  }
}
