import React, { useState, useEffect, createContext, useContext, Suspense, lazy } from "react";
import { BrowserRouter as Router, Routes, Route, Navigate } from "react-router-dom";
import { auth } from './config/firebaseConfig';
import { getRedirectResult, onAuthStateChanged } from "firebase/auth";
import { QueryClient, QueryClientProvider } from '@tanstack/react-query';
import LoadingSpinner from './components/ui/LoadingSpinner';
import "./styles/App.css";



const CaseRegistration = lazy(() => import('./pages/CaseRegistration'));
const CaseDetails = lazy(() => import('./pages/CaseDetails'));
const Login = lazy(() => import('./pages/Login'));
const SignUp = lazy(() => import('./pages/SignUp'));
const Dashboard = lazy(() => import('./pages/Dashboard'));
const ReportsOverview = lazy(() => import('./pages/ReportsOverview'));
const TimelinePage = lazy(() => import('./pages/TimelinePage.jsx'));
// const NotificationsPage = lazy(() => import('./pages/NotificationsPage')); // تم إزالة صفحة الإشعارات
const ProfilePage = lazy(() => import('./components/profile/ProfilePage'));
import EmailVerification from './pages/EmailVerification';

const EditFieldPage = lazy(() => import('./components/profile/EditFieldPage'));
const EditPasswordPage = lazy(() => import('./components/profile/EditPasswordPage'));
const ViewEmailPage = lazy(() => import('./components/profile/ViewEmailPage'));
const ProtectedRoute = lazy(() => import('./components/ProtectedRoute'));
const AddDeferral = lazy(() => import('./pages/CentralDetailsPage/AddDeferral.jsx'));
const AddAction = lazy(() => import('./pages/CentralDetailsPage/AddAction.jsx'));
const CaseFollowUp = lazy(() => import('./pages/CentralDetailsPage/CaseFollowUpModal'));
const AuthHandler = lazy(() => import('./pages/AuthHandler'));
const PostVerificationPage = lazy(() => import('./pages/PostVerificationPage'));
const ResetPassword = lazy(() => import('./pages/ResetPassword'));
const ResetPasswordSimple = lazy(() => import('./pages/ResetPasswordSimple'));



// Create QueryClient
const queryClient = new QueryClient({
  defaultOptions: {
    queries: {
      staleTime: 5 * 60 * 1000, // 5 دقائق
      cacheTime: 10 * 60 * 1000, // 10 دقائق
      retry: 1, // إعادة المحاولة مرة واحدة فقط
    },
  },
});

// Context for user state
const UserContext = createContext();

const NotFound = () => (
  <div style={{ textAlign: "center", padding: "50px" }}>
    <h2>404 - الصفحة غير موجودة</h2>
    <p>الصفحة التي تبحث عنها غير موجودة. تأكد من المسار أو ارجع إلى الصفحة الرئيسية.</p>
    <button onClick={() => window.location.href = "/dashboard"}>العودة للوحة التحكم</button>
  </div>
);

const Search = () => <div>صفحة البحث (تحت الإنشاء)</div>;

const App = () => {
  const [user, setUser] = useState(null);
  const [loading, setLoading] = useState(true);

  useEffect(() => {
    // onAuthStateChanged returns an unsubscribe function that we can use to clean up.
    // This listener is called when the user signs in, signs out, or when the user's ID token changes.
    // It also runs once when the listener is first attached, giving us the initial auth state.
    const unsubscribe = onAuthStateChanged(auth, (currentUser) => {
      setUser(currentUser);
      setLoading(false); // Always set loading to false after the first auth check completes.
      console.log("Auth state updated. User:", currentUser);
    });

    // The cleanup function for useEffect is called when the component unmounts.
    // This prevents memory leaks by removing the listener when it's no longer needed.
    return () => {
      console.log("Cleaning up auth listener.");
      unsubscribe();
    };
  }, []); // The empty dependency array ensures this effect runs only once when the component mounts.

  if (loading) {
    console.log("App - Initializing authentication, showing loading...");
    return <LoadingSpinner message="جاري التحقق من حالة المصادقة..." />;
  }

  return (
    <QueryClientProvider client={queryClient}>
      <UserContext.Provider value={{ user, loading }}>
        <Router>
          <Suspense fallback={<LoadingSpinner message="جاري تحميل الصفحة..." />}>
            <Routes>
              <Route path="/" element={user ? (user.emailVerified ? <Navigate to="/dashboard" replace /> : <Navigate to="/verify-email" replace />) : <Navigate to="/login" replace />} />
              <Route path="/login" element={<Login />} />
              <Route path="/signup" element={<SignUp />} />
              <Route path="/verify-email" element={<EmailVerification />} />
              <Route path="/__/auth/action" element={<AuthHandler />} />
              <Route path="/auth-handler" element={<AuthHandler />} />
              <Route path="/welcome" element={<PostVerificationPage />} />
              <Route
                path="/dashboard"
                element={
                  <ProtectedRoute user={user} loading={loading}>
                    <Dashboard currentUser={user} />
                  </ProtectedRoute>
                }
              />
              <Route
                path="/cases"
                element={
                  <ProtectedRoute user={user} loading={loading}>
                    <CaseRegistration currentUser={user} />
                  </ProtectedRoute>
                }
              />
              <Route
                path="/case-details/:caseNumber"
                element={
                  <ProtectedRoute user={user} loading={loading}>
                    <CaseDetails currentUser={user} />
                  </ProtectedRoute>
                }
              />
              <Route
                path="/reports"
                element={
                  <ProtectedRoute user={user} loading={loading}>
                    <ReportsOverview currentUser={user} />
                  </ProtectedRoute>
                }
              />
              <Route
                path="/notifications"
                element={
                  <ProtectedRoute user={user} loading={loading}>
                    <ReportsOverview currentUser={user} />
                  </ProtectedRoute>
                }
              />
              <Route
                path="/radar"
                element={
                  <ProtectedRoute user={user} loading={loading}>
                    <TimelinePage currentUser={user} />
                  </ProtectedRoute>
                }
              />
              <Route
                path="/profile"
                element={
                  <ProtectedRoute user={user} loading={loading}>
                    <ProfilePage currentUser={user} />
                  </ProtectedRoute>
                }
              />
              
              <Route
                path="/edit-field/:fieldName"
                element={
                  <ProtectedRoute user={user} loading={loading}>
                    <EditFieldPage currentUser={user} />
                  </ProtectedRoute>
                }
              />
              <Route
                path="/edit-password"
                element={
                  <ProtectedRoute user={user} loading={loading}>
                    <EditPasswordPage currentUser={user} />
                  </ProtectedRoute>
                }
              />
              <Route
                path="/view-email"
                element={
                  <ProtectedRoute user={user} loading={loading}>
                    <ViewEmailPage currentUser={user} />
                  </ProtectedRoute>
                }
              />
              <Route
                path="/search"
                element={
                  <ProtectedRoute user={user} loading={loading}>
                    <Search currentUser={user} />
                  </ProtectedRoute>
                }
              />
              <Route
                path="/add-deferral/:caseNumber"
                element={
                  <ProtectedRoute user={user} loading={loading}>
                    <AddDeferral currentUser={user} />
                  </ProtectedRoute>
                }
              />
              <Route
                path="/add-action/:caseNumber"
                element={
                  <ProtectedRoute user={user} loading={loading}>
                    <AddAction currentUser={user} />
                  </ProtectedRoute>
                }
              />
              <Route
                path="/case-follow-up/:caseId"
                element={
                  <ProtectedRoute user={user} loading={loading}>
                    <CaseFollowUp currentUser={user} />
                  </ProtectedRoute>
                }
              />
              
              
              
              <Route path="/reset-password" element={<ResetPassword />} />
              <Route path="/reset-password-simple" element={<ResetPasswordSimple />} />
             
              
              
              
              <Route path="*" element={<NotFound />} />
            </Routes>
          </Suspense>
        </Router>
      </UserContext.Provider>
    </QueryClientProvider>
  );
};

export const useUser = () => useContext(UserContext);

export default App;