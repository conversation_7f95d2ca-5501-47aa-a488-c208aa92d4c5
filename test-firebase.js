import { initializeApp } from 'firebase/app';
import { getAuth, sendPasswordResetEmail } from 'firebase/auth';

const firebaseConfig = {
  apiKey: "AIzaSyCM9JU8gQuL6uCuVVMw04p3RxnEF5-dWq8",
  authDomain: "agendicial.firebaseapp.com",
  projectId: "agendicial",
  storageBucket: "agendicial.firebasestorage.app",
  messagingSenderId: "986949638500",
  appId: "1:986949638500:web:39bc42c6ac795503850271",
  measurementId: "G-Q6STWWKFZV"
};

const app = initializeApp(firebaseConfig);
const auth = getAuth(app);

// اختبار إرسال رسالة إعادة تعيين كلمة المرور
async function testPasswordReset() {
  try {
    const testEmail = '<EMAIL>'; // ضع هنا إيميل اختبار
    await sendPasswordResetEmail(auth, testEmail);
    console.log('✅ تم إرسال رسالة إعادة تعيين كلمة المرور بنجاح');
  } catch (error) {
    console.error('❌ خطأ في إرسال رسالة إعادة تعيين كلمة المرور:', error.code, error.message);
  }
}

testPasswordReset();