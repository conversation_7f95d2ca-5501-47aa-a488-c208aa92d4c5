import React, { useState, useEffect, useRef } from 'react';
import { useNavigate } from 'react-router-dom';
import styles from './PostVerificationPage.module.css';
import { useUser } from '../App'; // Import useUser from App.jsx

const PostVerificationPage = () => {
  const navigate = useNavigate(); // Initialize useNavigate
  const { user: currentUser } = useUser(); // Get currentUser from context
  const [currentStep, setCurrentStep] = useState(0);
  const [displayStep, setDisplayStep] = useState(0); // The step currently being displayed
  const [animationClass, setAnimationClass] = useState('');
  const [prevAnimationClass, setPrevAnimationClass] = useState('');
  const animationDuration = 700; // Match CSS animation duration

  // Placeholder data - replace with actual data from your app state or API
  const userData = {
    name: currentUser?.displayName || 'المستخدم الجديد',
    email: currentUser?.email || '<EMAIL>',
    phone: currentUser?.phoneNumber || null, // Get phone number if available
  };

  const trialData = {
    daysRemaining: 14,
    endDate: '2025-08-12',
  };

  const features = [
    'ميزة حصرية للخطة المدفوعة 1',
    'ميزة حصرية للخطة المدفوعة 2',
    'ميزة حصرية للخطة المدفوعة 3',
    'وصول كامل لجميع الأدوات',
    'دعم فني على مدار الساعة',
  ];

  const steps = [
    {
      subtitle: 'تم التحقق من حسابك بنجاح. إليك ما تحتاج إلى معرفته للبدء.',
      content: (
        <section className={`${styles.card} ${styles.accountInfo}`}>
          <p>أهلاً وسهلاً بك يا <strong>{userData.name}</strong></p>
          <p>نورتنا في تطبيقنا، ويسعدنا انضمامك لينا.</p>
          <p>البريد الإلكتروني المسجّل: <strong>{userData.email}</strong></p>
          {userData.phone && <p>رقم الهاتف: <strong>{userData.phone}</strong></p>}
          <p>️ من هنا تقدر تبدأ تستخدم التطبيق، تسجل بياناتك، وتتابع كل التفاصيل بسهولة.</p>
          <p>لو احتجت أي مساعدة، إحنا دايمًا موجودين علشانك.</p>
          <p>شكرًا لانضمامك، ونتمنالك تجربة مميزة ❤️</p>
        </section>
      ),
    },
    {
      subtitle: 'تعرف على المدة المتبقية في فترتك التجريبية.',
      content: (
        <section className={`${styles.card} ${styles.trialInfo}`}>
          <p>أنت الآن على الخطة التجريبية لمدة 20 يومًا.</p>
          <p>خلال هذه الفترة، يمكنك الاستفادة من جميع مزايا التطبيق بدون أي قيود، وتشمل:</p>
          <ul>
            <li>تسجيل ومتابعة القضايا بسهولة</li>
            <li>️ حفظ المستندات والمرفقات</li>
            <li>⏰ التذكير التلقائي بالجلسات والإجراءات</li>
            <li>مشاركة القضايا مع فريق العمل</li>
            <li>عرض تقارير مفصّلة عن القضايا والإجراءات</li>
          </ul>
          <p>⏳ تنتهي الفترة التجريبية في: <strong>{trialData.endDate}</strong></p>
          <p>قبل انتهاء الفترة، سنذكّرك بخيارات الترقية للاستمرار في استخدام التطبيق بدون انقطاع.</p>
          <p>ابدأ الآن وانطلق بتجربتك الكاملة!</p>
        </section>
      ),
    },
    {
      subtitle: 'اختر طريقة حفظ بياناتك التي تناسبك.',
      content: (
        <section className={`${styles.card} ${styles.featuresSection}`}>
          <p>☁️ <strong>التخزين السحابي و المحلي – مرونة في حفظ بياناتك</strong></p>
          <p>في تطبيقنا، لديك خياران لحفظ بياناتك:</p>
          <div className={styles.storageOptions}>
            <div className={styles.storageOptionItem}>
              <h2>الحساب السحابي (Cloud)</h2>
              <ul>
                <li>✅ آمن، متاح من أي مكان، ومزامنة تلقائية.</li>
                <li>مشاركة القضايا ونسخ احتياطي تلقائي.</li>
              </ul>
            </div>
            <div className={styles.storageOptionItem}>
              <h2>الحساب المحلي (Offline)</h2>
              <ul>
                <li>بياناتك على جهازك فقط، يعمل بدون إنترنت.</li>
                <li>خصوصية تامة، لكن لا يمكن استعادة البيانات عند فقدان الجهاز.</li>
              </ul>
            </div>
          </div>
          <p><strong>ملاحظة:</strong> يمكنك التبديل بينهما من الإعدادات.</p>
          <p>اختر الأنسب لعملك، واستمتع بالمرونة والأمان!</p>
        </section>
      ),
    },
  ];

  const handleNext = () => {
    if (currentStep < steps.length - 1) {
      setAnimationClass(styles.slideOutLeft); // Animate current content out to left
      setTimeout(() => {
        setCurrentStep(currentStep + 1);
        setAnimationClass(styles.slideInRight); // Animate new content in from right
      }, animationDuration); // Wait for old content to slide out
    }
  };

  const handlePrevious = () => {
    if (currentStep > 0) {
      setAnimationClass(styles.slideOutRight); // Animate current content out to right
      setTimeout(() => {
        setCurrentStep(currentStep - 1);
        setAnimationClass(styles.slideInLeft); // Animate new content in from left
      }, animationDuration); // Wait for old content to slide out
    }
  };

  useEffect(() => {
    // Set initial animation for the first step
    setAnimationClass(styles.slideInRight);
  }, []);

  useEffect(() => {
    // When currentStep changes, ensure the new content slides in
    // This useEffect will run after the setTimeout in handleNext/Previous
    // and also on initial load.
    setDisplayStep(currentStep);
  }, [currentStep]);

  return (
    <div className={styles.pageContainer}>
      <header className={styles.header}>
        <p>{steps[displayStep].subtitle}</p>
      </header>

      <div className={styles.stepContentContainer}>
        <div className={`${styles.stepContent} ${animationClass}`}>
          {steps[displayStep].content}
        </div>
      </div>

      <div className={styles.navigationControls}>
        {currentStep > 0 && (
          <button className={styles.navButton} onClick={handlePrevious}>
            السابق
          </button>
        )}
        {currentStep < steps.length - 1 && (
          <button className={styles.navButton} onClick={handleNext}>
            التالي
          </button>
        )}
        {currentStep === steps.length - 1 && (
          <button className={styles.navButton} onClick={() => navigate('/dashboard')}>
            ابدأ الآن
          </button>
        )}
      </div>
    </div>
  );
};

export default PostVerificationPage;
