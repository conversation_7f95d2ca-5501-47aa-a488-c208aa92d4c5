import React, { useState, useCallback, useMemo } from 'react';
import { useParams } from 'react-router-dom';
import useCaseDetails from './CentralDetailsPage/useCaseDetails';
import {
  addDeferralLogic,
  addActionLogic,
  completeTaskLogic,
  deleteTaskLogic,
  saveJudgmentLogic
} from './CentralDetailsPage/ReportDetailsLogic';
import { getActiveAccount, deleteCase } from '../services/StorageService';

import TopBar from '../components/topbar/TopBar';
import LoadingSpinner from '../components/ui/LoadingSpinner';
import CaseInfoGroups from './CentralDetailsPage/CaseInfoGroups';
import UnifiedTasksAndReport from './CentralDetailsPage/UnifiedTasksAndReport';
import JudgmentVerdictModal from './CentralDetailsPage/JudgmentVerdictModal';
import styles from './CentralDetailsPage/CaseDetailsNew.module.css';

const CaseDetails = ({ currentUser }) => {
  const { caseNumber: caseId } = useParams();
  const { caseData, loading, error, refetch, updateCaseData } = useCaseDetails(caseId, currentUser);

  const [showJudgmentModal, setShowJudgmentModal] = useState(false);
  const [judgmentDeferral, setJudgmentDeferral] = useState(null);

  const handleAddTask = useCallback(async (taskType, data) => {
    if (!caseData) return;
    try {
      const logicFunction = taskType === 'deferral' ? addDeferralLogic : addActionLogic;
      const updatePayload = logicFunction(data, caseData, currentUser);
      await updateCaseData(updatePayload);
      alert('تمت إضافة المهمة بنجاح.');
    } catch (err) {
      alert(`خطأ في إضافة المهمة: ${err.message}`);
    }
  }, [caseData, currentUser, updateCaseData]);

  const handleCompleteTask = useCallback(async (item) => {
    if (!caseData) return;
    try {
      const result = completeTaskLogic(item, caseData, currentUser);
      if (result.type === 'judgment') {
        setJudgmentDeferral(result.data);
        setShowJudgmentModal(true);
      } else if (result.type === 'completed') {
        await updateCaseData(result.payload);
        alert('تم إكمال المهمة بنجاح.');
      }
    } catch (err) {
      alert(`خطأ في إكمال المهمة: ${err.message}`);
    }
  }, [caseData, currentUser, updateCaseData]);

  const handleDeleteTask = useCallback(async (item) => {
    if (!caseData || !window.confirm("هل أنت متأكد من حذف هذه المهمة نهائياً؟")) return;
    try {
      const updatePayload = deleteTaskLogic(item, caseData);
      await updateCaseData(updatePayload);
      alert('تم حذف المهمة بنجاح.');
    } catch (err) {
      alert(`خطأ في حذف المهمة: ${err.message}`);
    }
  }, [caseData, updateCaseData]);

  const handleSaveJudgment = useCallback(async (verdictData) => {
    if (!judgmentDeferral || !caseData) return;
    try {
        const updatePayload = saveJudgmentLogic(verdictData, judgmentDeferral, caseData, currentUser);
        await updateCaseData(updatePayload);
        setShowJudgmentModal(false);
        setJudgmentDeferral(null);
        alert('تم حفظ منطوق الحكم بنجاح.');
    } catch (err) {
        alert(`خطأ في حفظ الحكم: ${err.message}`);
    }
  }, [judgmentDeferral, caseData, currentUser, updateCaseData]);

  const handleSaveNote = useCallback(async (noteData) => {
      if (!caseData) return;
      try {
          const isDeleted = noteData.isDeleted;
          const noteId = noteData.id;
          let updatedNotes;

          if (isDeleted) {
              updatedNotes = (caseData.notes || []).filter(note => note.id !== noteId);
          } else {
              const existingNoteIndex = (caseData.notes || []).findIndex(note => note.id === noteId);
              if (existingNoteIndex > -1) {
                  updatedNotes = [...caseData.notes];
                  updatedNotes[existingNoteIndex] = noteData;
              } else {
                  updatedNotes = [...(caseData.notes || []), noteData];
              }
          }
          await updateCaseData({ notes: updatedNotes });
      } catch (err) {
          alert(`خطأ في حفظ الملاحظة: ${err.message}`);
      }
  }, [caseData, updateCaseData]);

  const handleCaseDelete = useCallback(async (caseId) => {
    if (!currentUser) {
      throw new Error('المستخدم غير مصادق عليه');
    }

    try {
      console.log('🗑️ بدء عملية حذف القضية من CaseDetails:', caseId);
      await deleteCase(currentUser.uid, caseId);
      console.log('✅ تم حذف القضية بنجاح من CaseDetails');
      return true;
    } catch (error) {
      console.error('❌ خطأ في حذف القضية من CaseDetails:', error);
      throw error;
    }
  }, [currentUser]);

  if (loading) return <LoadingSpinner message="جاري تحميل تفاصيل القضية..." />;
  if (error) return <div className={styles.errorMessage}>{error} <button onClick={refetch}>إعادة المحاولة</button></div>;
  if (!caseData) return <div className={styles.loadingMessage}>لم يتم العثور على القضية.</div>;

  return (
    <div className={styles.pageWrapper} id="case-details-page">
      <TopBar currentUser={currentUser} casesList={[]} />
      <div className={styles.mainContainer}>
        
        {showJudgmentModal && (
          <JudgmentVerdictModal
            deferralData={judgmentDeferral}
            onSave={handleSaveJudgment}
            onCancel={() => setShowJudgmentModal(false)}
          />
        )}

        <div className={styles.sectionContainer}>
          <CaseInfoGroups
            caseData={caseData}
            currentUser={currentUser}
            onCaseDataUpdate={updateCaseData}
            onCaseDelete={handleCaseDelete}
          />
        </div>

        <div className={styles.sectionContainer}>
          <UnifiedTasksAndReport 
            currentUser={currentUser} 
            actions={caseData.actions || []} 
            deferrals={caseData.deferrals || []} 
            history={caseData.history || []} 
            caseItem={caseData}
            onCompleteTask={handleCompleteTask}
            onDeleteTask={handleDeleteTask}
            onAddTask={handleAddTask}
            onSaveNote={handleSaveNote}
          />
        </div>
      </div>
    </div>
  );
};

export default CaseDetails;
