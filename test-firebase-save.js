// ملف اختبار لتجربة حفظ البيانات في Firebase
import { initializeApp } from 'firebase/app';
import { getAuth, signInWithEmailAndPassword } from 'firebase/auth';
import { getFirestore, collection, addDoc } from 'firebase/firestore';

const firebaseConfig = {
  apiKey: "AIzaSyCM9JU8gQuL6uCuVVMw04p3RxnEF5-dWq8",
  authDomain: "agendicial.firebaseapp.com",
  projectId: "agendicial",
  storageBucket: "agendicial.firebasestorage.app",
  messagingSenderId: "986949638500",
  appId: "1:986949638500:web:39bc42c6ac795503850271",
  measurementId: "G-Q6STWWKFZV"
};

// تهيئة Firebase
const app = initializeApp(firebaseConfig);
const auth = getAuth(app);
const db = getFirestore(app);

async function testFirebaseSave() {
  try {
    console.log('🔥 بدء اختبار Firebase...');
    
    // تسجيل الدخول (استخدم بيانات حساب حقيقي للاختبار)
    const email = prompt('أدخل الإيميل:');
    const password = prompt('أدخل كلمة المرور:');
    
    if (!email || !password) {
      console.log('❌ لم يتم إدخال بيانات تسجيل الدخول');
      return;
    }
    
    console.log('🔐 محاولة تسجيل الدخول...');
    const userCredential = await signInWithEmailAndPassword(auth, email, password);
    const user = userCredential.user;
    
    console.log('✅ تم تسجيل الدخول بنجاح!');
    console.log('👤 المستخدم:', user.uid);
    console.log('📧 الإيميل:', user.email);
    console.log('✅ الإيميل مُفعّل:', user.emailVerified);
    
    // الحصول على التوكن
    const token = await user.getIdToken();
    console.log('🎫 تم الحصول على التوكن بنجاح');
    
    // بيانات قضية تجريبية
    const testCaseData = {
      fullCaseNumber: 'TEST-2024',
      caseNumber: 'TEST',
      caseYear: '2024',
      clientName: 'عميل تجريبي',
      opponentName: 'خصم تجريبي',
      caseDescription: 'قضية تجريبية لاختبار النظام',
      caseCategory: 'مدني',
      caseDegree: 'ابتدائي',
      courtLocation: 'محكمة تجريبية',
      circleNumber: '1',
      caseDate: new Date().toISOString().split('T')[0],
      caseStatus: 'قيد النظر',
      reportNumber: null,
      reportLocation: null,
      deferrals: [],
      actions: [],
      history: [],
      createdAt: new Date().toISOString(),
      updatedAt: new Date().toISOString(),
      userId: user.uid,
      originalCaseId: null,
      originalCaseDegree: null,
      originalCaseNumber: null,
      isTransferredCase: false
    };
    
    console.log('📄 بيانات القضية التجريبية:', testCaseData);
    
    // محاولة حفظ البيانات
    console.log('💾 محاولة حفظ القضية في Firestore...');
    const casesRef = collection(db, 'cases');
    const docRef = await addDoc(casesRef, testCaseData);
    
    console.log('🎉 تم حفظ القضية بنجاح!');
    console.log('🆔 معرف القضية:', docRef.id);
    
    alert('✅ تم اختبار Firebase بنجاح! تحقق من الكونسول للتفاصيل.');
    
  } catch (error) {
    console.error('❌ خطأ في اختبار Firebase:');
    console.error('نوع الخطأ:', error.name);
    console.error('رمز الخطأ:', error.code);
    console.error('رسالة الخطأ:', error.message);
    console.error('تفاصيل الخطأ:', error);
    
    alert('❌ فشل اختبار Firebase! تحقق من الكونسول للتفاصيل.');
  }
}

// تشغيل الاختبار عند تحميل الصفحة
if (typeof window !== 'undefined') {
  window.testFirebaseSave = testFirebaseSave;
  console.log('🧪 لتشغيل اختبار Firebase، اكتب في الكونسول: testFirebaseSave()');
}

export { testFirebaseSave };
