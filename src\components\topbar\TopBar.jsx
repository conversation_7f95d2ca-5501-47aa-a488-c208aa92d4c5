import React, { useState, useEffect } from 'react';
import { FiArrowRight, FiPlusSquare } from 'react-icons/fi'; // تم تغيير FiPlusCircle إلى FiPlusSquare

import { useNavigate, useLocation } from 'react-router-dom';
import { db } from "../../config/firebaseConfig";
import { doc, getDoc } from "firebase/firestore";
import styles from './TopBar.module.css';
import { getActiveAccount } from '../../services/StorageService';







const TopBar = ({ currentUser }) => {
  const [activeAccount, setActiveAccountState] = useState(getActiveAccount());
  const navigate = useNavigate();
  const location = useLocation();
  const [userPhotoURL, setUserPhotoURL] = useState(null);
  const [userName, setUserName] = useState(null);
  // REMOVE: const { theme, changeTheme } = useTheme();
  
  useEffect(() => {
    const handleStorageChange = (e) => {
      if (e.key === 'user_permissions') {
        
      }
      const newActiveAccount = getActiveAccount();
      if (newActiveAccount !== activeAccount) {
        setActiveAccountState(newActiveAccount);
      }
    };

    

    
    window.addEventListener('storage', handleStorageChange);

    const intervalId = setInterval(() => {
      const currentActiveAccount = getActiveAccount();
      if (currentActiveAccount !== activeAccount) {
        setActiveAccountState(currentActiveAccount);
      }
      
    }, 1000);

    return () => {
      window.removeEventListener('storage', handleStorageChange);
      clearInterval(intervalId);
    };
  }, [activeAccount, currentUser]);

  useEffect(() => {
    const fetchUserData = async () => {
      if (!currentUser?.uid) return;

      const activeAccount = getActiveAccount();
      console.log('جاري جلب بيانات المستخدم، الحساب النشط:', activeAccount);

      try {
        if (activeAccount === 'local') {
          const localData = localStorage.getItem('localUserData_' + currentUser.uid);
          if (localData) {
            const parsedData = JSON.parse(localData);
            setUserPhotoURL(parsedData.photoURL || currentUser.photoURL || null);
            setUserName(parsedData.name || parsedData.username || currentUser.displayName || null);
            return;
          }
        }

        const userRef = doc(db, 'users', currentUser.uid);
        const userSnap = await getDoc(userRef);

        if (userSnap.exists()) {
          const userData = userSnap.data();
          setUserPhotoURL(userData.photoURL || currentUser.photoURL || null);
          setUserName(userData.name || userData.username || currentUser.displayName || null);
        } else {
          setUserPhotoURL(currentUser.photoURL || null);
          setUserName(currentUser.displayName || null);
        }
      } catch (error) {
        console.error('خطأ في جلب بيانات المستخدم:', error);
        setUserPhotoURL(currentUser.photoURL || null);
        setUserName(currentUser.displayName || null);
      }
    };

    fetchUserData();
  }, [currentUser, activeAccount]);



  const handleAddCase = () => {
    navigate('/cases');
  };

  // تحقق مما إذا كان المستخدم في صفحة Dashboard
  const isDashboard = location.pathname === '/' || location.pathname === '/dashboard';

  return (
    <div className={styles.headerContainer}>
      <header className={`${styles.topBar} ${styles.liquid}`}> {/* Force liquid style */}
        <div className={styles.leftSection}>
          {/* زر الملف الشخصي - الأول من اليمين (أقصى اليمين) */}
          <div className={styles.iconWrapper}>
            <button
              className={styles.iconButton}
              onClick={() => navigate('/profile')}
              aria-label="الملف الشخصي"
            >
              {userPhotoURL ? (
                <img
                  src={userPhotoURL}
                  alt="صورة المستخدم"
                  className={styles.userPhoto}
                  style={{width: 28, height: 28, borderRadius: '30%'}}
                />
              ) : (
                <div className={styles.userInitials} style={{width: 28, height: 28, fontSize: '14px', display: 'flex', alignItems: 'center', justifyContent: 'center', borderRadius: '30%', backgroundColor: '#667eea', color: 'white'}}>
                  {userName ? userName.charAt(0).toUpperCase() :
                    currentUser?.displayName ? currentUser.displayName.charAt(0).toUpperCase() : 'م'}
                </div>
              )}
              <span className={styles.tooltip}>الملف الشخصي</span>
            </button>
          </div>

          {/* زر إضافة قضية - الثاني من اليمين */}
          <div className={`${styles.iconWrapper} ${styles.addCaseWrapper}`}>
            <button
              className={styles.iconButton}
              onClick={handleAddCase}
              aria-label="إضافة قضية جديدة"
            >
              <FiPlusSquare size={24} />
              <span className={styles.tooltip}>إضافة قضية</span>
            </button>
          </div>



          {/* زر العودة - يظهر فقط عندما لا نكون في Dashboard */}
          {!isDashboard && (
            <div className={styles.iconWrapper}>
              <button
                className={styles.iconButton}
                onClick={() => navigate('/dashboard')}
                aria-label="العودة"
              >
                <FiArrowRight size={24} />
                <span className={styles.tooltip}>العودة</span>
              </button>
            </div>
          )}
        </div>
      </header>

    </div>
  );
};

export default TopBar;