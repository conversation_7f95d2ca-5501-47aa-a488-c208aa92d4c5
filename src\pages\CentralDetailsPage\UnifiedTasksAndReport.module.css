/* ================================================================= */
/* src/pages/CentralDetailsPage/UnifiedTasksAndReport.module.css     */
/* ================================================================= */

@import '../../styles/variables.css';

/* === الحاوية الرئيسية === */
.tasksContainer {
  margin-top: 5px;
  border-radius: 12px;
  padding: 20px;
  direction: rtl;
  background: var(--current-bg-primary);
}

/* === الشريط العلوي (العنوان والأزرار) === */
.header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding-bottom: 10px;
  border-bottom: 1px solid var(--neutral-200);
  margin-bottom: 20px;
}

.title {
  font-size: 1.4rem;
  font-weight: 600;
  color: var(--text-primary);
  margin: 0;
}

.headerActions {
  display: flex;
  align-items: center;
  gap: 12px;
}

/* === قالب الأزرار الموحد (لحل مشكلة الحجم المختلف) === */
.baseButton {
  display: inline-flex;
  align-items: center;
  justify-content: center;
  gap: 8px;
  padding: 0 16px; /* استخدام padding أفقي فقط */
  border: none;
  border-radius: 12px;
  font-size: 0.9rem;
  font-weight: 600;
  cursor: pointer;
  transition: all 0.2s ease;
  white-space: nowrap;
  height: 38px; /* ارتفاع ثابت وموحد */
  box-sizing: border-box;
}

.baseButton:hover {
  transform: translateY(-1px);
}

/* زر الإضافة يرث من القالب ويضيف لونه */
.addButton {
  composes: baseButton;
  background: var(--primary-color);
  color: white;
}
.addButton:hover {
  background: var(--primary-dark);
}

/* زر الملاحظات يرث من القالب ويضيف لونه */
.actionButton {
  composes: baseButton;
  background-color: var(--neutral-200);
  color: var(--neutral-700);
}
.actionButton:hover {
  background-color: var(--neutral-300);
}


/* === قسم عرض المهام === */
.section {
  display: flex;
  flex-direction: column;
  gap: 12px;
}

.taskTextarea {
  width: 100%;
  min-height: 120px;
  padding: 10px;
  border-radius: 12px;
  background: var(--current-bg-primary);
  font-family: "Courier New", monospace;
  font-size: 0.95rem;
  line-height: 1.6;
  color: #333;
  border: 1px solid var(--neutral-200);
}

.taskLine {
  padding: 8px 0;
  border-bottom: 1px dashed var(--neutral-300);
  display: flex;
  justify-content: space-between;
  align-items: center;
  gap: 15px;
}
.taskLine:last-child {
  border-bottom: none;
}

.taskContent {
  flex: 1;
  word-break: break-word;
}

.taskActions {
  display: flex;
  align-items: center;
  gap: 15px;
  white-space: nowrap;
}

.completeLink {
  color: var(--secondary-color);
  text-decoration: underline;
  cursor: pointer;
}
.deleteLink {
  color: var(--primary-dark-Violet);
  text-decoration: underline;
  cursor: pointer;
}

.noItems {
  text-align: center;
  color: var(--neutral-500);
  font-size: 0.9rem;
  padding: 20px;
  border: 1px dashed var(--neutral-300);
  border-radius: 12px;
}


/* === أنماط النوافذ المنبثقة (Modal) === */
.modalOverlay {
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: rgba(0, 0, 0, 0.7);
  display: flex;
  align-items: center;
  justify-content: center;
  z-index: 2000;
  padding: 20px;
}

.modal {
  background: white;
  border-radius: 20px;
  max-width: 600px;
  width: 100%;
  max-height: 90vh;
  overflow-y: auto;
  box-shadow: 0 25px 80px rgba(0, 0, 0, 0.3);
}

.modalHeader {
  padding: 16px 20px;
  border-bottom: 1px solid var(--neutral-200);
  display: flex;
  justify-content: space-between;
  align-items: center;
}

.modalHeader h3 {
  margin: 0;
  font-size: 1.2rem;
  font-weight: 600;
}

.modalContent {
  padding: 20px;
}

.addOptionsContainer {
  display: flex;
  flex-direction: column;
  gap: 12px;
  margin-top: 12px;
}

.addOptionButton {
  composes: baseButton; /* استخدام القالب الموحد للأزرار */
  width: 100%;
  justify-content: flex-start;
  background: var(--neutral-100);
  color: var(--text-primary);
}
.addOptionButton:hover {
  background: var(--neutral-200);
}

.modalActions {
  display: flex;
  gap: 12px;
  padding: 16px 20px;
  border-top: 1px solid var(--neutral-200);
  justify-content: flex-end;
}

.cancelButton {
  composes: baseButton;
  background: var(--neutral-200);
  color: var(--text-primary);
}
.cancelButton:hover {
  background: var(--neutral-300);
}

.confirmDeleteButton {
  composes: baseButton;
  color: white;
}
.confirmDeleteButton:hover {
  background: var(--danger-dark);
}
