import React from 'react';
import { FaGlobe, FaMobileAlt, FaTimes } from 'react-icons/fa';
import styles from './ProfilePage.module.css';

const AccountCard = ({
  type, // 'online' | 'local' | 'add'
  active,
  email,
  onDelete,
  onClick,
  isAdd,
  onAdd,
}) => {
  if (isAdd) {
    return (
      <div
        className={styles.accountCardAdd}
        onClick={onAdd}
        tabIndex={0}
        role="button"
        onKeyPress={e => (e.key === 'Enter' ? onAdd() : null)}
      >
        <div className={styles.accountCardAddIcon}>
          <FaMobileAlt size={40} color="#388e3c" />
        </div>
        <div className={styles.accountCardAddTitle}>إضافة حساب أوفلاين</div>
        <div className={styles.accountCardAddDesc}>يمكنك إنشاء حساب محلي منفصل</div>
      </div>
    );
  }

  const isOnline = type === 'online';
  const isLocal = type === 'local';

  return (
    <div
      className={
        `${styles.accountCard} ` +
        (isOnline && active ? styles.accountCardOnlineActive : '') +
        (isLocal && active ? styles.accountCardLocalActive : '')
      }
      onClick={onClick}
      tabIndex={0}
      role="button"
      onKeyPress={e => (e.key === 'Enter' ? onClick() : null)}
    >
      {active && (
        <div className={isOnline ? styles.accountCardCurrent : styles.accountCardCurrentLocal}>
          الحساب الحالي
        </div>
      )}
      <div className={isOnline ? styles.accountCardIconOnline : styles.accountCardIconLocal}>
        {isOnline ? (
          <FaGlobe size={40} color={active ? '#1976d2' : '#666'} />
        ) : (
          <FaMobileAlt size={40} color={active ? '#388e3c' : '#666'} />
        )}
      </div>
      <div className={
        active
          ? isOnline
            ? styles.accountCardTitleOnline
            : styles.accountCardTitleLocal
          : styles.accountCardTitle
      }>
        {isOnline ? 'الحساب الأونلاين' : 'الحساب الأوفلاين'}
      </div>
      <div className={styles.accountCardEmail}>{email || ''}</div>
      <div className={styles.accountCardDataType}>
        {isOnline ? 'بياناتك محفوظة سحابياً' : 'بياناتك محفوظة محلياً'}
      </div>
      <div style={{ flex: 1 }}></div>
      {onDelete && (
        <button
          onClick={e => {
            e.stopPropagation();
            onDelete();
          }}
          className={styles.deleteAccountButton}
          style={{ marginTop: 18, width: '90%' }}
        >
          <FaTimes /> حذف الحساب {isOnline ? 'الأونلاين' : 'الأوفلاين'}
        </button>
      )}
    </div>
  );
};

export default AccountCard; 