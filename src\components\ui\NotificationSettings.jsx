/**
 * مكون إعدادات الإشعارات الخارجية
 */

import React, { useState } from 'react';
import { FaBell, FaBellSlash, FaCheck, FaTimes, FaExclamationTriangle, FaCog } from 'react-icons/fa';
import { useNotifications } from '../../hooks/useNotifications';
import styles from './NotificationSettings.module.css';

const NotificationSettings = ({ currentUser, onClose }) => {
  const {
    notificationStatus,
    isInitialized,
    requestPermission,
    sendTestNotification,
    updateStatus
  } = useNotifications(currentUser);

  const [isLoading, setIsLoading] = useState(false);
  const [message, setMessage] = useState('');

  // طلب إذن الإشعارات
  const handleRequestPermission = async () => {
    setIsLoading(true);
    setMessage('');

    try {
      const granted = await requestPermission();
      if (granted) {
        setMessage('✅ تم منح إذن الإشعارات بنجاح!');
      } else {
        setMessage('❌ تم رفض إذن الإشعارات. يرجى تفعيلها من إعدادات المتصفح.');
      }
    } catch (error) {
      setMessage('❌ حدث خطأ في طلب إذن الإشعارات.');
      console.error('خطأ في طلب الإذن:', error);
    } finally {
      setIsLoading(false);
    }
  };

  // إرسال إشعار تجريبي
  const handleTestNotification = async () => {
    setIsLoading(true);
    setMessage('');

    try {
      const success = await sendTestNotification();
      if (success) {
        setMessage('✅ تم إرسال الإشعار التجريبي! تحقق من إشعارات النظام.');
      } else {
        setMessage('❌ فشل في إرسال الإشعار التجريبي. تأكد من منح الإذن أولاً.');
      }
    } catch (error) {
      setMessage('❌ حدث خطأ في إرسال الإشعار التجريبي.');
      console.error('خطأ في الإشعار التجريبي:', error);
    } finally {
      setIsLoading(false);
    }
  };

  // الحصول على نص حالة الإذن
  const getPermissionStatusText = () => {
    switch (notificationStatus.permission) {
      case 'granted':
        return { text: 'مُفعّلة', icon: <FaCheck />, className: styles.statusGranted };
      case 'denied':
        return { text: 'مرفوضة', icon: <FaTimes />, className: styles.statusDenied };
      default:
        return { text: 'غير محددة', icon: <FaExclamationTriangle />, className: styles.statusDefault };
    }
  };

  const permissionStatus = getPermissionStatusText();

  if (!isInitialized) {
    return (
      <div className={styles.container}>
        <div className={styles.loading}>
          <FaCog className={styles.spinningIcon} />
          <p>جاري تهيئة نظام الإشعارات...</p>
        </div>
      </div>
    );
  }

  return (
    <div className={styles.container}>
      <div className={styles.header}>
        <h3>
          <FaBell className={styles.headerIcon} />
          إعدادات الإشعارات الخارجية
        </h3>
        {onClose && (
          <button onClick={onClose} className={styles.closeButton}>
            <FaTimes />
          </button>
        )}
      </div>

      <div className={styles.content}>
        {/* معلومات الدعم */}
        <div className={styles.infoSection}>
          <h4>معلومات النظام</h4>
          <div className={styles.infoGrid}>
            <div className={styles.infoItem}>
              <span className={styles.infoLabel}>دعم الإشعارات:</span>
              <span className={`${styles.infoValue} ${notificationStatus.isSupported ? styles.supported : styles.notSupported}`}>
                {notificationStatus.isSupported ? (
                  <>
                    <FaCheck /> مدعومة
                  </>
                ) : (
                  <>
                    <FaTimes /> غير مدعومة
                  </>
                )}
              </span>
            </div>
            
            <div className={styles.infoItem}>
              <span className={styles.infoLabel}>حالة الإذن:</span>
              <span className={`${styles.infoValue} ${permissionStatus.className}`}>
                {permissionStatus.icon} {permissionStatus.text}
              </span>
            </div>

            <div className={styles.infoItem}>
              <span className={styles.infoLabel}>الإشعارات المجدولة:</span>
              <span className={styles.infoValue}>
                {notificationStatus.scheduledCount} إشعار
              </span>
            </div>
          </div>
        </div>

        {/* الإجراءات */}
        <div className={styles.actionsSection}>
          <h4>الإجراءات</h4>
          
          {!notificationStatus.isSupported ? (
            <div className={styles.warningMessage}>
              <FaExclamationTriangle />
              <p>متصفحك لا يدعم الإشعارات الخارجية. يرجى استخدام متصفح حديث مثل Chrome أو Firefox.</p>
            </div>
          ) : (
            <div className={styles.actionButtons}>
              {notificationStatus.permission !== 'granted' && (
                <button
                  onClick={handleRequestPermission}
                  disabled={isLoading}
                  className={`${styles.actionButton} ${styles.primaryButton}`}
                >
                  {isLoading ? (
                    <>
                      <FaCog className={styles.spinningIcon} />
                      جاري الطلب...
                    </>
                  ) : (
                    <>
                      <FaBell />
                      طلب إذن الإشعارات
                    </>
                  )}
                </button>
              )}

              {notificationStatus.permission === 'granted' && (
                <button
                  onClick={handleTestNotification}
                  disabled={isLoading}
                  className={`${styles.actionButton} ${styles.secondaryButton}`}
                >
                  {isLoading ? (
                    <>
                      <FaCog className={styles.spinningIcon} />
                      جاري الإرسال...
                    </>
                  ) : (
                    <>
                      <FaBell />
                      إرسال إشعار تجريبي
                    </>
                  )}
                </button>
              )}
            </div>
          )}
        </div>

        {/* رسالة الحالة */}
        {message && (
          <div className={styles.messageSection}>
            <p className={styles.message}>{message}</p>
          </div>
        )}

        {/* معلومات إضافية */}
        <div className={styles.helpSection}>
          <h4>كيف تعمل الإشعارات؟</h4>
          <ul className={styles.helpList}>
            <li>ستتلقى إشعاراً قبل يوم واحد من كل جلسة محكمة</li>
            <li>الإشعارات تظهر حتى لو كان التطبيق مغلقاً</li>
            <li>يمكنك النقر على الإشعار للانتقال مباشرة إلى القضية</li>
            <li>يتم جدولة الإشعارات تلقائياً عند تحديث التأجيلات</li>
          </ul>
        </div>
      </div>
    </div>
  );
};

export default NotificationSettings;