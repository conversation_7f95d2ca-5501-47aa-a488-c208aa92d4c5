{"name": "my-new-law-agenda", "private": true, "version": "0.0.0", "type": "module", "scripts": {"dev": "vite", "build": "vite build", "lint": "eslint .", "preview": "vite preview"}, "dependencies": {"@capacitor/android": "^7.2.0", "@capacitor/cli": "^7.2.0", "@capacitor/core": "^7.2.0", "@capacitor/network": "^7.0.1", "@capacitor/preferences": "^7.0.1", "@floating-ui/react": "^0.27.6", "@tanstack/react-query": "^5.74.4", "dexie": "^4.0.11", "firebase": "^11.6.0", "lodas": "^1.2.0", "react": "^19.1.0", "react-dom": "^19.1.0", "react-icons": "^5.5.0", "react-router-dom": "^7.5.0", "react-select": "^5.10.2", "sweetalert2": "^11.21.2", "uuid": "^11.1.0", "vite-plugin-pwa": "^1.0.2"}, "devDependencies": {"@eslint/js": "^9.21.0", "@types/react": "^19.0.10", "@types/react-dom": "^19.0.4", "@vitejs/plugin-react": "^4.3.4", "eslint": "^9.21.0", "eslint-plugin-react-hooks": "^5.1.0", "eslint-plugin-react-refresh": "^0.4.19", "globals": "^15.15.0", "vite": "^6.2.0"}}