import { initializeApp } from 'firebase/app';
import { getAuth, setPersistence, browserLocalPersistence } from 'firebase/auth';
import { getFirestore, initializeFirestore, CACHE_SIZE_UNLIMITED } from 'firebase/firestore';

// استبدل القيم دي بإعدادات مشروعك من Firebase Console
const firebaseConfig = {
  apiKey: "AIzaSyCM9JU8gQuL6uCuVVMw04p3RxnEF5-dWq8",
  authDomain: "agendicial.firebaseapp.com",
  projectId: "agendicial",
  storageBucket: "agendicial.firebasestorage.app",
  messagingSenderId: "986949638500",
  appId: "1:986949638500:web:39bc42c6ac795503850271",
  measurementId: "G-Q6STWWKFZV"
};

// تهيئة Firebase
// تهيئة Firebase
const app = initializeApp(firebaseConfig);

// إعداد Auth
export const auth = getAuth(app);

// ✨ ده السطر اللي بيحل المشكلة:
setPersistence(auth, browserLocalPersistence)
  .then(() => {
    console.log("✅ Session persistence set to 'local'");
  })
  .catch((error) => {
    console.error("❌ Failed to set persistence:", error);
  });

// إعداد Firestore مع التخزين المؤقت (Caching) والإعدادات المحسنة
export const db = initializeFirestore(app, {
  cache: {
    type: 'persistent', // استخدم 'persistent' للتخزين المؤقت على القرص
    cacheSizeBytes: CACHE_SIZE_UNLIMITED, // حجم التخزين غير محدود
  },
  // إعدادات إضافية لتحسين الأداء
  experimentalForceLongPolling: false, // تعطيل Long Polling لتحسين الأداء
  ignoreUndefinedProperties: true, // تجاهل الخصائص غير المعرفة
});