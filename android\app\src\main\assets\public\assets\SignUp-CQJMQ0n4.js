import{r as a,c as U,j as e,L as R,o as I,a as O,p as T,t as V,d as W,b as $}from"./index-HUlFBKIW.js";import{b,d as z,e as B}from"./LockoutManager-ByDxv6hW.js";function J(){const[l,w]=a.useState(""),[u,k]=a.useState(""),[d,C]=a.useState(""),[h,y]=a.useState(""),[x,r]=a.useState(""),[f,v]=a.useState(""),[n,c]=a.useState(!1),[L,p]=a.useState(0),[t,S]=a.useState(1),[g,E]=a.useState([]),F=U();a.useEffect(()=>{const s=b();s.isLocked?(r(s.message),c(!0)):p(s.attempts)},[]);const M=()=>{t<4&&(E([...g,t]),S(t+1))},q=()=>{t>1&&S(t-1)},j=s=>{switch(s){case 1:return l.trim().length>0;case 2:return u.trim().length>0&&u.includes("@");case 3:return d.trim().length>=6;case 4:return!0;default:return!1}},A=s=>{s.preventDefault(),t===4?P(s):j(t)?M():r(D(t))},D=s=>{switch(s){case 1:return"يرجى إدخال اسم المستخدم.";case 2:return"يرجى إدخال بريد إلكتروني صحيح.";case 3:return"كلمة المرور يجب أن تكون 6 أحرف على الأقل.";default:return""}},P=async s=>{if(s.preventDefault(),r(""),v(""),c(!0),!u.trim()){r("يرجى إدخال البريد الإلكتروني."),c(!1);return}if(!d.trim()){r("يرجى إدخال كلمة المرور."),c(!1);return}const N=b();if(N.isLocked){r(N.message),c(!1);return}try{const i=(await I(O,u.trim(),d)).user;console.log("تم إنشاء حساب بنجاح:",i),l.trim()&&(await T(i,{displayName:l.trim()}),console.log("تم تحديث اسم المستخدم:",l.trim())),await V(W($,"users",i.uid),{name:l.trim()||"",username:l.trim()||null,email:i.email,phone:h.trim()||null,role:"محامي",createdAt:new Date().toISOString()}),console.log("تم حفظ بيانات المستخدم في Firestore"),z(),p(0),v("تم إنشاء الحساب بنجاح! سيتم توجيهك إلى صفحة تسجيل الدخول."),setTimeout(()=>{F("/login")},1500)}catch(m){console.error("خطأ في إنشاء الحساب:",m.code,m.message);const i=B(L);if(p(i.attempts),i.isLocked){r(i.message),c(!0);return}let o="حدث خطأ أثناء إنشاء الحساب. يرجى المحاولة مرة أخرى.";switch(m.code){case"auth/email-already-in-use":o="البريد الإلكتروني هذا مستخدم بالفعل. يرجى استخدام بريد إلكتروني آخر أو تسجيل الدخول.";break;case"auth/invalid-email":o="صيغة البريد الإلكتروني غير صحيحة. يرجى إدخال بريد إلكتروني صالح.";break;case"auth/weak-password":o="كلمة المرور ضعيفة جدًا. يجب أن تحتوي على 6 أحرف على الأقل.";break;case"auth/too-many-requests":o="تم حظر إنشاء الحساب مؤقتًا بسبب كثرة المحاولات. يرجى المحاولة لاحقًا.";break;default:o="حدث خطأ غير متوقع. يرجى التحقق من اتصال الإنترنت والمحاولة لاحقًا."}r(o)}finally{c(!1)}};return e.jsxs("div",{className:"signup-container",children:[e.jsx("div",{className:"logo-section",children:e.jsx("img",{src:"/logo.png",alt:"الأجندة القضائية",className:"legal-agenda-logo"})}),e.jsxs("div",{className:"header-form",children:[e.jsx("span",{className:"title",children:"مرحباً بك!"}),e.jsx("span",{className:"subtitle",children:"اختر خياراً للمتابعة"})]}),e.jsx("div",{className:"steps-indicator",children:[1,2,3,4].map(s=>e.jsx("div",{className:`step-dot ${g.includes(s)?"completed":t===s?"active":"pending"}`,children:g.includes(s)?"✓":s},s))}),x&&e.jsx("div",{className:"error-message",children:x}),f&&e.jsx("div",{className:"success-message",children:f}),n&&e.jsx(R,{message:"جاري إنشاء الحساب..."}),e.jsxs("form",{id:"signup-form",onSubmit:A,children:[t===1&&e.jsxs("div",{className:"step-container active",children:[e.jsx("h3",{className:"step-title",children:"الخطوة 1: اسم المستخدم"}),e.jsx("input",{type:"text",id:"signup-username",name:"username",value:l,onChange:s=>w(s.target.value),disabled:n,placeholder:"اسم المستخدم",autoComplete:"username",autoFocus:!0,required:!0})]}),t===2&&e.jsxs("div",{className:"step-container active",children:[e.jsx("h3",{className:"step-title",children:"الخطوة 2: البريد الإلكتروني"}),e.jsx("input",{type:"email",id:"signup-email",name:"email",value:u,onChange:s=>k(s.target.value),required:!0,disabled:n,placeholder:"البريد الإلكتروني",autoComplete:"email",autoFocus:!0})]}),t===3&&e.jsxs("div",{className:"step-container active",children:[e.jsx("h3",{className:"step-title",children:"الخطوة 3: كلمة المرور"}),e.jsx("input",{type:"password",id:"signup-password",name:"password",value:d,onChange:s=>C(s.target.value),required:!0,disabled:n,placeholder:"كلمة المرور (6 أحرف على الأقل)",autoComplete:"new-password",autoFocus:!0})]}),t===4&&e.jsxs("div",{className:"step-container active",children:[e.jsx("h3",{className:"step-title",children:"الخطوة 4: رقم الهاتف (اختياري)"}),e.jsx("input",{type:"tel",id:"signup-phone",name:"phone",value:h,onChange:s=>y(s.target.value),disabled:n,placeholder:"رقم الهاتف (اختياري)",autoComplete:"tel",autoFocus:!0})]}),e.jsx("button",{type:"submit",disabled:n||t<4&&!j(t),children:n?"جاري المعالجة...":t===4?"إنشاء حساب":"متابعة"})]}),e.jsx("div",{className:"button-container",children:t>1&&e.jsx("button",{type:"button",onClick:q,className:"prev-btn",disabled:n,children:"السابق"})}),e.jsx("div",{className:"signup-link",children:e.jsx("a",{href:"/login",className:"create-account-link",children:"لديك حساب بالفعل؟ تسجيل الدخول"})})]})}export{J as default};
