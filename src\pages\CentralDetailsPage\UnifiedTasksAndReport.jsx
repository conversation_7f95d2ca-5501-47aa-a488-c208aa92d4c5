import React, { useState, useMemo } from 'react';
import { FaStickyNote } from 'react-icons/fa';
import AddDeferral from './AddDeferral';
import AddAction from './AddAction';
import CaseFollowUpModal from './CaseFollowUpModal';
import styles from './UnifiedTasksAndReport.module.css';

const UnifiedTasksAndReport = React.memo(({ 
  currentUser, 
  actions, 
  deferrals, 
  history, 
  caseItem, 
  onCompleteTask, 
  onDeleteTask, 
  onAddTask,
  onSaveNote
}) => {
  const [showForm, setShowForm] = useState(null);
  const [showAddOptionsModal, setShowAddOptionsModal] = useState(false);
  const [showDeleteConfirmation, setShowDeleteConfirmation] = useState(false);
  const [itemToDelete, setItemToDelete] = useState(null);
  const [showNotesModal, setShowNotesModal] = useState(false);

  const formatDate = (dateString) => {
    if (!dateString) return '';
    return new Date(dateString).toLocaleDateString('ar-EG', { year: 'numeric', month: 'long', day: 'numeric' });
  };

  const timelineEvents = useMemo(() => {
    const pendingItems = [
      ...deferrals.map(d => ({ type: 'deferral', data: d, date: new Date(d.date), status: 'pending' })),
      ...actions.map(a => ({ type: 'action', data: a, date: new Date(a.deadline), status: 'pending' }))
    ];
    
    const completedItems = history
      .filter(h => h.type === 'completed_deferral' || h.type === 'completed_action' || h.type === 'judgment_verdict')
      .map(h => ({
        type: h.type === 'completed_action' ? 'action' : 'deferral',
        data: { ...h, reasons: h.description ? h.description.split(': ') : [] },
        date: new Date(h.completedAt || h.timestamp),
        status: 'completed'
      }));

    return [...pendingItems, ...completedItems].sort((a, b) => new Date(b.date) - new Date(a.date));
  }, [deferrals, actions, history]);

  const handleSaveTask = (taskType, data) => {
    onAddTask(taskType, data);
    setShowForm(null);
  };

  const handleDeleteClick = (item) => {
    setItemToDelete(item);
    setShowDeleteConfirmation(true);
  };

  const confirmDelete = () => {
    if (itemToDelete) {
      onDeleteTask(itemToDelete);
    }
    setShowDeleteConfirmation(false);
    setItemToDelete(null);
  };

  return (
    <>
      <div className={styles.tasksContainer}>
        <div className={styles.header}>
          <h2 className={styles.title}>التقرير والمهام</h2>
          <div className={styles.headerActions}>
            <button className={styles.actionButton} onClick={() => setShowNotesModal(true)}>
              <span>الملاحظات</span>
            </button>
            <button className={styles.addButton} onClick={() => setShowAddOptionsModal(true)}>
              إضافة
            </button>
          </div>
        </div>

        <div className={styles.section}>
          {timelineEvents.length > 0 ? (
            <div className={styles.taskTextarea}>
              {timelineEvents.map((item, index) => (
                <div key={item.data.id || `history-${index}`} className={styles.taskLine}>
                  <div className={styles.taskContent}>
                    {item.status === 'completed' ? '✔ ' : '◼ '}
                    {`${index + 1}. ${item.type === 'deferral' ? 'تأجيل' : 'إجراء'} - `}
                    {`${formatDate(item.type === 'deferral' ? item.data.date || item.data.originalDate : item.data.deadline || item.data.originalDeadline)} - `}
                    {item.type === 'deferral'
                      ? (item.data.reasons?.join(', ') || item.data.description || 'تأجيل بدون تفاصيل')
                      : item.data.description}
                  </div>
                  {item.status === 'pending' && (
                    <div className={styles.taskActions}>
                      <span className={styles.completeLink} onClick={() => onCompleteTask(item)}>[إكمال]</span>
                      <span className={styles.deleteLink} onClick={() => handleDeleteClick(item)}>[حذف]</span>
                    </div>
                  )}
                </div>
              ))}
            </div>
          ) : (
            <div className={styles.noItems}>لا توجد مهام أو أحداث مسجلة.</div>
          )}
        </div>
      </div>

      {showNotesModal && (
        <div className={styles.modalOverlay}>
          <div className={styles.modal}>
            <CaseFollowUpModal
              caseId={caseItem.id}
              userId={currentUser.uid}
              savedNotes={caseItem.notes || []}
              onSave={onSaveNote}
              onClose={() => setShowNotesModal(false)}
            />
          </div>
        </div>
      )}

      {showForm && (
        <div className={styles.modalOverlay}>
          <div className={styles.modal}>
            {showForm === 'deferral' ? (
              <AddDeferral currentUser={currentUser} onSave={handleSaveTask} onClose={() => setShowForm(null)} />
            ) : (
              <AddAction onSave={handleSaveTask} onClose={() => setShowForm(null)} />
            )}
          </div>
        </div>
      )}

      {showAddOptionsModal && (
        <div className={styles.modalOverlay}>
          <div className={styles.modal}>
            <div className={styles.modalHeader}><h3>إضافة تنبيه جديد</h3></div>
            <div className={styles.modalContent}>
              <p>اختر نوع التنبيه:</p>
              <div className={styles.addOptionsContainer}>
                {caseItem?.caseStatus !== 'قيد النظر' && (
                  <button className={styles.addOptionButton} onClick={() => { setShowAddOptionsModal(false); setShowForm('deferral'); }}>تاريخ جلسة</button>
                )}
                <button className={styles.addOptionButton} onClick={() => { setShowAddOptionsModal(false); setShowForm('action'); }}>إجراء</button>
              </div>
            </div>
            <div className={styles.modalActions}>
              <button className={styles.cancelButton} onClick={() => setShowAddOptionsModal(false)}>إلغاء</button>
            </div>
          </div>
        </div>
      )}

      {showDeleteConfirmation && (
        <div className={styles.modalOverlay}>
          <div className={styles.modal}>
            <div className={styles.modalHeader}><h3>تأكيد الحذف</h3></div>
            <div className={styles.modalContent}>
              <p>هل أنت متأكد من حذف هذا {itemToDelete.type === 'deferral' ? 'التأجيل' : 'الإجراء'}؟</p>
            </div>
            <div className={styles.modalActions}>
              <button className={styles.cancelButton} onClick={() => setShowDeleteConfirmation(false)}>إلغاء</button>
              <button className={styles.confirmDeleteButton} onClick={confirmDelete}>حذف</button>
            </div>
          </div>
        </div>
      )}
    </>
  );
});

export default UnifiedTasksAndReport;
