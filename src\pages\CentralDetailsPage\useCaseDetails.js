import { useState, useEffect, useCallback, useMemo } from 'react';
import { getActiveAccount, getCase, updateCase as updateCaseInStorage } from '../../services/StorageService';
import { cacheManager } from '../../utils/CacheManager';

export default function useCaseDetails(caseId, currentUser) {
  const [caseData, setCaseData] = useState(null);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState(null);

  const CACHE_KEY = useMemo(() => `case_${caseId}`, [caseId]);

  const fetchCaseData = useCallback(async (bypassCache = false) => {
    if (!caseId || !currentUser?.uid) {
      setError('بيانات غير كافية لجلب القضية.');
      setLoading(false);
      return;
    }
    setLoading(true);

    if (!bypassCache) {
      const cachedData = cacheManager.getCache(CACHE_KEY);
      if (cachedData) {
        setCaseData(cachedData);
        setLoading(false);
        return;
      }
    }

    try {
      const data = await getCase(currentUser.uid, caseId);
      if (data && data.userId === currentUser.uid) {
        const sanitizedData = {
          ...data,
          deferrals: data.deferrals || [],
          actions: data.actions || [],
          history: data.history || [],
          notes: data.notes || [],
        };
        setCaseData(sanitizedData);
        cacheManager.updateCache(CACHE_KEY, sanitizedData);
      } else if (!data) {
        setError('لم يتم العثور على القضية.');
      } else {
        setError('لا تملك صلاحية عرض هذه القضية.');
      }
    } catch (err) {
      setError('حدث خطأ أثناء جلب البيانات.');
      console.error("fetchCaseData error:", err);
    } finally {
      setLoading(false);
    }
  }, [caseId, currentUser, CACHE_KEY]);

  const updateCaseData = useCallback(async (updatePayload) => {
    if (!caseData) return;

    const updatedData = { ...caseData, ...updatePayload, updatedAt: new Date().toISOString() };
    setCaseData(updatedData);
    cacheManager.updateCache(CACHE_KEY, updatedData);

    try {
      await updateCaseInStorage(currentUser.uid, caseId, updatePayload);
    } catch (err) {
      console.error("Failed to update case, rolling back:", err);
      setCaseData(caseData);
      cacheManager.updateCache(CACHE_KEY, caseData);
      throw err;
    }
  }, [caseData, currentUser, caseId, CACHE_KEY]);

  useEffect(() => {
    fetchCaseData();
    
    const refreshListener = ({ detail: updatedCaseId }) => {
        if (updatedCaseId === caseId) {
            fetchCaseData(true);
        }
    };
    
    document.addEventListener('caseUpdated', refreshListener);

    return () => {
      document.removeEventListener('caseUpdated', refreshListener);
    };
  }, [caseId, fetchCaseData]);

  return {
    caseData,
    loading,
    error,
    refetch: () => fetchCaseData(true),
    updateCaseData,
  };
}
