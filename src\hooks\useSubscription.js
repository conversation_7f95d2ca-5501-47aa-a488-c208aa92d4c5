import { useState, useEffect } from 'react';
import { doc, getDoc, updateDoc } from 'firebase/firestore';
import { db } from '../config/firebaseConfig';

const TRIAL_DURATION_DAYS = 20;
const LOCAL_SYNC_GRACE_PERIOD_DAYS = 21;

export const useSubscription = (user) => {
  const [subscription, setSubscription] = useState({
    status: 'loading', // loading, active, trial, expired, local_locked
    daysRemaining: null,
  });

  useEffect(() => {
    if (!user) {
      setSubscription({ status: 'unauthenticated', daysRemaining: null });
      return;
    }

    const checkSubscription = async () => {
      const userRef = doc(db, 'users', user.uid);
      try {
        // This is an online check
        const userSnap = await getDoc(userRef);
        localStorage.setItem('lastOnlineSync', new Date().toISOString());

        if (userSnap.exists()) {
          const userData = userSnap.data();
          const { subscriptionStatus, trialStartDate } = userData;

          if (subscriptionStatus === 'active') {
            setSubscription({ status: 'active', daysRemaining: null });
          } else if (subscriptionStatus === 'trial') {
            const startDate = new Date(trialStartDate);
            const now = new Date();
            const diffTime = now - startDate;
            const diffDays = Math.ceil(diffTime / (1000 * 60 * 60 * 24));

            if (diffDays > TRIAL_DURATION_DAYS) {
              await updateDoc(userRef, { subscriptionStatus: 'expired' });
              setSubscription({ status: 'expired', daysRemaining: 0 });
            } else {
              setSubscription({
                status: 'trial',
                daysRemaining: TRIAL_DURATION_DAYS - diffDays,
              });
            }
          } else {
            setSubscription({ status: 'expired', daysRemaining: 0 });
          }
        } else {
            setSubscription({ status: 'expired', daysRemaining: 0 });
        }
      } catch (error) {
        // This block runs if the user is offline
        console.log("User is offline. Checking local status.");
        const lastSync = localStorage.getItem('lastOnlineSync');
        if (lastSync) {
            const lastSyncDate = new Date(lastSync);
            const now = new Date();
            const diffTime = now - lastSyncDate;
            const diffDays = Math.ceil(diffTime / (1000 * 60 * 60 * 24));

            if (diffDays > LOCAL_SYNC_GRACE_PERIOD_DAYS) {
                setSubscription({ status: 'local_locked', daysRemaining: null });
            } else {
                // If within grace period, we can't know the real status, so we assume it's ok for now
                // but you might want to show a warning
                setSubscription({ status: 'active', daysRemaining: null }); // Assume active for offline use
            }
        } else {
            // حساب جديد غالبًا ولسه معملش sync، نديه فرصة ونعامله كـ trial مؤقتًا
            setSubscription({ status: 'trial', daysRemaining: TRIAL_DURATION_DAYS });
        }
      }
    };

    checkSubscription();
  }, [user]);

  return subscription;
};