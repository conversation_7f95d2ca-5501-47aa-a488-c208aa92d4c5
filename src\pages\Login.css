/* تصميم تسجيل الدخول الجديد بدون Container */
@import '../styles/variables.css';

.login-container {
  min-height: 100vh;
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  background: linear-gradient(135deg, #f8fafc 0%, #e2e8f0 100%);
  font-family: var(--font-family-primary);
  padding: 20px;
  box-sizing: border-box;
  overflow: hidden;
}

/* قسم الشعار */
.logo-section {
  text-align: center;
  margin-bottom: 30px;
}

.legal-agenda-logo {
  width: 300px;
  height: auto;
  margin-bottom: 0;
  filter: drop-shadow(0 4px 8px rgba(0, 0, 0, 0.1));
}

/* العناوين */
.header-form {
  display: flex;
  flex-direction: column;
  align-items: center;
  margin-bottom: 40px;
  text-align: center;
}

.header-form .title {
  font-size: 2.5rem;
  font-weight: 700;
  color: var(--primary-dark-blue);
  margin-bottom: 8px;
  line-height: 1.2;
  background: linear-gradient(135deg, var(--primary-dark-blue) 0%, var(--primary-medium-blue) 100%);
  -webkit-background-clip: text;
  -webkit-text-fill-color: transparent;
  background-clip: text;
}

.header-form .subtitle {
  color: var(--neutral-600);
  font-size: 1.1rem;
  font-weight: 400;
  margin-bottom: 0;
  line-height: 1.4;
}

/* نموذج تسجيل الدخول */
#login-form {
  display: flex;
  flex-direction: column;
  gap: 20px;
  width: 100%;
  max-width: 400px;
  margin: 0 auto;
}

/* حقول الإدخال */
#login-form input {
  padding: 18px 24px;
  border: 2px solid var(--neutral-200);
  border-radius: 12px;
  outline: none;
  transition: all 0.3s ease;
  width: 100%;
  font-size: 1.1rem;
  box-sizing: border-box;
  background-color: var(--white);
  color: var(--neutral-700);
  margin-bottom: 0;
  font-family: var(--font-family-primary);
  font-weight: 500;
  min-height: 60px;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.04);
}

#login-form input:focus {
  border-color: var(--primary-medium-blue);
  box-shadow: 0 0 0 3px rgba(76, 104, 192, 0.1), 0 4px 12px rgba(0, 0, 0, 0.1);
  transform: translateY(-2px);
}

#login-form input::placeholder {
  color: var(--neutral-500);
  font-weight: 400;
}

#login-form input:hover {
  border-color: var(--primary-medium-blue);
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.08);
}

/* الأزرار */
#login-form button {
  border: none;
  padding: 18px 24px;
  border-radius: 12px;
  cursor: pointer;
  transition: all 0.3s ease;
  font-size: 1.1rem;
  width: 100%;
  margin-bottom: 0;
  font-weight: 600;
  font-family: var(--font-family-primary);
  min-height: 60px;
  position: relative;
  overflow: hidden;
}

#login-form button[type="submit"] {
  background: linear-gradient(135deg, var(--primary-medium-blue) 0%, var(--primary-dark-blue) 100%);
  color: var(--white);
  box-shadow: 0 4px 15px rgba(76, 104, 192, 0.3);
}

#login-form button[type="submit"]:hover {
  transform: translateY(-2px);
  box-shadow: 0 8px 25px rgba(76, 104, 192, 0.4);
}

#login-form button[type="submit"]:active {
  transform: translateY(0);
}

#login-form button:disabled {
  background: var(--neutral-200);
  color: var(--neutral-500);
  cursor: not-allowed;
  transform: none;
  box-shadow: none;
}

#login-form #google-btn {
  background-color: var(--white);
  border: 2px solid var(--neutral-200);
  color: var(--neutral-700);
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.04);
}

#login-form #google-btn:hover {
  border-color: var(--primary-medium-blue);
  transform: translateY(-2px);
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.1);
}

#login-form #signup-btn {
  background: linear-gradient(135deg, var(--neutral-700) 0%, var(--neutral-800) 100%);
  color: var(--white);
  box-shadow: 0 4px 15px rgba(0, 0, 0, 0.2);
}

#login-form #signup-btn:hover {
  transform: translateY(-2px);
  box-shadow: 0 8px 25px rgba(0, 0, 0, 0.3);
}

/* خط الفاصل */
#login-form .division-or {
  display: flex;
  align-items: center;
  gap: 15px;
  margin: 25px 0;
}

#login-form .division-or span {
  color: var(--neutral-500);
  font-size: 0.9rem;
  font-weight: 500;
  background: linear-gradient(135deg, #f8fafc 0%, #e2e8f0 100%);
  padding: 8px 16px;
  border-radius: 20px;
  white-space: nowrap;
}

#login-form .h-line {
  flex: 1;
  height: 1px;
  background: linear-gradient(90deg, transparent, var(--neutral-300), transparent);
}

/* الروابط */
.forgot-password {
  text-align: center;
  margin: 20px 0;
}

.password-actions {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin: 20px 0;
  flex-wrap: wrap;
  gap: 15px;
}

.forgot-password a,
.back-to-email a {
  color: var(--primary-medium-blue);
  text-decoration: none;
  font-size: 0.95rem;
  font-weight: 500;
  transition: all 0.3s ease;
  padding: 8px 16px;
  border-radius: 8px;
}

.forgot-password a:hover,
.back-to-email a:hover {
  background-color: rgba(76, 104, 192, 0.1);
  color: var(--primary-dark-blue);
}

/* رسائل الخطأ والنجاح */
.error-message {
  color: var(--error-700);
  margin: 20px 0;
  padding: 16px 20px;
  background: var(--error-50);
  border: 1px solid var(--error-200);
  border-radius: 12px;
  font-size: 0.95rem;
  text-align: center;
  box-shadow: 0 2px 8px rgba(185, 28, 28, 0.1);
}

.success-message {
  color: var(--success-700);
  margin: 20px 0;
  padding: 16px 20px;
  background: var(--success-50);
  border: 1px solid var(--success-200);
  border-radius: 12px;
  font-size: 0.95rem;
  text-align: center;
  box-shadow: 0 2px 8px rgba(21, 128, 61, 0.1);
}

/* تصميم متجاوب */
@media (max-width: 768px) {
  .login-container {
    padding: 15px;
  }

  .legal-agenda-logo {
    width: 100px;
  }

  .header-form .title {
    font-size: 2rem;
  }

  .header-form .subtitle {
    font-size: 1rem;
  }

  #login-form {
    max-width: 100%;
  }

  #login-form input,
  #login-form button {
    padding: 16px 20px;
    font-size: 1rem;
    min-height: 55px;
  }

  .password-actions {
    flex-direction: column;
    gap: 10px;
    text-align: center;
  }
}

@media (max-width: 480px) {
  .login-container {
    padding: 10px;
  }

  .legal-agenda-logo {
    width: 250px;
  }

  .header-form .title {
    font-size: 1.8rem;
  }

  .header-form .subtitle {
    font-size: 0.9rem;
  }

  #login-form input,
  #login-form button {
    padding: 14px 18px;
    font-size: 16px; /* يمنع التكبير في iOS */
    min-height: 50px;
  }

  .forgot-password a,
  .back-to-email a {
    font-size: 0.9rem;
    padding: 6px 12px;
  }

  .error-message,
  .success-message {
    padding: 12px 16px;
    font-size: 0.9rem;
  }
}