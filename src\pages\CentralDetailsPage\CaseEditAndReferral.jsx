import React, { useState, useEffect } from 'react';
import { useQueryClient, useMutation } from '@tanstack/react-query';
import { useNavigate } from 'react-router-dom';
import { FaExchangeAlt, FaTimes, FaGavel, FaUsers } from 'react-icons/fa';
import { updateCase, getActiveAccount } from '../../services/StorageService';

const CaseEditAndReferral = ({ caseData, currentUser, onCaseDataUpdate, onJudgmentDetected, onCaseDelete, caseCategoryOptions, courtLocations, caseDegrees, styles, children }) => {
  const navigate = useNavigate();
  const queryClient = useQueryClient();
  const [editingField, setEditingField] = useState(null);
  const [editValue, setEditValue] = useState('');
  const [customValue, setCustomValue] = useState('');
  const [error, setError] = useState('');
  const [saving, setSaving] = useState(false);
  const [showCaseNumberFields, setShowCaseNumberFields] = useState(false);
  const [caseNumberValue, setCaseNumberValue] = useState('');
  const [caseYearValue, setCaseYearValue] = useState('');
  const [showTransferConfirmation, setShowTransferConfirmation] = useState(false);
  const [showCourtReferralModal, setShowCourtReferralModal] = useState(false);
  const [showStatusTransferConfirmation, setShowStatusTransferConfirmation] = useState(false);
  const [selectedNewStatus, setSelectedNewStatus] = useState('');
  const [lawsuitDate, setLawsuitDate] = useState('');
  const [reportDate, setReportDate] = useState('');
  const [referralJudgmentDate, setReferralJudgmentDate] = useState('');
  const [newCourtLocation, setNewCourtLocation] = useState('');
  const [referralDateError, setReferralDateError] = useState('');
  const [showExpertReferralModal, setShowExpertReferralModal] = useState(false);
  const [expertReferralJudgmentDate, setExpertReferralJudgmentDate] = useState('');
  const [expertLocation, setExpertLocation] = useState('');
  const [expertReferralDateError, setExpertReferralDateError] = useState('');
  const [showEditModal, setShowEditModal] = useState(false);
  const [showDeleteConfirmation, setShowDeleteConfirmation] = useState(false);

  // Mutation for updating case data
  const mutation = useMutation({
    mutationFn: async ({ caseId, updateData }) => {
      console.log('🔄 بدء عملية تحديث البيانات من CaseEditAndReferral...');
      console.log('🆔 معرف القضية:', caseId);
      console.log('📄 بيانات التحديث:', updateData);
      console.log('👤 المستخدم الحالي:', currentUser?.uid);

      // التحقق من حالة المصادقة للحساب الأونلاين
      const activeAccount = getActiveAccount();
      if (activeAccount === 'online') {
        console.log('🔍 التحقق من حالة المصادقة للحساب الأونلاين...');

        if (!currentUser) {
          throw new Error('المستخدم غير مصادق عليه');
        }

        // التحقق من صحة التوكن
        try {
          const token = await currentUser.getIdToken(true); // force refresh
          console.log('🎫 تم تحديث التوكن بنجاح');
        } catch (tokenError) {
          console.error('❌ خطأ في الحصول على التوكن:', tokenError);
          throw new Error('خطأ في المصادقة. يرجى تسجيل الدخول مرة أخرى.');
        }
      }

      await updateCase(currentUser.uid, caseId, updateData);
    },
    onSuccess: (_, { caseId, updateData }) => {
      queryClient.setQueryData(['case', caseId], (oldData) => ({ ...oldData, ...updateData }));
      if (onCaseDataUpdate) {
        onCaseDataUpdate(updateData);
      }
      setEditingField(null);
      setEditValue('');
      setCustomValue('');
      setError('');
      setShowEditModal(false);
      const activeAccount = getActiveAccount();
      alert(`تم حفظ التعديل بنجاح في الحساب ${activeAccount === 'online' ? 'الأونلاين' : 'المحلي'}!`);
    },
    onError: (error) => {
      console.error('❌ خطأ في حفظ التعديل:', error);

      let errorMessage = 'حدث خطأ أثناء حفظ التعديل: ' + error.message;
      const activeAccount = getActiveAccount();

      // تحليل نوع الخطأ وتقديم رسائل واضحة
      if (activeAccount === 'online') {
        if (error.code === 'permission-denied') {
          errorMessage = 'ليس لديك صلاحية لتحديث هذه البيانات. تحقق من حالة حسابك.';
        } else if (error.code === 'unauthenticated') {
          errorMessage = 'انتهت جلسة المصادقة. يرجى تسجيل الدخول مرة أخرى.';
          setTimeout(() => window.location.href = '/login', 2000);
        } else if (error.code === 'not-found') {
          errorMessage = 'القضية غير موجودة أو تم حذفها.';
        } else if (error.code === 'network-request-failed' || error.message.includes('network')) {
          errorMessage = 'خطأ في الاتصال بالإنترنت. تحقق من اتصالك وحاول مرة أخرى.';
        } else if (error.message.includes('Firebase') || error.message.includes('Firestore')) {
          errorMessage = 'خطأ في الخدمة السحابية. يرجى المحاولة مرة أخرى لاحقاً.';
        }
      }

      setError(errorMessage);
    },
    onSettled: () => {
      setSaving(false);
    },
  });

  // Handle edit button click
  const handleEditClick = (field, currentValue) => {
    if (field.field === 'caseDegree' && canTransferDegree()) {
      handleTransferDegree();
      return;
    }
    if (field.isExpertReferral) {
      handleExpertReferral();
      return;
    }
    if (field.isCourtReferral) {
      handleCourtReferral();
      return;
    }
    setEditingField(field);
    setEditValue(currentValue === 'غير محدد' || currentValue === '—' ? '' : currentValue || '');
    setCustomValue('');
    setError('');
    setShowEditModal(true);
    if (field.isFullCaseNumber && caseData.caseStatus === 'دعوى قضائية') {
      setShowCaseNumberFields(true);
      setCaseNumberValue(caseData.caseNumber || '');
      setCaseYearValue(caseData.caseYear || '');
    } else if (field.isFullCaseNumber && caseData.caseStatus === 'محضر') {
      setShowCaseNumberFields(true);
      setCaseNumberValue(caseData.reportNumber || '');
      setCaseYearValue(caseData.caseYear || '');
    }
  };

  // Handle save edit
  const handleSaveEdit = () => {
    if (!editingField || !caseData || !currentUser) {
      setError('لا يمكن حفظ التعديل حالياً. يرجى تحديث الصفحة.');
      return;
    }
    if (editingField.isFullCaseNumber && caseData.caseStatus === 'دعوى قضائية') {
      if (!caseNumberValue.trim()) {
        setError('رقم القضية مطلوب');
        return;
      }
      if (!caseYearValue.trim()) {
        setError('السنة القضائية مطلوبة');
        return;
      }
      if (!/^\d+$/.test(caseNumberValue.trim())) {
        setError('رقم القضية يجب أن يحتوي على أرقام فقط');
        return;
      }
      if (!/^\d{4}$/.test(caseYearValue.trim())) {
        setError('السنة القضائية يجب أن تتكون من 4 أرقام فقط');
        return;
      }
      setError('');
      setSaving(true);
      const fullCaseNumber = `${caseNumberValue.trim()}/${caseYearValue.trim()}`;
      const updateData = {
        fullCaseNumber,
        caseNumber: caseNumberValue.trim(),
        caseYear: caseYearValue.trim(),
      };
      mutation.mutate({ caseId: caseData.id, updateData });
      setShowCaseNumberFields(false);
      return;
    }
    if (editingField.isFullCaseNumber && caseData.caseStatus === 'محضر') {
      if (!caseNumberValue.trim()) {
        setError('رقم المحضر مطلوب');
        return;
      }
      if (!caseYearValue.trim()) {
        setError('سنة المحضر مطلوبة');
        return;
      }
      if (!/^\d+$/.test(caseNumberValue.trim())) {
        setError('رقم المحضر يجب أن يحتوي على أرقام فقط');
        return;
      }
      if (!/^\d{4}$/.test(caseYearValue.trim())) {
        setError('سنة المحضر يجب أن تتكون من 4 أرقام فقط');
        return;
      }
      setError('');
      setSaving(true);
      const fullCaseNumber = `${caseNumberValue.trim()}/${caseYearValue.trim()}`;
      const updateData = {
        fullCaseNumber,
        reportNumber: caseNumberValue.trim(),
        caseYear: caseYearValue.trim(),
      };
      mutation.mutate({ caseId: caseData.id, updateData });
      setShowCaseNumberFields(false);
      return;
    }
    if (editingField.required && !editValue.trim() && editingField.type !== 'select') {
      setError(`${editingField.label} مطلوب`);
      return;
    }
    let finalValue = editValue.trim();
    if (editingField.type === 'select') {
      if (editValue === 'أخرى') {
        if (!customValue.trim()) {
          setError('يرجى إدخال قيمة مخصصة لـ "أخرى"');
          return;
        }
        finalValue = customValue.trim();
      }
      if (editingField.required && !finalValue) {
        setError(`${editingField.label} مطلوب`);
        return;
      }
      if (editingField.field === 'referredToExpert') {
        finalValue = finalValue === 'نعم';
      }
    }
    setError('');
    setSaving(true);
    const updateData = { [editingField.field]: finalValue };
    mutation.mutate({ caseId: caseData.id, updateData });
  };

  // Handle cancel edit
  const handleCancelEdit = () => {
    setEditingField(null);
    setEditValue('');
    setCustomValue('');
    setError('');
    setShowCaseNumberFields(false);
    setCaseNumberValue('');
    setCaseYearValue('');
    setShowEditModal(false);
  };

  // Handle degree transfer
  const handleTransferDegree = () => {
    setShowTransferConfirmation(true);
  };

  const handleTransferConfirm = async () => {
    try {
      const updatedCaseData = {
        ...caseData,
        isHidden: true,
        hiddenAt: new Date().toISOString(),
        updatedAt: new Date().toISOString(),
      };
      await updateCase(currentUser.uid, caseData.id, updatedCaseData);
      const transferData = {
        clientName: caseData.clientName,
        caseDescription: caseData.caseDescription,
        opponentName: caseData.opponentName,
        caseCategory: caseData.caseCategory,
        courtLocation: caseData.courtLocation,
        originalCaseId: caseData.id,
        originalCaseDegree: caseData.caseDegree,
        originalCaseNumber: caseData.fullCaseNumber,
        originalCourtLocation: caseData.courtLocation,
        originalCaseDate: caseData.caseDate,
        originalClientName: caseData.clientName,
        originalOpponentName: caseData.opponentName,
        originalCaseDescription: caseData.caseDescription,
        originalCaseCategory: caseData.caseCategory,
        originalCircleNumber: caseData.circleNumber,
      };
      localStorage.setItem('transferData', JSON.stringify(transferData));
      navigate('/cases');
    } catch (error) {
      alert('خطأ في تحويل الدرجة: ' + error.message);
    }
  };

  const handleTransferCancel = () => {
    setShowTransferConfirmation(false);
  };

  // Handle status transfer
  const handleStatusTransfer = () => {
    setShowStatusTransferConfirmation(true);
    setSelectedNewStatus('');
    setLawsuitDate('');
    setReportDate('');
  };

  const handleStatusSelection = (newStatus) => {
    setSelectedNewStatus(newStatus);
  };

  const handleStatusTransferConfirm = async () => {
    try {
      if (!selectedNewStatus) {
        alert('يرجى اختيار الحالة الجديدة');
        return;
      }
      if (selectedNewStatus === 'دعوى قضائية' && !lawsuitDate) {
        alert('يرجى إدخال تاريخ رفع الدعوى');
        return;
      }
      if (selectedNewStatus === 'محضر' && !reportDate) {
        alert('يرجى إدخال تاريخ كتابة المحضر');
        return;
      }
      let updatedCaseData = {
        ...caseData,
        caseStatus: selectedNewStatus,
        updatedAt: new Date().toISOString(),
      };
      if (selectedNewStatus === 'محضر') {
        updatedCaseData = {
          ...updatedCaseData,
          fullCaseNumber: `محضر-${Date.now()}`,
          caseDate: reportDate,
          caseNumber: null,
          caseYear: null,
          circleNumber: null,
          caseDegree: null,
          caseCategory: null,
          courtLocation: null,
        };
      } else if (selectedNewStatus === 'دعوى قضائية') {
        updatedCaseData = {
          ...updatedCaseData,
          fullCaseNumber: `دعوى-${Date.now()}`,
          caseDate: lawsuitDate,
        };
      }
      await updateCase(currentUser.uid, caseData.id, updatedCaseData);
      if (onCaseDataUpdate) {
        onCaseDataUpdate(updatedCaseData);
      }
      alert('تم تحويل حالة الملف بنجاح');
      setShowStatusTransferConfirmation(false);
      setSelectedNewStatus('');
      setLawsuitDate('');
      setReportDate('');
    } catch (error) {
      alert('خطأ في تحويل الحالة: ' + error.message);
    }
  };

  const handleStatusTransferCancel = () => {
    setShowStatusTransferConfirmation(false);
    setSelectedNewStatus('');
    setLawsuitDate('');
    setReportDate('');
  };

  // Handle court referral
  const handleCourtReferral = () => {
    if (!hasCourtReferralJudgment(caseData)) {
      alert('لا يمكن تنفيذ الإحالة للمحكمة. لا يوجد حكم بالإحالة للمحكمة مسجل في الأرشيف الزمني.');
      return;
    }
    setShowCourtReferralModal(true);
    setReferralJudgmentDate('');
    setNewCourtLocation('');
    setReferralDateError('');
  };

  const handleCourtReferralConfirm = async () => {
    setReferralDateError('');
    if (!newCourtLocation) {
      setReferralDateError('يرجى اختيار المحكمة الجديدة');
      return;
    }
    try {
      const currentDate = new Date().toLocaleDateString('ar-EG');
      const timelineEntry = {
        date: currentDate,
        description: `تم إحالة القضية للمحكمة ${newCourtLocation} بتاريخ ${currentDate}`,
        type: 'court_transfer',
        timestamp: new Date().toISOString(),
      };
      const updatedCaseData = {
        ...caseData,
        courtLocation: newCourtLocation,
        timeline: [...(caseData.timeline || []), timelineEntry],
        courtReferral: {
          ...caseData?.courtReferral,
          transferDate: new Date().toISOString(),
          newCourtLocation,
          previousCourtLocation: caseData.courtLocation,
          confirmationCompleted: true,
          confirmationDate: new Date().toISOString(),
        },
        updatedAt: new Date().toISOString(),
      };
      await updateCase(currentUser.uid, caseData.id, updatedCaseData);
      if (onCaseDataUpdate) {
        onCaseDataUpdate(updatedCaseData);
      }
      setShowCourtReferralModal(false);
      alert('تم إحالة القضية للمحكمة الجديدة وتحديث الأرشيف الزمني بنجاح');
    } catch (error) {
      setReferralDateError('خطأ في حفظ البيانات: ' + error.message);
    }
  };

  const handleCourtReferralCancel = () => {
    setShowCourtReferralModal(false);
    setReferralJudgmentDate('');
    setNewCourtLocation('');
    setReferralDateError('');
  };

  // Handle expert referral
  const handleExpertReferral = () => {
    if (!hasExpertReferralJudgment(caseData)) {
      alert('لا يمكن تنفيذ الإحالة للخبراء. لا يوجد حكم بالإحالة للخبراء مسجل في الأرشيف الزمني.');
      return;
    }
    setShowExpertReferralModal(true);
    setExpertReferralJudgmentDate('');
    setExpertReferralDateError('');
  };

  const handleExpertReferralConfirm = async () => {
    setExpertReferralDateError('');
    if (!expertLocation.trim()) {
      setExpertReferralDateError('يرجى كتابة مكان/اسم الخبراء');
      return;
    }
    try {
      const currentDate = new Date().toLocaleDateString('ar-EG');
      const timelineEntry = {
        date: currentDate,
        description: `تم إحالة القضية للخبراء - ${expertLocation} بتاريخ ${currentDate}`,
        type: 'expert_referral',
        timestamp: new Date().toISOString(),
      };
      const updatedCaseData = {
        ...caseData,
        referredToExpert: true,
        expertReferralStatus: `تم الإحالة للخبراء ${expertLocation}`,
        timeline: [...(caseData.timeline || []), timelineEntry],
        updatedAt: new Date().toISOString(),
      };
      await updateCase(currentUser.uid, caseData.id, updatedCaseData);
      if (onCaseDataUpdate) {
        onCaseDataUpdate(updatedCaseData);
      }
      setShowExpertReferralModal(false);
      alert('تم إحالة القضية للخبراء وتحديث الأرشيف الزمني بنجاح');
    } catch (error) {
      setExpertReferralDateError('خطأ في حفظ البيانات: ' + error.message);
    }
  };

  const handleExpertReferralCancel = () => {
    setShowExpertReferralModal(false);
    setExpertReferralJudgmentDate('');
    setExpertReferralDateError('');
  };

  // Handle case deletion
  const handleDeleteClick = () => {
    setShowDeleteConfirmation(true);
  };

  const handleDeleteConfirm = async () => {
    if (!caseData || !currentUser) {
      alert('خطأ: لا يمكن حذف القضية حالياً');
      return;
    }

    try {
      console.log('🗑️ بدء عملية حذف القضية:', caseData.id);

      if (onCaseDelete) {
        await onCaseDelete(caseData.id);
        console.log('✅ تم حذف القضية بنجاح');
        alert('تم حذف القضية بنجاح');
        navigate('/dashboard'); // العودة للوحة التحكم بعد الحذف
      } else {
        console.error('❌ دالة الحذف غير متوفرة');
        alert('خطأ: دالة الحذف غير متوفرة');
      }
    } catch (error) {
      console.error('❌ خطأ في حذف القضية:', error);
      alert('حدث خطأ أثناء حذف القضية: ' + error.message);
    } finally {
      setShowDeleteConfirmation(false);
    }
  };

  const handleDeleteCancel = () => {
    setShowDeleteConfirmation(false);
  };

  // Check if degree transfer is allowed
  const canTransferDegree = () => {
    return caseData?.caseStatus === 'دعوى قضائية' && !caseData?.isHidden;
  };

  // Check if status transfer is allowed
  const canTransferStatus = () => {
    return caseData?.caseStatus && caseData?.caseStatus !== 'غير محدد' && caseData?.caseStatus !== 'دعوى قضائية' && !caseData?.isHidden;
  };

  // Get status transfer question
  const getStatusTransferQuestion = () => {
    switch (caseData?.caseStatus) {
      case 'قيد النظر':
        return 'هل تريد عمل محضر أم رفع دعوى قضائية؟';
      case 'محضر':
        return 'هل تريد رفع دعوى قضائية؟';
      default:
        return 'تحويل حالة الملف';
    }
  };

  // Check for court referral judgment
  const hasCourtReferralJudgment = (caseData) => {
    if (!caseData || !caseData.timeline) return false;
    return caseData.timeline.some((event) => event.type === 'judgment_verdict' && event.verdict === 'الإحالة لمحكمة أخرى');
  };

  // Check for expert referral judgment
  const hasExpertReferralJudgment = (caseData) => {
    if (!caseData || !caseData.timeline) return false;
    return caseData.timeline.some((event) => event.type === 'judgment_verdict' && event.verdict === 'الإحالة للخبراء');
  };

  // Render edit field (now returns null as it will be handled in modal)
  const renderEditField = () => {
    return null;
  };

  return children({
    editingField,
    editValue,
    customValue,
    error,
    saving,
    showCaseNumberFields,
    caseNumberValue,
    caseYearValue,
    showTransferConfirmation,
    showCourtReferralModal,
    showStatusTransferConfirmation,
    selectedNewStatus,
    lawsuitDate,
    reportDate,
    referralJudgmentDate,
    newCourtLocation,
    referralDateError,
    showExpertReferralModal,
    expertReferralJudgmentDate,
    expertLocation,
    expertReferralDateError,
    showEditModal,
    showDeleteConfirmation,
    handleEditClick,
    handleSaveEdit,
    handleCancelEdit,
    handleDeleteClick,
    handleDeleteConfirm,
    handleDeleteCancel,
    handleTransferConfirm,
    handleTransferCancel,
    handleStatusSelection,
    handleStatusTransfer,
    handleStatusTransferConfirm,
    handleStatusTransferCancel,
    handleCourtReferral,
    handleCourtReferralConfirm,
    handleCourtReferralCancel,
    handleExpertReferral,
    handleExpertReferralConfirm,
    handleExpertReferralCancel,
    setEditValue,
    setCustomValue,
    setCaseNumberValue,
    setCaseYearValue,
    setNewCourtLocation,
    setExpertLocation,
    setLawsuitDate,
    setReportDate,
    setShowEditModal,
    canTransferStatus,
    getStatusTransferQuestion,
    renderEditField,
  });
};

export default CaseEditAndReferral;