/* متغيرات CSS مخصصة - ثيم أزرق مهني */
@import '../../styles/variables.css';


/* التخطيط العام للصفحة */
.pageWrapper {
  min-height: 100vh;
  display: flex;
  flex-direction: column;
  font-family: var(--font-family-primary);
  color: var(--neutral-700);
}

.mainContainer {
  flex: 1;
  max-width: 1400px;
  margin: 0 auto;
  padding: 1.5rem;
  width: 100%;
  display: flex;
  flex-direction: column;
  gap: 1.5rem;
}

/* === لوحة معلومات الهيدر === */
.headerInfoContainer {
  display: flex;
  flex-direction: column;
  gap: 1rem;
  padding: 1rem;
  background: var(--neutral-50);
  border-radius: 16px;
  border: 1px solid var(--neutral-200);
  box-shadow: var(--current-shadow-light);
}

.infoRow {
  display: flex;
  align-items: stretch;
  justify-content: center;
  gap: 1rem;
}

.infoBlock {
  flex: 1;
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  gap: 0.25rem;
  padding: 0.75rem;
  border-radius: 12px;
  background: white;
  border: 1px solid transparent;
  cursor: pointer;
  transition: all 0.2s ease;
  text-align: center;
  min-width: 180px;
}

.infoBlock:hover {
  transform: translateY(-2px);
  border-color: var(--primary-color);
  box-shadow: var(--current-shadow-medium);
}

.infoLabel {
  font-size: 0.9rem;
  font-weight: 500;
  color: var(--neutral-500);
}

.infoValue {
  font-size: 1.5rem;
  font-weight: 600;
  color: var(--primary-dark-blue);
  line-height: 1.3;
  word-break: break-word;
}

.infoValueDescription {
  font-size: 1.1rem;
  font-weight: 600;
  color: var(--neutral-700);
  line-height: 1.6;
  font-style: italic;
  white-space: pre-wrap;
}

.versusSeparator {
  display: flex;
  align-items: center;
  justify-content: center;
  flex-grow: 0;
  flex-shrink: 0;
}
.versusText {
  font-size: 1.2rem;
  font-weight: 600;
  color: var(--error-700);
  padding: 0 1rem;
}



/* === أنماط التوزيع الجديد بجانب الوصف === */


.descriptionBlock {
  flex-grow: 2;
  flex-basis: 0;
  align-items: flex-start;
  text-align: right;
}

.sideInfoContainer {
  flex-grow: 1;
  flex-basis: 0;
  display: flex;
  flex-direction: column;
  gap: 1rem;
}

/* الوظائف الموسعة */
.expandedFunctionsContainer {
  width: 100%;
  display: flex;
  flex-direction: column;
  gap: 1rem;
  margin-top: 1rem;
}

.transferDegreeSection {
  margin-bottom: 1rem;
}

.transferDegreeBox {
  background: var(--current-bg-primary);
  border-radius: 12px;
  border: 1px solid var(--current-border-primary);
  box-shadow: var(--current-shadow-medium);
  overflow: hidden;
}

.transferHeader {
  padding: 1rem;
  background: var(--current-bg-secondary);
  border-bottom: 1px solid var(--current-border-primary);
  display: flex;
  align-items: center;
  justify-content: space-between;
}

.transferTitleSection {
  display: flex;
  align-items: center;
  gap: 0.5rem;
}

.transferIcon {
  color: var(--primary-color);
  font-size: 1.2rem;
}

.transferTitle {
  font-weight: 600;
  color: var(--current-text-primary);
}

.transferContent {
  padding: 1rem;
}

.transferInfoRow {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 0.5rem;
}

.transferLabel {
  font-weight: 600;
  color: var(--current-text-secondary);
}

.transferValue {
  color: var(--current-text-primary);
}

.transferButton {
  background: var(--primary-color);
  color: white;
  border: none;
  padding: 0.5rem 1rem;
  border-radius: 6px;
  cursor: pointer;
  font-weight: 600;
  transition: all 0.2s ease;
  margin-top: 0.5rem;
  width: 100%;
}

.transferButton:hover {
  background: var(--primary-color-dark);
  transform: translateY(-1px);
}

/* النوافذ المنبثقة */
.transferConfirmationOverlay {
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background-color: rgba(0, 0, 0, 0.6);
  display: flex;
  justify-content: center;
  align-items: center;
  z-index: 2000;
  padding: 1rem;
}

.transferConfirmationDialog {
  background: white;
  border-radius: 16px;
  padding: 2rem;
  max-width: 500px;
  width: 100%;
  box-shadow: 0 10px 30px rgba(0, 0, 0, 0.2);
  animation: fadeIn 0.3s ease;
}

@keyframes fadeIn {
  from { opacity: 0; transform: scale(0.95); }
  to { opacity: 1; transform: scale(1); }
}

.transferConfirmationIcon {
  text-align: center;
  margin-bottom: 1rem;
  font-size: 2.5rem;
  color: var(--primary-color);
}

.transferConfirmationTitle {
  text-align: center;
  margin-bottom: 1rem;
  color: #333;
  font-size: 1.4rem;
  font-weight: 600;
}

.transferConfirmationMessage {
  text-align: center;
  margin-bottom: 2rem;
  color: #666;
  line-height: 1.6;
}

.transferConfirmationActions {
  display: flex;
  gap: 1rem;
  justify-content: center;
}

.transferConfirmButton, .transferCancelButton {
  flex: 1;
  padding: 0.75rem;
  border-radius: 8px;
  cursor: pointer;
  font-size: 1rem;
  font-weight: 600;
  border: none;
  transition: all 0.2s ease;
}

.transferConfirmButton {
  background: var(--primary-color);
  color: white;
}
.transferConfirmButton:hover {
  background: var(--primary-dark);
}

.transferCancelButton {
  background: var(--neutral-200);
  color: var(--neutral-700);
}
.transferCancelButton:hover {
  background: var(--neutral-300);
}

.statusOptionsContainer {
  margin-bottom: 1.5rem;
}

/* أنماط حقول التعديل داخل النافذة المنبثقة */
.editInput {
  width: 100%;
  padding: 0.75rem 1rem;
  border: 1px solid var(--neutral-300);
  border-radius: 8px;
  font-size: 1rem;
  transition: all 0.2s ease;
}
.editInput:focus {
  outline: none;
  border-color: var(--primary-color);
  box-shadow: 0 0 0 3px rgba(59, 130, 246, 0.2);
}
.textareaInput {
  resize: vertical;
}
.inputError {
  border-color: var(--danger-color);
}
.errorText {
  color: var(--danger-color);
  font-size: 0.875rem;
  margin-top: 0.5rem;
  text-align: center;
}

/* أنماط حقول رقم القضية المركب */
.fullCaseNumberEditContainer {
  width: 100%;
}
.caseNumberFieldsContainer {
  display: flex;
  gap: 1rem;
}
.caseNumberField {
  flex: 1;
  display: flex;
  flex-direction: column;
  gap: 0.5rem;
}
.inlineLabel {
  font-weight: 500;
  color: var(--neutral-700);
  font-size: 0.9rem;
  text-align: right;
}

/* === Color Themes for Info Blocks === */
.theme1 .infoValue, .theme1 .infoValueDescription { color: var(--neutral-900) !important; }
.theme1:hover { border-color: var(--neutral-900) !important; }

.theme2 .infoValue, .theme2 .infoValueDescription { color: var(--theme-fg-2) !important; }
.theme2:hover { border-color: var(--theme-fg-2) !important; }

.theme3 .infoValue, .theme3 .infoValueDescription { color: var(--primary-medium-Violet) !important; }
.theme3:hover { border-color: var(--primary-dark-Violet) !important; }

.theme4 .infoValue, .theme4 .infoValueDescription { color: var(--secondary-color) !important; }
.theme4:hover { border-color: var(--secondary-color) !important; }

.theme5 .infoValue, .theme5 .infoValueDescription { color: var(--theme-fg-5) !important; }
.theme5:hover { border-color: var(--theme-fg-5) !important; }

.theme6 .infoValue, .theme6 .infoValueDescription { color: var( --primary-medium-Violet) !important; }
.theme6:hover { border-color: var( --primary-medium-Violet) !important; }


/* === أنماط الاستجابة للشاشات الصغيرة (تصغير المحتوى) === */
@media (max-width: 992px) {
  .infoBlock {
    min-width: 0; /* Allow blocks to shrink */
    padding: 0.5rem;
    gap: 0.15rem;
  }

  .infoValue {
    font-size: 1.1rem; /* Smaller font for the main value */
  }

  .infoValueDescription {
    font-size: 0.9rem; /* Smaller font for the description */
  }

  .infoLabel {
    font-size: 0.8rem; /* Smaller font for the label */
  }
  
  .versusText {
    font-size: 1rem;
    padding: 0 0.5rem;
  }
}

@media (max-width: 768px) {
  .infoValue {
    font-size: 0.9rem;
  }

  .infoValueDescription {
    font-size: 0.8rem;
  }

  .infoLabel {
    font-size: 0.7rem;
  }
}
/* === أنماط إجراء الحذف === */
.deleteActionContainer {
  margin-top: 1.5rem;
  padding-top: 1rem;
  border-top: 1px solid var(--neutral-200);
  text-align: center;
}

.deleteButton {
  background: transparent;
  color: var(--danger-color);
  border: 1px solid var(--danger-color);
  padding: 0.5rem 1rem;
  border-radius: 6px;
  cursor: pointer;
  font-weight: 600;
  transition: all 0.2s ease;
  display: inline-flex;
  align-items: center;
  gap: 0.5rem;
}

.deleteButton:hover {
  background: var(--danger-light);
  color: white;
}

.deleteButton:disabled {
  opacity: 0.5;
  cursor: not-allowed;
}

.deleteConfirmButton {
  flex: 1;
  padding: 0.75rem;
  border-radius: 8px;
  cursor: pointer;
  font-size: 1rem;
  font-weight: 600;
  border: none;
  transition: all 0.2s ease;
  background: var(--danger-color);
  color: white;
  display: inline-flex;
  align-items: center;
  justify-content: center;
  gap: 0.5rem;
}

.deleteConfirmButton:hover {
  background: var(--danger-dark);
}
