import{D as e,F as t,G as s,H as r,I as n,J as o,K as l,M as i,O as c,P as m,Q as _,T as d,U as u,V as g,W as y,X as D,Y as p,Z as h,$ as A,a0 as I,a1 as S,a2 as P,a3 as f,a4 as F,a5 as C,a6 as E,a7 as T,a8 as V,a9 as b,aa as w,m as Q,e as R,ab as B,d as W,ac as v,ad as N,g as O,f as U,ae as M,q as x,t as J,u as K,w as j,h as q}from"./index-HUlFBKIW.js";const G=Object.freeze(Object.defineProperty({__proto__:null,AbstractUserDataWriter:e,Bytes:t,CACHE_SIZE_UNLIMITED:s,CollectionReference:r,DocumentReference:n,DocumentSnapshot:o,FieldPath:l,FieldValue:i,Firestore:c,FirestoreError:m,GeoPoint:_,Query:d,QueryCompositeFilterConstraint:u,QueryConstraint:g,QueryDocumentSnapshot:y,QueryFieldFilterConstraint:D,QuerySnapshot:p,SnapshotMetadata:h,Timestamp:A,VectorValue:I,WriteBatch:S,_AutoId:P,_ByteString:f,_DatabaseId:F,_DocumentKey:C,_EmptyAuthCredentialsProvider:E,_FieldPath:T,_cast:V,_logWarn:b,_validateIsNotUsedTogether:w,addDoc:Q,collection:R,deleteDoc:B,doc:W,ensureFirestoreConfigured:v,executeWrite:N,getDoc:O,getDocs:U,initializeFirestore:M,query:x,setDoc:J,updateDoc:K,where:j,writeBatch:q},Symbol.toStringTag,{value:"Module"})),H=["ابتدائي","استئنافي","نقض","أخرى"],L={ابتدائي:Array.from(new Set(["مدني","تجاري","عمالي","أحوال شخصية","إيجارات","جنح","جنايات","أخرى"])),استئنافي:Array.from(new Set(["مدني","تجاري","عمالي","أحوال شخصية","إيجارات","جنح","جنايات","أخرى"])),نقض:Array.from(new Set(["نقض مدني","نقض جنائي","أخرى"])),أخرى:["أخرى"]},Z=Array.from(new Set([{name:"محكمة القاهرة الجديدة",lat:30.0265,lng:31.4915},{name:"محكمة الجيزة",lat:30.0131,lng:31.2089},{name:"محكمة الإسكندرية",lat:31.2001,lng:29.9187},{name:"محكمة المنصورة",lat:31.0409,lng:31.3785},{name:"محكمة طنطا",lat:30.7845,lng:31.0015},{name:"محكمة أسيوط",lat:27.1809,lng:31.1837},{name:"محكمة الزقازيق",lat:30.5877,lng:31.502},{name:"محكمة بنها",lat:30.4659,lng:31.183},{name:"محكمة دمنهور",lat:31.0341,lng:30.4692},{name:"محكمة سوهاج",lat:26.5571,lng:31.6948},{name:"أخرى",lat:null,lng:null}].map(a=>JSON.stringify(a)))).map(a=>JSON.parse(a));export{L as a,H as b,Z as c,G as i};
