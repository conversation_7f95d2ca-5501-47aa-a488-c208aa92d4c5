/* Modern Case Registration Styles */
@import '../styles/variables.css';

/* Main Layout */
.case-registration-page {
  min-height: 100vh;
  font-family: 'Inter', -apple-system, BlinkMacSystemFont, system-ui, sans-serif;
  direction: rtl;
  color: var(--text-primary);
}

.main-container {
  display: flex;
  flex-direction: column;
  gap: 16px;
}

/* Registration Card */
.registration-card {
  width: 85%;
  max-width: 1200px;
  min-height: calc(100vh - 160px);
  margin: 40px auto;
  background: var(--page-background);
  border-radius: 15px;
  box-shadow: 
    0 20px 60px var(--shadow-medium),
    0 10px 30px var(--shadow-light);
  border: none;
  transition: all var(--transition-normal);
  overflow: hidden;
  display: flex;
  flex-direction: column;
  position: relative;
}

.registration-card:hover {
  box-shadow: 
    0 25px 70px var(--shadow-medium),
    0 15px 40px var(--shadow-light);
  transform: translateY(-4px);
}

/* Header */
.card-header {
  margin-bottom: 16px;
  text-align: right;
  padding: 24px 32px 0 32px;
}

.header-content h1 {
  font-weight: 700;
  text-align: right;
  line-height: 1.2;
  background: linear-gradient(135deg, var(--primary-dark-blue) 0%, var(--primary-medium-blue) 100%);
  -webkit-background-clip: text;
  -webkit-text-fill-color: transparent;
  background-clip: text;
}

.transfer-badge {
  background: rgba(255, 255, 255, 0.2);
  border: 1px solid rgba(255, 255, 255, 0.3);
  border-radius: 50px;
  padding: 0.5rem 1rem;
  margin-top: 1rem;
  display: inline-block;
}

.transfer-badge span {
  font-size: 0.9rem;
  font-weight: 500;
}

/* Messages */
.message {
  display: flex;
  align-items: center;
  gap: 0.75rem;
  padding: 1rem 1.5rem;
  margin: 1rem 2rem;
  border-radius: var(--border-radius);
  font-weight: 500;
  transition: var(--transition);
}

.message-icon {
  font-size: 1.25rem;
  flex-shrink: 0;
}

.message-text {
  flex: 1;
}

.error-message {
  background: var(--error-light);
  color: var(--error-color);
  border: 1px solid #fecaca;
}

.success-message {
  background: var(--success-light);
  color: var(--success-color);
  border: 1px solid #a7f3d0;
}

.loading-message {
  background: var(--primary-light);
  color: var(--primary-color);
  border: 1px solid #bfdbfe;
}

.loading-spinner {
  width: 1.25rem;
  height: 1.25rem;
  border: 2px solid var(--primary-light);
  border-top: 2px solid var(--primary-color);
  border-radius: 50%;
  animation: var(--loading-animation);
}

/* Form */
.registration-form {
  display: flex;
  flex-direction: column;
  gap: 24px;
  padding: 32px;
  width: 100%;
  flex: 1;
}

.form-body {
  display: flex;
  flex-direction: column;
  gap: 12px;
  width: 100%;
  align-items: stretch;
}

/* Input Rows and Fields */
.input-row {
  display: flex;
  flex-wrap: wrap;
  gap: 8px;
  margin-bottom: 8px;
}

.input-field {
  flex: 1 1 30%;
  min-width: 220px;
  display: flex;
  flex-direction: column;
  gap: 4px;
  margin-left: 0;
  margin-right: 0;
}

.input-field.full-width {
  flex: 1 1 100%;
  max-width: 100%;
}

.input-field label {
  font-size: 0.875rem;
  font-weight: 600;
  color: var(--text-primary);
  display: flex;
  align-items: center;
  gap: 0.25rem;
}

.label {
  display: block;
  font-size: 0.95rem;
  font-weight: 600;
  color: #4a5568;
  margin-bottom: 4px;
}

/* Input Styles - الثيم الأصلي */
.input {
  width: 100%;
  height: 48px;
  background-color: #ffffff;
  border-radius: 12px;
  padding: 0 16px;
  border: 2px solid #ffffff;
  font-size: 1rem;
  color: #1a202c;
  transition: all 0.2s ease;
  outline: none;
   box-shadow: 
    0 20px 60px var(--shadow-medium),
    0 10px 30px var(--shadow-light);
}

.input:hover {
    border-color: var(--secondary-color);
}

.input:focus {
  border-color: var(--secondary-color);
}
select.input {
  appearance: none;
  background-image: url("data:image/svg+xml,%3Csvg xmlns='http://www.w3.org/2000/svg' fill='none' viewBox='0 0 24 24' stroke='%234A5568'%3E%3Cpath stroke-linecap='round' stroke-linejoin='round' stroke-width='2' d='M19 9l-7 7-7-7'%3E%3C/path%3E%3C/svg%3E");
  background-repeat: no-repeat;
  background-position: left 12px center;
  background-size: 20px;
  padding-left: 40px;
  cursor: pointer;
}

input[type="date"].input {
  padding-right: 12px;
  min-height: 48px;
}

.input-field input:disabled,
.input-field select:disabled {
  background: var(--background);
  color: var(--text-muted);
  cursor: not-allowed;
}

.input-field textarea {
  resize: vertical;
  min-height: 120px;
  line-height: 1.5;
}

textarea.input {
  min-height: 44px;
  max-height: 200px;
  height: 44px;
  resize: vertical;
  padding: 12px 16px;
  font-size: 1rem;
  border-radius: 12px;
  border: 2px solid #e2e8f0;
  background: #fff;
  transition: border 0.2s;
}

/* Case Number Container */
.case-number-container {
  background: #f8fafc;
  border-radius: 16px;
  padding: 24px;
  width: 100%;
  border: 1px solid #e2e8f0;
}

.number-inputs {
  display: flex;
  flex-direction: row;
  align-items: center;
  gap: 8px;
  width: 100%;
  max-width: 500px;
  margin: 0 auto;
}

.number-input,
.year-input {
  padding: 0.75rem 1rem;
  border: 2px solid var(--border-color);
  border-radius: var(--border-radius);
  font-size: 1.125rem;
  text-align: center;
  font-weight: 600;
  background: var(--card-background);
  transition: var(--transition);
  min-width: 120px;
  flex: 1 1 0;
}

.number-input:focus,
.year-input:focus {
  outline: none;
  border-color: var(--secondary-color);
  box-shadow: 0 0 0 3px var(--secondary-color);
}

.separator {
  font-size: 1.5rem;
  font-weight: 600;
  color: var(--text-secondary);
  margin: 0 2px;
  padding: 0;
  flex-shrink: 0;
}

/* Final Buttons */
.final-buttons {
  display: flex;
  flex-direction: row;
  gap: 12px;
  justify-content: flex-end;
  margin-top: 12px;
}

/* Responsive Design */
@media (max-width: 768px) {
  .registration-card {
    margin: 16px;
    width: calc(100% - 32px);
    min-height: auto;
  }

  .registration-form {
    padding: 20px;
  }

  .form-body {
    gap: 20px;
  }

  .number-inputs {
    flex-direction: column;
    gap: 8px;
    max-width: 100%;
  }

  .separator {
    display: none;
  }

  .input {
    height: 52px;
  }

  select.input {
    padding-left: 16px;
    background-position: left 16px center;
  }

  .case-number-container {
    padding: 20px;
  }

  .final-buttons {
    flex-direction: column;
    gap: 16px;
  }

  .submit-btn,
  .cancel-btn {
    width: 100%;
  }

  .input-row {
    flex-direction: column;
    gap: 8px;
  }

  .input-field, .input-field.full-width {
    min-width: 100%;
    max-width: 100%;
    margin-left: 0;
    margin-right: 0;
  }

  .final-buttons {
    flex-direction: column;
    align-items: stretch;
    gap: 10px;
  }

  .submit-btn, .cancel-btn {
    max-width: 100%;
    min-width: 100%;
  }
}

@media (max-width: 768px) {
  .card-header {
    padding: 20px 24px 0 24px;
  }

  .header-content h1 {
    font-size: 2rem;
  }
}

@media (max-width: 480px) {
  .card-header {
    padding: 16px 20px 0 20px;
  }

  .header-content h1 {
    font-size: 1.8rem;
  }
}

/* Print Styles */
@media print {
  .case-registration-page {
    background: white;
  }

  .registration-card {
    box-shadow: none;
    border: 1px solid var(--border-color);
  }
}

/* SweetAlert2 horizontal actions for court modal */
body .swal2-actions, .swal2-actions-horizontal {
  display: flex !important;
  flex-direction: row-reverse !important;
  justify-content: center !important;
  gap: 12px !important;
  flex-wrap: nowrap !important;
}

/* SweetAlert2 Modern Theme */
.swal2-popup {
  border-radius: 20px !important;
  padding: 30px !important;
  background: var(--page-background) !important;
  box-shadow: 
    0 20px 60px var(--shadow-medium),
    0 10px 30px var(--shadow-light) !important;
  direction: rtl !important;
}

.swal2-title {
  font-size: 1.5rem !important;
  font-weight: 700 !important;
  color: var(--text-primary) !important;
  margin-bottom: 20px !important;
  background: linear-gradient(135deg, var(--primary-dark-blue) 0%, var(--primary-medium-blue) 100%) !important;
  -webkit-background-clip: text !important;
  -webkit-text-fill-color: transparent !important;
  background-clip: text !important;
}

.swal2-html-container {
  font-size: 1rem !important;
  color: var(--text-secondary) !important;
  margin-bottom: 25px !important;
  line-height: 1.6 !important;
  text-align: right !important;
}

.swal2-input {
  border: 2px solid var(--border-color) !important;
  border-radius: 12px !important;
  padding: 12px 16px !important;
  font-size: 1rem !important;
  margin-bottom: 20px !important;
  transition: all var(--transition-normal) !important;
  box-shadow: 
    0 5px 15px var(--shadow-light),
    0 2px 5px var(--shadow-medium) !important;
}

.swal2-input:focus {
  border-color: var(--secondary-color) !important;
  box-shadow: 0 0 0 3px rgba(70, 130, 180, 0.2) !important;
}

.swal2-actions {
  gap: 15px !important;
  margin-top: 20px !important;
}

.swal2-confirm, 
.swal2-deny, 
.swal2-cancel {
  border-radius: 12px !important;
  padding: 12px 24px !important;
  font-weight: 600 !important;
  font-size: 1rem !important;
  transition: all var(--transition-normal) !important;
  box-shadow: 
    0 5px 15px var(--shadow-light),
    0 2px 5px var(--shadow-medium) !important;
  margin: 5px;
  box-sizing: border-box !important; /* Include padding in width calculation */
}

.swal2-confirm {
  background: var(--primary-color) !important;
  border: none !important;
}

.swal2-confirm:hover {
  background: var(--primary-medium-Violet) !important;
  transform: translateY(-2px) !important;
}

.swal2-deny {
  background: var(--neutral-200) !important;
  color: var(--text-primary) !important;
  border: none !important;
}

.swal2-deny:hover {
  background: var(--neutral-300) !important;
  transform: translateY(-2px) !important;
}

.swal2-cancel {
  background: #dc3545 !important;
  color: white !important;
  border: none !important;
}

.swal2-cancel:hover {
  background: var(--primary-medium-Violet) !important;
  transform: translateY(-2px) !important;
}

/* تحسينات للقوائم المنسدلة داخل النوافذ */
.swal2-select {
  border: 2px solid var(--border-color) !important;
  border-radius: 12px !important;
  padding: 12px 16px !important;
  font-size: 1rem !important;
  background-image: url("data:image/svg+xml,%3Csvg xmlns='http://www.w3.org/2000/svg' fill='none' viewBox='0 0 24 24' stroke='%234A5568'%3E%3Cpath stroke-linecap='round' stroke-linejoin='round' stroke-width='2' d='M19 9l-7 7-7-7'%3E%3C/path%3E%3C/svg%3E") !important;
  background-position: left 12px center !important;
  background-repeat: no-repeat !important;
  background-size: 20px !important;
  padding-right: 12px !important;
}

/* تحسينات للعناصر المخصصة */
.swal2-content-custom {
  max-height: 300px !important;
  overflow-y: auto !important;
  padding: 15px !important;
  background: var(--card-background) !important;
  border-radius: 12px !important;
  border: 1px solid var(--border-color) !important;
  margin-bottom: 20px !important;
}

.saved-item-custom {
  display: flex !important;
  justify-content: space-between !important;
  align-items: center !important;
  padding: 12px 16px !important;
  margin: 8px 0 !important;
  background: var(--page-background) !important;
  border-radius: 8px !important;
  border: 1px solid var(--border-color) !important;
  transition: all var(--transition-normal) !important;
}

.saved-item-custom:hover {
  background: var(--background) !important;
  transform: translateY(-2px) !important;
  box-shadow: 0 5px 15px var(--shadow-light) !important;
}

.delete-btn-custom {
  background: var(--error-color) !important;
  color: white !important;
  border: none !important;
  border-radius: 6px !important;
  padding: 6px 12px !important;
  font-size: 0.85rem !important;
  font-weight: 600 !important;
  transition: all var(--transition-normal) !important;
}

.delete-btn-custom:hover {
  background: var(--error-dark) !important;
  transform: scale(1.05) !important;
}

/* تحسينات للشاشات الصغيرة */
@media (max-width: 768px) {
  .swal2-popup {
    width: 90% !important;
    padding: 20px !important;
  }
  
  .swal2-title {
    font-size: 1.3rem !important;
  }
  
  .swal2-html-container {
    font-size: 0.9rem !important;
  }
  
  .swal2-confirm, 
  .swal2-deny, 
  .swal2-cancel {
  }
  
  .swal2-actions {
    flex-direction: row !important;
    gap: 10px !important;
  }
}
/* تحسين النافذة المنبثقة للشاشات الصغيرة - Custom */
@media (max-width: 768px) {
  .swal2-popup-custom {
    padding: 20px !important;
  }

  .swal2-title-custom {
    font-size: 1.3rem !important;
  }

  .swal2-html-container-custom {
    font-size: 0.9rem !important;
  }

  .swal2-actions-custom {
    flex-direction: row-reverse !important;
    gap: 10px !important;
    flex-wrap: nowrap !important;
  }

  .swal2-confirm-custom,
  .swal2-cancel-custom {
    flex: 1 !important;
    min-width: 100px !important;
    padding: 8px 12px !important;
  }
}