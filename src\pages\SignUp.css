/* تصميم إنشاء الحساب الجديد بدون Container */
@import '../styles/variables.css';

.signup-container {
  min-height: 100vh;
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  background: linear-gradient(135deg, #f8fafc 0%, #e2e8f0 100%);
  font-family: var(--font-family-primary);
  padding: 20px;
  box-sizing: border-box;
  overflow: hidden;
}

/* قسم الشعار */
.logo-section {
  text-align: center;
  margin-bottom: 30px;
}

.legal-agenda-logo {
  width: 220px;
  height: auto;
  margin-bottom: 0;
  filter: drop-shadow(0 4px 8px rgba(0, 0, 0, 0.1));
}

/* العناوين */
.header-form {
  display: flex;
  flex-direction: column;
  align-items: center;
  margin-bottom: 30px;
  text-align: center;
}

.header-form .title {
  font-size: 2.5rem;
  font-weight: 700;
  color: var(--primary-dark-blue);
  margin-bottom: 8px;
  line-height: 1.2;
  background: linear-gradient(135deg, var(--primary-dark-blue) 0%, var(--primary-medium-blue) 100%);
  -webkit-background-clip: text;
  -webkit-text-fill-color: transparent;
  background-clip: text;
}

.header-form .subtitle {
  color: var(--neutral-600);
  font-size: 1.1rem;
  font-weight: 400;
  margin-bottom: 0;
  line-height: 1.4;
}

/* نموذج إنشاء الحساب */
#signup-form {
  display: flex;
  flex-direction: column;
  gap: 20px;
  width: 100%;
  max-width: 400px;
  margin: 0 auto;
}

/* مؤشر الخطوات */
.steps-indicator {
  display: flex;
  justify-content: center;
  align-items: center;
  gap: 15px;
  margin-bottom: 30px;
}

.step-dot {
  width: 45px;
  height: 45px;
  border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
  font-size: 1rem;
  font-weight: 600;
  transition: all 0.3s ease;
  position: relative;
}

.step-dot.pending {
  background: var(--neutral-200);
  color: var(--neutral-500);
  border: 2px solid var(--neutral-200);
}

.step-dot.active {
  background: linear-gradient(135deg, var(--primary-medium-blue) 0%, var(--primary-dark-blue) 100%);
  color: var(--white);
  border: 2px solid var(--primary-medium-blue);
  box-shadow: 0 4px 15px rgba(76, 104, 192, 0.3);
  transform: scale(1.1);
}

.step-dot.completed {
  background: linear-gradient(135deg, var(--success-color) 0%, var(--success-dark) 100%);
  color: var(--white);
  border: 2px solid var(--success-color);
  box-shadow: 0 4px 15px rgba(16, 185, 129, 0.3);
}


/* حاوي الخطوة */
.step-container {
  min-height: 140px;
  opacity: 0;
  transform: translateY(30px);
  transition: all 0.5s cubic-bezier(0.4, 0, 0.2, 1);
}

.step-container.active {
  opacity: 1;
  transform: translateY(0);
}

.step-title {
  color: var(--neutral-700);
  font-size: 1.3rem;
  font-weight: 600;
  margin-bottom: 20px;
  text-align: center;
}

/* حقول الإدخال */
#signup-form input {
  padding: 18px 24px;
  border: 2px solid var(--neutral-200);
  border-radius: 12px;
  outline: none;
  transition: all 0.3s ease;
  width: 100%;
  font-size: 1.1rem;
  font-family: var(--font-family-primary);
  box-sizing: border-box;
  min-height: 60px;
  background-color: var(--white);
  color: var(--neutral-700);
  margin-bottom: 0;
  font-weight: 500;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.04);
}

#signup-form input:focus {
  border-color: var(--primary-medium-blue);
  box-shadow: 0 0 0 3px rgba(76, 104, 192, 0.1), 0 4px 12px rgba(0, 0, 0, 0.1);
  transform: translateY(-2px);
}

#signup-form input::placeholder {
  color: var(--neutral-500);
  font-weight: 400;
}

#signup-form input:hover {
  border-color: var(--primary-medium-blue);
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.08);
}

/* حاوي الأزرار */
.button-container {
  display: flex;
  justify-content: center;
  align-items: center;
  gap: 20px;
  margin-top: 30px;
  flex-wrap: wrap;
}

.button-container:has(.prev-btn) {
  justify-content: space-between;
}

.button-container:not(:has(.prev-btn)) {
  justify-content: center;
}

/* زر السابق */
.prev-btn {
  background: transparent;
  color: var(--primary-medium-blue);
  padding: 12px 25px;
  border: 2px solid var(--primary-medium-blue);
  border-radius: 12px;
  cursor: pointer;
  font-size: 1rem;
  font-weight: 500;
  transition: all 0.3s ease;
  min-width: 100px;
  font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
}

.prev-btn:hover {
  background: var(--primary-medium-blue);
  color: var(--white);
  transform: translateY(-2px);
  box-shadow: 0 4px 15px var(--shadow-light);
}

.prev-btn:disabled {
  opacity: 0.5;
  cursor: not-allowed;
  transform: none;
  box-shadow: none;
}

/* تطبيق الثيم الجديد على الأزرار */
#signup-form button {
  border: none;
  padding: 15px 20px;
  border-radius: 5px;
  cursor: pointer;
  transition: 0.1s ease-in-out;
  font-family: var(--font-family-primary);
  font-size: 1.1rem;
  min-height: 55px;
  width: 100%;
  margin-top: 10px;
  font-weight: 500;
}

#signup-form button[type="submit"] {
  background-color: var(--primary-color);
  color: #fff;
}

#signup-form button[type="submit"]:hover {
  opacity: 0.9;
  transform: translateY(-1px);
}

#signup-form button:disabled {
  background-color: var(--gray-light);
  color: var(--gray);
  cursor: not-allowed;
  opacity: 0.6;
}

/* تعديل زر التالي/إنشاء الحساب */
.google-login-btn {
  flex: 1;
  max-width: 200px;
}

.google-login-btn {
  background: linear-gradient(135deg, var(--primary-medium-blue) 0%, var(--primary-dark-blue) 100%);
  color: var(--white);
  padding: 18px 40px;
  border: none;
  border-radius: 15px;
  cursor: pointer;
  font-size: 1.1rem;
  font-weight: 600;
  transition: all 0.3s ease;
  min-height: 60px;
  width: 100%;
  font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
  position: relative;
  overflow: hidden;
}

.google-login-btn::before {
  content: '';
  position: absolute;
  top: 0;
  left: -100%;
  width: 100%;
  height: 100%;
  background: linear-gradient(90deg, transparent, rgba(255, 255, 255, 0.2), transparent);
  transition: left 0.5s;
}

.google-login-btn:hover::before {
  left: 100%;
}

.google-login-btn:hover {
  transform: translateY(-2px);
  box-shadow: 0 8px 25px var(--shadow-medium);
}

.google-login-btn:active {
  transform: translateY(0);
  box-shadow: 0 4px 15px var(--shadow-medium);
}

.google-login-btn:disabled {
  background: var(--light-purple-gray);
  color: var(--dark-blue-gray);
  cursor: not-allowed;
  transform: none;
  box-shadow: none;
}

.google-login-btn:disabled::before {
  display: none;
}

.create-account-link {
  color: var(--primary-medium-blue);
  text-decoration: none;
  font-size: 1rem;
  font-weight: 600;
  transition: all 0.3s ease;
  padding: 10px 20px;
  border-radius: 10px;
}

.create-account-link:hover {
  background-color: var(--light-purple-gray);
  color: var(--primary-dark-blue);
}

/* مجموعة رابط التسجيل */
.signup-link {
  text-align: center;
  margin-top: 20px;
  padding-top: 20px;
  border-top: 1px solid var(--light-purple-gray);
}

/* تأثيرات الانتقال للحقول */
.form-group input[type='text'],
.form-group input[type='email'],
.form-group input[type='password'],
.form-group input[type='tel'] {
  animation: slideInUp 0.5s ease-out;
}

@keyframes slideInUp {
  from {
    opacity: 0;
    transform: translateY(30px);
  }
  to {
    opacity: 1;
    transform: translateY(0);
  }
}

/* تأثير التركيز المحسن */
.form-group input:focus {
  border-color: var(--primary-medium-blue);
  box-shadow: 0 0 0 3px rgba(76, 104, 192, 0.1);
  transform: translateY(-2px) scale(1.02);
}

/* تأثير التحقق من صحة البيانات */
.form-group input:valid:not(:placeholder-shown) {
  border-color: #28a745;
  background-image: url("data:image/svg+xml,%3csvg xmlns='http://www.w3.org/2000/svg' viewBox='0 0 8 8'%3e%3cpath fill='%2328a745' d='m2.3 6.73.7-.7L4.25 4.8 6.7 2.35l.7.7-3.2 3.18z'/%3e%3c/svg%3e");
  background-repeat: no-repeat;
  background-position: left 15px center;
  background-size: 16px;
  padding-left: 45px;
}

/* تحسين الاستجابة */
@media (max-width: 480px) {
  .steps-indicator {
    gap: 10px;
    margin-bottom: 20px;
  }
  
  .step-dot {
    width: 40px;
    height: 40px;
    font-size: 0.9rem;
  }
  
  .step-dot:not(:last-child)::after {
    width: 20px;
    right: -15px;
  }
  
  .button-container {
    flex-direction: column;
    gap: 15px;
  }
  
  .prev-btn,
  .google-login-btn {
    width: 100%;
    max-width: none;
  }
  
  .step-title {
    font-size: 1.1rem;
    margin-bottom: 15px;
  }
}

/* رسائل الخطأ والنجاح */
.error-message {
  color: #e53e3e;
  margin: 15px 0;
  padding: 12px 15px;
  background: #fed7d7;
  border-radius: 5px;
  font-size: 14px;
  text-align: center;
}

.success-message {
  color: #38a169;
  margin: 15px 0;
  padding: 12px 15px;
  background: #c6f6d5;
  border-radius: 5px;
  font-size: 14px;
  text-align: center;
}


/* تصميم متجاوب */
@media (max-width: 480px) {
  .signup-container {
    padding: 10px;
  }

  .signup-box {
    padding: 30px 20px;
  }

  .legal-agenda-logo {
    width: 100px;
  }

  #signup-form .header-form .title {
    font-size: 24px;
  }

  .step-title {
    font-size: 16px;
  }

  .button-container {
    flex-direction: column;
    gap: 15px;
  }

  .prev-btn,
  .google-login-btn {
    width: 100%;
  }
}
/* CSS for password toggle button inside input */
.password-input-container {
  position: relative;
  width: 100%;
}

.password-input-container input.password-input {
  width: 100%;
  padding-right: 40px; /* Make space for the icon */
}

.password-input-container .password-toggle-button {
  position: absolute;
  right: 10px;
  top: 50%;
  transform: translateY(-50%);
  background: none;
  border: none;
  cursor: pointer;
  font-size: 1.2em;
  color: #888;
  padding: 0;
}

/* Styles for input requirements comments */
.input-requirements,
.password-requirements {
  font-size: 0.9rem;
  color: var(--neutral-600);
  margin-top: 5px;
  padding: 8px 12px;
  background-color: var(--neutral-100);
  border-radius: 8px;
  border: 1px solid var(--neutral-200);
  display: block; /* Ensures it takes full width */
  text-align: center;
  line-height: 1.4;
  box-shadow: 0 2px 4px rgba(0, 0, 0, 0.05);
  transition: all 0.3s ease;
}

.input-requirements:hover,
.password-requirements:hover {
  background-color: var(--neutral-200);
  border-color: var(--primary-medium-blue);
  color: var(--primary-dark-blue);
}