import React, { useState } from 'react';
import { FaGavel, FaSave, FaTimes, FaArrowLeft } from 'react-icons/fa';
import styles from "./CaseDetailsNew.module.css";

const JudgmentVerdictModal = ({ deferralData, onSave, onCancel }) => {
  const [step, setStep] = useState(1);
  const [verdict, setVerdict] = useState('');
  const [verdictDetails, setVerdictDetails] = useState('');
  // --- تم حذف verdictDate من هنا ---
  const [error, setError] = useState('');

  const handleOptionSelect = (selectedVerdict) => {
    setVerdict(selectedVerdict);
    setStep(2);
  };

  const handleBack = () => {
    setStep(1);
    setError('');
    setVerdictDetails('');
    // --- وتم حذف verdictDate من هنا أيضاً ---
  };

  const handleSave = () => {
    // --- تم تعديل شرط التحقق هنا ---
    if (!verdictDetails) {
      setError('يرجى إدخال تفاصيل الحكم');
      return;
    }

    // --- تم تعديل الكائن الذي يتم إرساله هنا ---
    onSave({
      verdict: verdict,
      verdictDetails: verdictDetails,
      // لم نعد نرسل تاريخ الحكم
      deferralDate: deferralData.originalDate
    });
  };

  const renderStepOne = () => (
    <>
      <div className={styles.transferConfirmationIcon}><FaGavel /></div>
      <h3 className={styles.transferConfirmationTitle}>تسجيل منطوق الحكم</h3>
      <p className={styles.transferConfirmationMessage}>
        جلسة الحكم بتاريخ: {new Date(deferralData.originalDate).toLocaleDateString('ar-EG')}
      </p>
      <p className={styles.questionText}>اختر نوع الحكم:</p>
      <div className={styles.addOptionsContainer}>
        <button className={styles.addOptionButton} onClick={() => handleOptionSelect('الإحالة للخبراء')}>إحالة للخبراء</button>
        <button className={styles.addOptionButton} onClick={() => handleOptionSelect('الإحالة لمحكمة أخرى')}>إحالة لمحكمة أخرى</button>
        <button className={styles.addOptionButton} onClick={() => handleOptionSelect('أخرى')}>أخرى</button>
      </div>
      <div className={styles.transferConfirmationActions}>
        <button onClick={onCancel} className={styles.transferCancelButton}><FaTimes /> إلغاء</button>
      </div>
    </>
  );

  const renderStepTwo = () => (
    <>
      <div className={styles.modalHeader}>
        <button onClick={handleBack} className={styles.backButton}><FaArrowLeft /></button>
        <h3 className={styles.transferConfirmationTitle}>{verdict}</h3>
      </div>
      <div className={styles.statusOptionsContainer}>
        <div className={styles.dateInputContainer}>
          <label className={styles.dateLabel}>تفاصيل الحكم <span style={{ color: 'red' }}>*</span></label>
          <textarea
            value={verdictDetails}
            onChange={(e) => { setVerdictDetails(e.target.value); setError(''); }}
            className={`${styles.editInput} ${styles.textareaInput} ${error ? styles.inputError : ''}`}
            placeholder="أدخل تفاصيل ومنطوق الحكم"
            rows={4}
          />
        </div>
        
        {/* === تم حذف حاوية حقل التاريخ بالكامل من هنا === */}

        {error && <div className={styles.errorMessage}>{error}</div>}
      </div>
      <div className={styles.transferConfirmationActions}>
        {/* --- تم تعديل شرط التعطيل هنا --- */}
        <button onClick={handleSave} className={styles.transferConfirmButton} disabled={!verdictDetails}><FaSave /> حفظ</button>
        <button onClick={onCancel} className={styles.transferCancelButton}><FaTimes /> إلغاء</button>
      </div>
    </>
  );

  return (
    <div className={styles.transferConfirmationOverlay}>
      <div className={styles.transferConfirmationDialog}>
        {step === 1 ? renderStepOne() : renderStepTwo()}
      </div>
    </div>
  );
};

export default JudgmentVerdictModal;
