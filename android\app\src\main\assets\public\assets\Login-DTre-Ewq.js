import{r as i,c as C,j as e,L as E,s as M,a as k,n as P}from"./index-HUlFBKIW.js";import{c as w,a as R,r as D}from"./LockoutManager-ByDxv6hW.js";function F(){const[o,N]=i.useState(""),[m,g]=i.useState(""),[x,t]=i.useState(""),[j,h]=i.useState(""),[c,r]=i.useState(!1),[S,f]=i.useState(0),[u,p]=i.useState(!1),v=C();i.useEffect(()=>{const s=w();s.isLocked?(t(s.message),r(!0)):f(s.attempts)},[]);const L=s=>{const a=s.target.value;N(a);const l=/^[^\s@]+@[^\s@]+\.[^\s@]+$/;a.trim()&&l.test(a)?p(!0):(p(!1),g(""))},y=async s=>{if(s.preventDefault(),t(""),h(""),r(!0),!o.trim()||!m.trim()){t("يرجى إدخال البريد الإلكتروني وكلمة المرور."),r(!1);return}const a=w();if(a.isLocked){t(a.message),r(!1);return}try{const d=(await M(k,o.trim(),m)).user;console.log("تم تسجيل الدخول بنجاح:",d),R(),f(0),v("/dashboard",{replace:!0})}catch(l){console.error("خطأ في تسجيل الدخول:",l.code,l.message);const d=D(S);if(f(d.attempts),d.isLocked){t(d.message),r(!0);return}let n="حدث خطأ أثناء تسجيل الدخول. يرجى المحاولة مرة أخرى.";switch(l.code){case"auth/invalid-credential":n="البريد الإلكتروني أو كلمة المرور غير صحيحة. يرجى التحقق من البيانات أو إعادة تعيين كلمة المرور.";break;case"auth/invalid-email":n="صيغة البريد الإلكتروني غير صحيحة.";break;case"auth/user-disabled":n="تم تعطيل حساب المستخدم هذا.";break;case"auth/too-many-requests":n="��م حظر تسجيل الدخول مؤقتًا بسبب محاولات كثيرة جدًا. يرجى المحاولة لاحقًا.";break;default:n="حدث خطأ غير متوقع. يرجى التحقق من اتصال الإنترنت والمحاولة لاحقًا."}t(n)}finally{r(!1)}},b=async()=>{if(!o.trim()){t("يرجى إدخال البريد الإلكتروني أولاً.");return}t(""),h(""),r(!0);try{await P(k,o.trim()),h("تم إرسال رابط إعادة تعيين كلمة المرور إلى بريدك الإلكتروني. يرجى التحقق من صندوق الوارد أو البريد العشوائي.")}catch(s){let a="حدث خطأ أثناء إرسال رابط إعادة التعيين.";switch(s.code){case"auth/invalid-email":a="صيغة البريد الإلكتروني غير صحيحة.";break;case"auth/user-not-found":a="لا يوجد حساب مرتبط بهذا البريد الإلكتروني.";break;default:a="حدث خطأ غير متوقع. يرجى المحاولة لاحقًا."}t(a)}finally{r(!1)}};return e.jsxs("div",{className:"login-container",children:[e.jsx("div",{className:"logo-section",children:e.jsx("img",{src:"/logo.png",alt:"الأجندة القضائية",className:"legal-agenda-logo"})}),e.jsxs("div",{className:"header-form",children:[e.jsx("span",{className:"title",children:"مرحباً بعودتك!"}),e.jsx("span",{className:"subtitle",children:"اختر خياراً للمتابعة"})]}),c&&e.jsx(E,{message:"جاري تسجيل الدخول..."}),x&&e.jsx("div",{className:"error-message",children:x}),j&&e.jsx("div",{className:"success-message",children:j}),e.jsxs("form",{id:"login-form",onSubmit:y,children:[e.jsx("input",{type:"email",id:"login-email",placeholder:"البريد الإلكتروني",required:!0,value:o,onChange:L,disabled:c}),u&&e.jsx("input",{type:"password",id:"login-password",placeholder:"كلمة المرور",required:!0,value:m,onChange:s=>g(s.target.value),disabled:c}),e.jsx("button",{type:"submit",disabled:c||!u,children:c?"جاري التحقق...":"متابعة"}),e.jsxs("div",{className:"division-or",children:[e.jsx("div",{className:"h-line"}),e.jsx("span",{children:"أو"}),e.jsx("div",{className:"h-line"})]}),e.jsx("button",{type:"button",id:"google-btn",children:"متابعة مع Google"}),e.jsx("button",{type:"button",id:"signup-btn",onClick:()=>v("/signup"),children:"إنشاء حساب"}),!u&&e.jsx("div",{className:"forgot-password",children:e.jsx("a",{href:"#",onClick:s=>{s.preventDefault(),b()},children:"نسيت عنوان البريد الإلكتروني؟"})}),u&&e.jsxs("div",{className:"password-actions",children:[e.jsx("div",{className:"forgot-password",children:e.jsx("a",{href:"#",onClick:s=>{s.preventDefault(),b()},children:"نسيت كلمة المرور؟"})}),e.jsx("div",{className:"back-to-email",children:e.jsx("a",{href:"#",onClick:s=>{s.preventDefault(),p(!1),g("")},children:"← تغيير البريد الإلكتروني"})})]})]})]})}export{F as default};
