import React from 'react';
// تم استيراد FaTrash لحذف القضية وإزالة FaExchangeAlt التي كانت تستخدم للتحويل
import { FaPen, FaTrash, FaSave, FaTimes, FaGavel, FaUsers, FaFileAlt } from 'react-icons/fa'; 
import { caseCategoriesByDegree, courtLocations, caseDegrees } from "../../utils/CaseFilters";
import styles from "./CaseDetailsNew.module.css";
import CaseEditAndReferral from './CaseEditAndReferral';

// تمت إضافة onCaseDelete إلى props لاستقبال دالة الحذف من المكون الأب
const CaseInfoGroups = React.memo(({ caseData, currentUser, onCaseDataUpdate, onJudgmentDetected, onCaseDelete }) => {
  if (!caseData) {
    return <div className={styles.loadingMessage}>جاري تحميل بيانات القضية...</div>;
  }

  const caseCategoryOptions = caseData?.caseDegree ? caseCategoriesByDegree[caseData.caseDegree] || [] : [];

  return (
    <CaseEditAndReferral
      caseData={caseData}
      currentUser={currentUser}
      onCaseDataUpdate={onCaseDataUpdate}
      onJudgmentDetected={onJudgmentDetected}
      // تمرير دالة الحذف إلى المكون الحاوي للمنطق
      onCaseDelete={onCaseDelete} 
      caseCategoryOptions={caseCategoryOptions}
      courtLocations={courtLocations}
      caseDegrees={caseDegrees}
      styles={styles}
    >
      {({
        editingField,
        editValue,
        customValue,
        error,
        saving,
        showCaseNumberFields,
        caseNumberValue,
        caseYearValue,
        showCourtReferralModal,
        showExpertReferralModal,
        lawsuitDate,
        reportDate,
        newCourtLocation,
        referralDateError,
        expertLocation,
        expertReferralDateError,
        showEditModal,
        showDeleteConfirmation, // حالة جديدة لإظهار نافذة تأكيد الحذف
        handleEditClick,
        handleSaveEdit,
        handleCancelEdit,
        handleCourtReferralConfirm,
        handleCourtReferralCancel,
        handleExpertReferralConfirm,
        handleExpertReferralCancel,
        handleDeleteClick, // دالة جديدة لفتح نافذة تأكيد الحذف
        handleDeleteConfirm, // دالة جديدة لتأكيد الحذف
        handleDeleteCancel, // دالة جديدة لإلغاء الحذف
        setEditValue,
        setCustomValue,
        setCaseNumberValue,
        setCaseYearValue,
        setNewCourtLocation,
        setExpertLocation,
        setLawsuitDate,
        setReportDate,
      }) => (
        <>
          {/* الهيكل الجديد لعرض بيانات القضية */}
          <div className={styles.headerInfoContainer}>
            
            {/* الصف الأول: الخصوم */}
            <div className={styles.infoRow}>
              <div 
                className={`${styles.infoBlock} ${styles.theme1}`}
                onClick={() => handleEditClick({ field: 'clientName', label: 'اسم الموكل', type: 'text', required: true }, caseData.clientName)}
                title="تعديل اسم الموكل"
              >
                <span className={styles.infoLabel}>الموكل</span>
                <span className={styles.infoValue}>{caseData.clientName || 'غير محدد'}</span>
              </div>
              
              <div className={styles.versusSeparator}>
                <span className={styles.versusText}>ضد</span>
              </div>

              <div 
                className={`${styles.infoBlock} ${styles.theme2}`}
                onClick={() => handleEditClick({ field: 'opponentName', label: 'اسم الخصم', type: 'text', required: false }, caseData.opponentName)}
                title="تعديل اسم الخصم"
              >
                <span className={styles.infoLabel}>الخصم</span>
                <span className={styles.infoValue}>{caseData.opponentName || 'غير محدد'}</span>
              </div>
            </div>


            {/* الصف الثاني: بيانات القضية */}
            <div className={styles.infoRow}>
              {caseData.caseStatus !== 'قيد النظر' && (
                <div 
                  className={`${styles.infoBlock} ${styles.theme3}`}
                  onClick={() => handleEditClick({ field: 'fullCaseNumber', label: caseData.caseStatus === 'محضر' ? 'رقم المحضر' : 'رقم القضية', type: 'text', required: true, isFullCaseNumber: true }, caseData.fullCaseNumber)}
                  title={caseData.caseStatus === 'محضر' ? 'تعديل رقم المحضر' : 'تعديل رقم القضية'}
                >
                  <span className={styles.infoLabel}>{caseData.caseStatus === 'محضر' ? 'رقم المحضر' : 'رقم القضية'}</span>
                  <span className={styles.infoValue}>{caseData.fullCaseNumber || '—'}</span>
                </div>
              )}

              {caseData.caseStatus === 'دعوى قضائية' && (
                <div 
                  className={`${styles.infoBlock} ${styles.theme4}`}
                  onClick={() => handleEditClick({ field: 'circleNumber', label: 'رقم الدائرة', type: 'text', required: false }, caseData.circleNumber)}
                  title="تعديل رقم الدائرة"
                >
                  <span className={styles.infoLabel}>الدائرة</span>
                  <span className={styles.infoValue}>{caseData.circleNumber || 'غير محدد'}</span>
                </div>
              )}

              {caseData.caseStatus === 'دعوى قضائية' && (
                <div 
                  className={`${styles.infoBlock} ${styles.theme5}`}
                  onClick={() => handleEditClick({ field: 'caseCategory', label: 'نوع الدعوى', type: 'select', options: caseCategoryOptions, required: true }, caseData.caseCategory)}
                  title="تعديل نوع الدعوى"
                >
                  <span className={styles.infoLabel}>نوع الدعوى</span>
                  <span className={styles.infoValue}>{caseData.caseCategory || 'غير محدد'}</span>
                </div>
              )}
            </div>

            {/* الصف الثالث: الوصف والمحكمة والدرجة */}
            <div className={styles.infoRow}>
                <div 
                  className={`${styles.infoBlock} ${styles.theme6}`}
                  onClick={() => handleEditClick({ field: 'caseDescription', label: 'الوصف العام', type: 'textarea', required: false }, caseData.caseDescription)}
                  title="تعديل الوصف العام"
                >
                  <span className={styles.infoLabel}>الوصف العام</span>
                  <span className={styles.infoValueDescription}>{caseData.caseDescription || 'لا يوجد وصف'}</span>
                </div>

                <div 
                    className={`${styles.infoBlock} ${styles.theme1}`}
                    onClick={() => handleEditClick({ field: 'courtLocation', label: 'مكان المحكمة', type: 'text', required: true }, caseData.courtLocation)}
                    title="تعديل مكان المحكمة"
                >
                    <span className={styles.infoLabel}>المحكمة</span>
                    <span className={styles.infoValue}>{caseData.courtLocation || 'غير محدد'}</span>
                </div>

                {caseData.caseStatus === 'دعوى قضائية' && (
                    <div 
                        className={`${styles.infoBlock} ${styles.theme2}`}
                        onClick={() => handleEditClick({ field: 'caseDegree', label: 'درجة التقاضي', type: 'select', options: caseDegrees, required: true }, caseData.caseDegree)}
                        title="تعديل درجة التقاضي"
                    >
                        <span className={styles.infoLabel}>درجة التقاضي</span>
                        <span className={styles.infoValue}>{caseData.caseDegree || 'غير محدد'}</span>
                    </div>
                )}
            </div>
          </div>

          {/* الوظائف الموسعة (تم حذف قسم تحويل الحالة بالكامل) */}
          <div className={styles.expandedFunctionsContainer}>
            {caseData.originalCaseId && (
              <div className={styles.transferDegreeSection}>
                <div className={`${styles.transferDegreeBox} ${styles.identificationTheme}`}>
                  <div className={styles.transferHeader}>
                    <div className={styles.transferTitleSection}>
                      <FaFileAlt className={styles.transferIcon} />
                      <span className={styles.transferTitle}>بيانات الملف السابق</span>
                    </div>
                  </div>
                  <div className={styles.transferContent}>
                    {caseData.originalCaseNumber && (
                      <div className={styles.transferInfoRow}>
                        <span className={styles.transferLabel}>رقم القضية السابق:</span>
                        <span className={styles.transferValue}>{caseData.originalCaseNumber}</span>
                      </div>
                    )}
                  </div>
                </div>
              </div>
            )}
          </div>

          {/* نافذة التعديل المنبثقة (مع إضافة زر الحذف) */}
          {showEditModal && editingField && (
            <div className={styles.transferConfirmationOverlay}>
              <div className={styles.transferConfirmationDialog}>
                <div className={styles.transferConfirmationIcon}><FaPen /></div>
                <h3 className={styles.transferConfirmationTitle}>تعديل {editingField.label}</h3>
                
                 <div className={styles.statusOptionsContainer}>
                  {editingField.isFullCaseNumber && showCaseNumberFields ? (
                    <div className={styles.fullCaseNumberEditContainer}>
                      <div className={styles.caseNumberFieldsContainer}>
                        <div className={styles.caseNumberField}>
                          <label className={styles.inlineLabel}>{caseData.caseStatus === 'محضر' ? 'رقم المحضر:' : 'رقم القضية:'}</label>
                          <input
                            type="text"
                            value={caseNumberValue}
                            onChange={(e) => setCaseNumberValue(e.target.value)}
                            className={`${styles.editInput} ${error ? styles.inputError : ''}`}
                            placeholder="أدخل الرقم"
                          />
                        </div>
                        <div className={styles.caseNumberField}>
                          <label className={styles.inlineLabel}>السنة:</label>
                          <input
                            type="text"
                            value={caseYearValue}
                            onChange={(e) => setCaseYearValue(e.target.value)}
                            className={`${styles.editInput} ${error ? styles.inputError : ''}`}
                            placeholder="أدخل السنة"
                          />
                        </div>
                      </div>
                    </div>
                  ) : (
                    <>
                      {(() => {
                        switch (editingField.type) {
                          case 'select':
                            return (
                              <>
                                <select
                                  value={editValue}
                                  onChange={(e) => setEditValue(e.target.value)}
                                  className={styles.editInput}
                                >
                                  <option value="">اختر {editingField.label}</option>
                                  {(editingField.options || []).map((option) => (
                                    <option key={option} value={option}>
                                      {option}
                                    </option>
                                  ))}
                                </select>
                                {editValue === 'أخرى' && (
                                  <input
                                    type="text"
                                    value={customValue}
                                    onChange={(e) => setCustomValue(e.target.value)}
                                    className={styles.editInput}
                                    placeholder="أدخل قيمة مخصصة"
                                    style={{ marginTop: '1rem' }}
                                  />
                                )}
                              </>
                            );
                          case 'textarea':
                            return (
                              <textarea
                                value={editValue}
                                onChange={(e) => setEditValue(e.target.value)}
                                className={`${styles.editInput} ${styles.textareaInput}`}
                                placeholder={`أدخل ${editingField.label}`}
                                rows="5"
                              />
                            );
                          default:
                            return (
                              <input
                                type="text"
                                value={editValue}
                                onChange={(e) => setEditValue(e.target.value)}
                                className={styles.editInput}
                                placeholder={`أدخل ${editingField.label}`}
                              />
                            );
                        }
                      })()}
                    </>
                  )}
                  {error && <div className={styles.errorText}>{error}</div>}
                </div>
                
                <div className={styles.transferConfirmationActions}>
                  <button onClick={handleSaveEdit} className={styles.transferConfirmButton} disabled={saving}>
                    <FaSave /> {saving ? 'جاري الحفظ...' : 'حفظ'}
                  </button>
                  <button onClick={handleCancelEdit} className={styles.transferCancelButton} disabled={saving}>
                    <FaTimes /> إلغاء
                  </button>
                </div>

                {/* زر حذف القضية يظهر فقط عند تعديل اسم الموكل */}
                {editingField.field === 'clientName' && (
                  <div className={styles.deleteActionContainer}>
                    <button onClick={handleDeleteClick} className={styles.deleteButton} disabled={saving}>
                      <FaTrash /> حذف القضية بالكامل
                    </button>
                  </div>
                )}
              </div>
            </div>
          )}

          {/* نافذة تأكيد الحذف الجديدة */}
          {showDeleteConfirmation && (
            <div className={styles.transferConfirmationOverlay}>
              <div className={styles.transferConfirmationDialog}>
                <div className={styles.transferConfirmationIcon} style={{ color: 'var(--danger-color)' }}><FaTrash /></div>
                <h3 className={styles.transferConfirmationTitle}>تأكيد حذف القضية</h3>
                <p className={styles.transferConfirmationMessage}>
                  هل أنت متأكد من رغبتك في حذف هذه القضية بشكل نهائي؟  
لا يمكن التراجع عن هذا الإجراء.
                </p>
                <div className={styles.transferConfirmationActions}>
                  <button onClick={handleDeleteConfirm} className={styles.deleteConfirmButton}>
                    <FaTrash /> نعم، قم بالحذف
                  </button>
                  <button onClick={handleDeleteCancel} className={styles.transferCancelButton}>
                    <FaTimes /> إلغاء
                  </button>
                </div>
              </div>
            </div>
          )}

          {/* نافذة الإحالة للمحكمة */}
          {showCourtReferralModal && (
            <div className={styles.transferConfirmationOverlay}>
              <div className={styles.transferConfirmationDialog}>
                <div className={styles.transferConfirmationIcon}><FaGavel /></div>
                <h3 className={styles.transferConfirmationTitle}>إحالة لمحكمة أخرى</h3>
                <p className={styles.transferConfirmationMessage}>هل تم الحكم بالإحالة لمحكمة أخرى؟</p>
                <div className={styles.statusOptionsContainer}>
                  <div className={styles.dateInputContainer}>
                    <label className={styles.dateLabel}>
                      المحكمة الجديدة <span style={{ color: 'red' }}>*</span>
                    </label>
                    <select value={newCourtLocation} onChange={(e) => setNewCourtLocation(e.target.value)} className={styles.dateInput}>
                      <option value="">اختر المحكمة</option>
                      {courtLocations.map((court) => (
                        <option key={court.name} value={court.name}>
                          {court.name}
                        </option>
                      ))}
                    </select>
                  </div>
                  {referralDateError && <div className={styles.errorMessage}>{referralDateError}</div>}
                </div>
                <div className={styles.transferConfirmationActions}>
                  <button onClick={handleCourtReferralConfirm} className={styles.transferConfirmButton} disabled={!newCourtLocation}>
                    <FaGavel /> تأكيد الإحالة
                  </button>
                  <button onClick={handleCourtReferralCancel} className={styles.transferCancelButton}>
                    <FaTimes /> إلغاء
                  </button>
                </div>
              </div>
            </div>
          )}

          {/* نافذة الإحالة للخبراء */}
          {showExpertReferralModal && (
            <div className={styles.transferConfirmationOverlay}>
              <div className={styles.transferConfirmationDialog}>
                <div className={styles.transferConfirmationIcon}><FaUsers /></div>
                <h3 className={styles.transferConfirmationTitle}>إحالة للخبراء</h3>
                <p className={styles.transferConfirmationMessage}>هل تم الحكم بالإحالة للخبراء؟</p>
                <div className={styles.statusOptionsContainer}>
                  <div className={styles.dateInputContainer}>
                    <label className={styles.dateLabel}>
                      مكان/اسم الخبراء <span style={{ color: 'red' }}>*</span>
                    </label>
                    <input
                      type="text"
                      value={expertLocation}
                      onChange={(e) => setExpertLocation(e.target.value)}
                      className={styles.dateInput}
                      placeholder="أدخل مكان أو اسم الخبراء"
                    />
                  </div>
                  {expertReferralDateError && <div className={styles.errorMessage}>{expertReferralDateError}</div>}
                </div>
                <div className={styles.transferConfirmationActions}>
                  <button onClick={handleExpertReferralConfirm} className={styles.transferConfirmButton} disabled={!expertLocation.trim()}>
                    <FaUsers /> تأكيد الإحالة للخبراء
                  </button>
                  <button onClick={handleExpertReferralCancel} className={styles.transferCancelButton}>
                    <FaTimes /> إلغاء
                  </button>
                </div>
              </div>
            </div>
          )}
        </>
      )}
    </CaseEditAndReferral>
  );
});

export default CaseInfoGroups;
