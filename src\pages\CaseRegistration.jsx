import React, { useState, useEffect } from 'react';
import Swal from 'sweetalert2';
import { FaPlus } from 'react-icons/fa';
import TopBar from "../components/topbar/TopBar";
import './CaseRegistration.css';
import { caseDegrees, caseCategoriesByDegree } from "../utils/CaseFilters";
import { useNavigate } from 'react-router-dom';
import { getActiveAccount, addCase, getCases, updateCase } from '../services/StorageService';
import { notifyCaseCreated } from '../utils/CacheManager';

const CaseRegistration = ({ casesList = [], setCasesList = () => {}, currentUser }) => {
  const currentYear = new Date().getFullYear().toString();
  
  const [caseData, setCaseData] = useState({
    caseNumber: '',
    caseYear: currentYear,
    clientName: '',
    caseDescription: '',
    caseCategory: '',
    opponentName: '',
    caseDegree: '',
    circleNumber: '',
    courtLocation: '', // هذا الحقل سيُستخدم مباشرة لإدخال اسم المحكمة
    caseStatus: 'قيد النظر',
    reportNumber: '',
    reportLocation: '',
    caseDate: '',
    originalCaseId: '',
    originalCaseDegree: '',
    originalCaseNumber: ''
  });

  const [fullCaseNumber, setFullCaseNumber] = useState('');
  const [isDegreeModified, setIsDegreeModified] = useState(false);
  const [loading, setLoading] = useState(false);
  const [error, setError] = useState(null);
  const [success, setSuccess] = useState(null);
  const [isTransferCase, setIsTransferCase] = useState(false);
  const navigate = useNavigate();

  // تحميل بيانات التحويل عند تحميل المكون
  useEffect(() => {
    const transferData = localStorage.getItem('transferData');
    if (transferData) {
      try {
        const parsedData = JSON.parse(transferData);
        setCaseData(prev => ({
          ...prev,
          clientName: parsedData.clientName || '',
          caseDescription: parsedData.caseDescription || '',
          opponentName: parsedData.opponentName || '',
          caseCategory: parsedData.caseCategory || '',
          courtLocation: parsedData.courtLocation || '',
          caseStatus: 'دعوى قضائية',
          originalCaseId: parsedData.originalCaseId || '',
          originalCaseDegree: parsedData.originalCaseDegree || '',
          originalCaseNumber: parsedData.originalCaseNumber || '',
          originalCourtLocation: parsedData.originalCourtLocation || '',
          originalCaseDate: parsedData.originalCaseDate || '',
          originalClientName: parsedData.originalClientName || '',
          originalOpponentName: parsedData.originalOpponentName || '',
          originalCaseDescription: parsedData.originalCaseDescription || '',
          originalCaseCategory: parsedData.originalCaseCategory || '',
          originalCircleNumber: parsedData.originalCircleNumber || ''
        }));
        setIsTransferCase(true);
        localStorage.removeItem('transferData');
      } catch (error) {
        console.error('خطأ في تحميل بيانات التحويل:', error);
      }
    }
  }, []);

  // تأثير لتحديث نوع الدعوى عند تغيير درجة الدعوى
  useEffect(() => {
    if (caseData.caseDegree && caseCategoriesByDegree[caseData.caseDegree]) {
      if (!caseCategoriesByDegree[caseData.caseDegree].includes(caseData.caseCategory)) {
        setCaseData(prev => ({
          ...prev,
          caseCategory: caseCategoriesByDegree[caseData.caseDegree][0]
        }));
      }
    }
  }, [caseData.caseDegree]);

  // تأثير لتحديث رقم القضية الكامل المعروض
  useEffect(() => {
    if (caseData.caseStatus === 'دعوى قضائية') {
      setFullCaseNumber(
        caseData.caseNumber ? `${caseData.caseNumber}/${caseData.caseYear}` : caseData.caseYear
      );
    } else if (caseData.caseStatus === 'محضر') {
      setFullCaseNumber(
        caseData.reportNumber ? `${caseData.reportNumber}/${caseData.caseYear}` : caseData.caseYear
      );
    } else {
      setFullCaseNumber('');
    }
  }, [caseData.caseNumber, caseData.caseYear, caseData.reportNumber, caseData.caseStatus]);

  // معالج تغيير المدخلات
  const handleChange = (e) => {
    const { name, value } = e.target;
    setCaseData(prev => ({ ...prev, [name]: value }));
    if (name === 'caseDegree') setIsDegreeModified(true);
  };

  // معالج إرسال النموذج
  const handleSubmit = async (e) => {
    e.preventDefault();
    setError(null);
    setSuccess(null);

    // التحقق الأساسي من وجود المستخدم
    if (!currentUser) {
      alert("يجب تسجيل الدخول لحفظ القضايا.");
      navigate('/login');
      return;
    }

    // التحقق من حالة المصادقة للحساب الأونلاين
    const activeAccount = getActiveAccount();
    if (activeAccount === 'online') {
      console.log('🔍 التحقق من حالة المصادقة للحساب الأونلاين...');
      console.log('👤 المستخدم الحالي:', currentUser);
      console.log('🆔 معرف المستخدم:', currentUser.uid);
      console.log('📧 الإيميل:', currentUser.email);
      console.log('✅ الإيميل مُفعّل:', currentUser.emailVerified);

      // التحقق من صحة التوكن
      try {
        const token = await currentUser.getIdToken(true); // force refresh
        console.log('🎫 تم تحديث التوكن بنجاح');
      } catch (tokenError) {
        console.error('❌ خطأ في الحصول على التوكن:', tokenError);
        setError('خطأ في المصادقة. يرجى تسجيل الدخول مرة أخرى.');
        return;
      }
    }

    if (!caseData.clientName.trim()) {
      setError('يجب إدخال اسم الموكل');
      return;
    }

    // توليد رقم القضية الكامل بناءً على حالتها
    let generatedFullCaseNumber = '';
    if (caseData.caseStatus === 'قيد النظر') {
      generatedFullCaseNumber = `قيد النظر-${Date.now()}`;
    } else if (caseData.caseStatus === 'محضر') {
      if (!caseData.reportNumber.trim() || !caseData.caseYear || !/^\d{4}$/.test(caseData.caseYear)) {
        setError('يجب إدخال رقم المحضر وسنة صحيحة (4 أرقام).');
        return;
      }
      generatedFullCaseNumber = `${caseData.reportNumber.trim()}/${caseData.caseYear}`;
    } else if (caseData.caseStatus === 'دعوى قضائية') {
      if (!caseData.caseNumber.trim() || !caseData.caseYear || !/^\d{4}$/.test(caseData.caseYear)) {
        setError('يجب إدخال رقم القضية وسنة صحيحة (4 أرقام).');
        return;
      }
      generatedFullCaseNumber = `${caseData.caseNumber.trim()}/${caseData.caseYear}`;
    }

    // التحقق من صحة الحقول الإلزامية بناءً على نوع القضية
    if (caseData.caseStatus === 'دعوى قضائية') {
      if (!caseData.circleNumber.trim()) { setError('يجب إدخال رقم الدائرة.'); return; }
      if (!caseData.caseDegree) { setError('يجب اختيار درجة الدعوى.'); return; }
      if (!caseData.caseCategory) { setError('يجب اختيار نوع الدعوى.'); return; }
      if (!caseData.courtLocation.trim()) { setError('يجب إدخال اسم المحكمة.'); return; }
      if (!caseData.caseDescription.trim()) { setError('يجب إدخال الوصف.'); return; }
      if (!caseData.caseDate.trim()) { setError('يجب إدخال تاريخ رفع الدعوى.'); return; }
    }

    if (caseData.caseStatus === 'محضر') {
      if (!caseData.reportLocation.trim()) { setError('يجب إدخال مكان الجهة المختصة.'); return; }
      if (!caseData.caseDescription.trim()) { setError('يجب إدخال وصف المحضر.'); return; }
      if (!caseData.caseDate.trim()) { setError('يجب إدخال تاريخ المحضر.'); return; }
    }

    if (caseData.caseStatus === 'قيد النظر') {
      if (!caseData.reportLocation.trim()) { setError('يجب إدخال مكان الجهة المختصة.'); return; }
      if (!caseData.caseDescription.trim()) { setError('يجب إدخال الوصف القضائي.'); return; }
    }

    if (!window.confirm('هل أنت متأكد من حفظ القضية؟')) {
      return;
    }

    const caseDataToSave = {
      fullCaseNumber: generatedFullCaseNumber,
      caseNumber: caseData.caseNumber.trim() || null,
      caseYear: caseData.caseYear || currentYear,
      clientName: caseData.clientName.trim(),
      opponentName: caseData.opponentName.trim() || null,
      caseDescription: caseData.caseDescription.trim() || null,
      caseCategory: caseData.caseCategory || null,
      caseDegree: caseData.caseDegree || null,
      courtLocation: caseData.courtLocation.trim() || null,
      circleNumber: caseData.circleNumber.trim() || null,
      caseDate: caseData.caseDate.trim() || null,
      caseStatus: caseData.caseStatus || 'قيد النظر',
      reportNumber: caseData.reportNumber.trim() || null,
      reportLocation: caseData.reportLocation.trim() || null,
      deferrals: [],
      actions: [],
      history: [],
      createdAt: new Date().toISOString(),
      updatedAt: new Date().toISOString(),
      userId: currentUser.uid,
      originalCaseId: caseData.originalCaseId || null,
      originalCaseDegree: caseData.originalCaseDegree || null,
      originalCaseNumber: caseData.originalCaseNumber || null,
      isTransferredCase: isTransferCase
    };

    console.log('Saving case data:', caseDataToSave);
    setLoading(true);
    try {
      const savedCase = await addCase(currentUser.uid, caseDataToSave);
      notifyCaseCreated(currentUser.uid);
      // إعادة تعيين النموذج
      setCaseData({
        caseNumber: '', caseYear: currentYear, clientName: '', caseDescription: '',
        caseCategory: '', opponentName: '', caseDegree: '', circleNumber: '',
        courtLocation: '', caseStatus: 'قيد النظر', reportNumber: '',
        reportLocation: '', caseDate: ''
      });
      setFullCaseNumber('');
      setIsDegreeModified(false);
      
      const activeAccount = getActiveAccount();
      if (isTransferCase) {
        setSuccess('تم تحويل الدرجة القضائية بنجاح!');
      } else if (activeAccount === 'online') {
        setSuccess('تم حفظ القضية بنجاح في الحساب الأونلاين!');
      } else {
        setSuccess('تم حفظ القضية بنجاح في الحساب المحلي!');
      }
      
      setTimeout(() => {
        navigate(`/case-details/${savedCase.id}`);
      }, 1500);

    } catch (e) {
      console.error('❌ خطأ في حفظ القضية:', e);

      let errorMessage = "حدث خطأ أثناء حفظ القضية: " + e.message;

      // تحليل نوع الخطأ وتقديم رسائل واضحة
      if (activeAccount === 'online') {
        if (e.code === 'permission-denied') {
          errorMessage = "ليس لديك صلاحية لحفظ البيانات. تحقق من حالة حسابك أو تواصل مع الدعم الفني.";
        } else if (e.code === 'unauthenticated') {
          errorMessage = "انتهت جلسة المصادقة. يرجى تسجيل الدخول مرة أخرى.";
          setTimeout(() => navigate('/login'), 2000);
        } else if (e.code === 'network-request-failed' || e.message.includes('network')) {
          errorMessage = "خطأ في الاتصال بالإنترنت. تحقق من اتصالك وحاول مرة أخرى.";
        } else if (e.message.includes('Firebase') || e.message.includes('Firestore')) {
          errorMessage = "خطأ في الخدمة السحابية. يرجى المحاولة مرة أخرى لاحقاً.";
        }
      }

      setError(errorMessage);
    } finally {
      setLoading(false);
    }
  };

  const handleCancel = () => {
    navigate('/dashboard', { state: { refresh: true } });
  };

  return (
    <div className="case-registration-page">
      <TopBar currentUser={currentUser} casesList={casesList} />
      <div className="main-container">
        <div className="registration-card">
          <div className="card-header">
            <div className="header-content">
              <h1>{isTransferCase ? 'تحويل درجة قضائية' : 'تسجيل قضية جديدة'}</h1>
              {isTransferCase && (
                <div className="transfer-badge">
                  <span>تحويل من: {caseData.originalCaseDegree} - رقم: {caseData.originalCaseNumber}</span>
                </div>
              )}
              {/* عرض نوع الحساب النشط */}
              <div style={{
                fontSize: '14px',
                color: getActiveAccount() === 'online' ? '#2196F3' : '#FF9800',
                marginTop: '8px',
                fontWeight: 'bold'
              }}>
                {getActiveAccount() === 'online' ? '🌐 الحساب السحابي (أونلاين)' : '💾 الحساب المحلي (أوفلاين)'}
              </div>
            </div>
          </div>
          {error && (
            <div className="message error-message">
              <div className="message-icon">⚠️</div>
              <div className="message-text">{error}</div>
            </div>
          )}
          {success && (
            <div className="message success-message">
              <div className="message-icon">✅</div>
              <div className="message-text">{success}</div>
            </div>
          )}
          {loading && (
            <div className="message loading-message">
              <div className="loading-spinner"></div>
              <div className="message-text">جاري حفظ القضية...</div>
            </div>
          )}
          <form className="registration-form" onSubmit={handleSubmit}>
            <div className="form-body">
              {/* الصف الأول: البيانات الأساسية */}
              <div className="input-row">
                <div className="input-field">
                  <label className="label">نوع القضية</label>
                  <select name="caseStatus" className="input" value={caseData.caseStatus} onChange={(e) => setCaseData(prev => ({ ...prev, caseStatus: e.target.value }))} required>
                    <option value="قيد النظر">⏳ قيد النظر</option>
                    <option value="محضر">📋 محضر</option>
                    <option value="دعوى قضائية">⚖️ دعوى قضائية</option>
                  </select>
                </div>
                <div className="input-field">
                  <label className="label">اسم الموكل</label>
                  <input type="text" name="clientName" className="input" value={caseData.clientName} onChange={handleChange} required placeholder="اسم الموكل *"/>
                </div>
                <div className="input-field">
                  <label className="label">اسم الخصم</label>
                  <input type="text" name="opponentName" className="input" value={caseData.opponentName} onChange={handleChange} placeholder="اسم الخصم"/>
                </div>
              </div>
              {/* الصف الثاني: أرقام القضايا والتواريخ */}
              <div className="input-row">
                {caseData.caseStatus === 'دعوى قضائية' && (
                  <div className="input-field">
                    <label className="label">رقم القضية/السنة</label>
                    <div className="number-inputs">
                      <input type="text" name="caseNumber" value={caseData.caseNumber} onChange={handleChange} placeholder="رقم القضية" className="input"/>
                      <span className="separator">/</span>
                      <input type="text" name="caseYear" value={caseData.caseYear} onChange={handleChange} placeholder="السنة" className="input"/>
                    </div>
                  </div>
                )}
                {caseData.caseStatus === 'محضر' && (
                  <div className="input-field">
                    <label className="label">رقم المحضر/السنة</label>
                    <div className="number-inputs">
                      <input type="text" name="reportNumber" value={caseData.reportNumber} onChange={handleChange} placeholder="رقم المحضر" className="input"/>
                      <span className="separator">/</span>
                      <input type="text" name="caseYear" value={caseData.caseYear} onChange={handleChange} placeholder="السنة" className="input"/>
                    </div>
                  </div>
                )}
                {caseData.caseStatus === 'دعوى قضائية' && (
                  <div className="input-field">
                    <label className="label">تاريخ رفع الدعوى</label>
                    <input type="date" name="caseDate" className="input" value={caseData.caseDate} onChange={handleChange} required/>
                  </div>
                )}
                {caseData.caseStatus === 'محضر' && (
                  <div className="input-field">
                    <label className="label">تاريخ المحضر</label>
                    <input type="date" name="caseDate" className="input" value={caseData.caseDate} onChange={handleChange} required/>
                  </div>
                )}
                {(caseData.caseStatus === 'محضر' || caseData.caseStatus === 'قيد النظر') && (
                  <div className="input-field">
                    <label className="label">مكان الجهة المختصة</label>
                    <input type="text" name="reportLocation" className="input" placeholder="مكان الجهة المختصة *" value={caseData.reportLocation} onChange={handleChange} required/>
                  </div>
                )}
              </div>
              {/* الصف الثالث: بيانات المحكمة (للدعاوى القضائية فقط) */}
              {caseData.caseStatus === 'دعوى قضائية' && (
                <div className="input-row">
                  <div className="input-field">
                    <label className="label">المحكمة</label>
                    <input type="text" name="courtLocation" className="input" placeholder="اكتب اسم المحكمة *" value={caseData.courtLocation} onChange={handleChange} required/>
                  </div>
                  <div className="input-field">
                    <label className="label">رقم الدائرة</label>
                    <input type="text" name="circleNumber" className="input" placeholder="رقم الدائرة *" value={caseData.circleNumber} onChange={handleChange} required/>
                  </div>
                  <div className="input-field">
                    <label className="label">درجة الدعوى</label>
                    <select name="caseDegree" className="input" value={caseData.caseDegree} onChange={handleChange} required>
                      <option value="">اختر درجة الدعوى</option>
                      {caseDegrees.map(degree => (
                        <option key={degree} value={degree}>{degree}</option>
                      ))}
                    </select>
                  </div>
                </div>
              )}
              {/* الصف الرابع: نوع الدعوى + وصف القضية */}
              <div className="input-row">
                {caseData.caseStatus === 'دعوى قضائية' && (
                  <div className="input-field">
                    <label className="label">نوع الدعوى</label>
                    <select name="caseCategory" className="input" value={caseData.caseCategory} onChange={handleChange} disabled={!caseData.caseDegree} required>
                      <option value="">اختر نوع الدعوى</option>
                      {caseData.caseDegree && caseCategoriesByDegree[caseData.caseDegree]?.map(category => (
                        <option key={category} value={category}>{category}</option>
                      ))}
                    </select>
                  </div>
                )}
                <div className={caseData.caseStatus === 'دعوى قضائية' ? "input-field" : "input-field full-width"}>
                  <label className="label">وصف القضية</label>
                  <input type="text" name="caseDescription" className="input" value={caseData.caseDescription} onChange={handleChange} required placeholder="وصف القضية *"/>
                </div>
              </div>
            </div>
            <div className="final-buttons">
              <button type="submit" className="submit-btn" disabled={loading} style={{maxWidth:'150px'}}>
                {loading ? 'جاري الحفظ...' : 'تسجيل القضية'}
              </button>
              <button type="button" onClick={handleCancel} className="cancel-btn" style={{maxWidth:'150px'}}>
                إلغاء
              </button>
            </div>
          </form>
        </div>
      </div>
    </div>
  );
};

export default CaseRegistration;
