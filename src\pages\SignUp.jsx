import React, { useState, useEffect } from 'react';
import { useNavigate } from 'react-router-dom';
import './SignUp.css';
import { auth, db } from '../config/firebaseConfig';
import LoadingSpinner from '../components/ui/LoadingSpinner';
import { createUserWithEmailAndPassword, updateProfile, sendEmailVerification } from "firebase/auth";
import { collection, doc, setDoc, query, where, getDocs, getDoc } from "firebase/firestore";
import { checkSignupLockout, recordSignupFailure, clearSignupLockout } from '../utils/LockoutManager';

// إضافة الثوابت المفقودة
const MAX_ATTEMPTS = 5;
const LOCKOUT_DURATION = 15 * 60 * 1000; // 15 دقيقة

function SignUp() {
  const [username, setUsername] = useState('');
  const [email, setEmail] = useState('');
  const [password, setPassword] = useState('');
  const [phone, setPhone] = useState('');
  const [errorMessage, setErrorMessage] = useState('');
  const [successMessage, setSuccessMessage] = useState('');
  const [loading, setLoading] = useState(false);
  const [signupAttempts, setSignupAttempts] = useState(0);
  const [currentStep, setCurrentStep] = useState(1);
  const [completedSteps, setCompletedSteps] = useState([]);
  const navigate = useNavigate();

  useEffect(() => {
    const lockoutStatus = checkSignupLockout();
    if (lockoutStatus.isLocked) {
      setErrorMessage(lockoutStatus.message);
      setLoading(true);
    } else {
      setSignupAttempts(lockoutStatus.attempts);
    }
  }, []);

  // دوال التعامل مع الخطوات
  const nextStep = () => {
    if (currentStep < 4) {
      setCompletedSteps([...completedSteps, currentStep]);
      setCurrentStep(currentStep + 1);
    }
  };

  const prevStep = () => {
    if (currentStep > 1) {
      setCurrentStep(currentStep - 1);
    }
  };

  const isStepValid = (step) => {
    switch (step) {
      case 1:
        const trimmedUsername = username.trim();
        // الاسم: 12 حرف ورقم كحد أقصى (أحرف وأرقام عربية أو إنجليزية فقط)
        if (trimmedUsername.length === 0 || trimmedUsername.length > 15) {
          return false;
        }
        return /^[a-zA-Z0-9\u0600-\u06FF ]+$/.test(trimmedUsername);
      case 2:
        return email.trim().length > 0 && email.includes('@');
      case 3:
        // كلمة المرور: كابتل، أرقام، سمول، 7-15 حرف، حروف وأرقام فقط
        const passwordRegex = /^(?=.*[a-z])(?=.*[A-Z])(?=.*\d)[a-zA-Z0-9\u0600-\u06FF ]{7,15}$/;
        return passwordRegex.test(password);
        // رقم الهاتف: أرقام فقط، 11 رقم كحد أقصى (اختياري)
        if (phone.trim() === '') return true; // اختياري
        return /^[0-9]{1,11}$/.test(phone);
      default:
        return false;
    }
  };

  const handleStepSubmit = (e) => {
    e.preventDefault();
    if (currentStep === 4) {
      handleSubmit(e);
    } else if (isStepValid(currentStep)) {
      nextStep();
    } else {
      setErrorMessage(getStepErrorMessage(currentStep));
    }
  };

  const getStepErrorMessage = (step) => {
    switch (step) {
      case 1:
        return 'اسم المستخدم يجب أن يتكون من 15 حرفًا أو رقمًا كحد أقصى (أحرف وأرقام عربية أو إنجليزية فقط).';
      case 2:
        return 'يرجى إدخال بريد إلكتروني صحيح.';
      case 3:
        return 'كلمة المرور يجب أن تتكون من 7 إلى 15 حرفًا، وتتضمن حروفًا كبيرة وصغيرة وأرقامًا فقط.';
      case 4:
        return 'رقم الهاتف يجب أن يتكون من أرقام فقط وبحد أقصى 11 رقمًا.';
      default:
        return '';
    }
  };

  const handleSubmit = async (event) => {
    event.preventDefault();
    setErrorMessage('');
    setSuccessMessage('');
    setLoading(true);

    if (!email.trim()) {
      setErrorMessage('يرجى إدخال البريد الإلكتروني.');
      setLoading(false);
      return;
    }

    if (!password.trim()) {
      setErrorMessage('يرجى إدخال كلمة المرور.');
      setLoading(false);
      return;
    }

    // التحقق من حالة القفل قبل المحاولة
    const lockoutStatus = checkSignupLockout();
    if (lockoutStatus.isLocked) {
      setErrorMessage(lockoutStatus.message);
      setLoading(false);
      return;
    }

    // التحقق مما إذا كان اسم المستخدم موجودًا بالفعل
    try {
      // محاولة التحقق من مجموعة usernames أولاً
      const usernameDoc = await getDoc(doc(db, 'usernames', username.trim().toLowerCase()));
      if (usernameDoc.exists()) {
        setErrorMessage('اسم المستخدم هذا موجود بالفعل. يرجى اختيار اسم مستخدم آخر.');
        setLoading(false);
        return;
      }
    } catch (error) {
      console.error('خطأ في التحقق من اسم المستخدم من مجموعة usernames:', error);
      
      // خطة احتياطية: التحقق من مجموعة users
      try {
        const usersRef = collection(db, 'users');
        const q = query(usersRef, where('username', '==', username.trim()));
        const querySnapshot = await getDocs(q);
        
        if (!querySnapshot.empty) {
          setErrorMessage('اسم المستخدم هذا موجود بالفعل. يرجى اختيار اسم مستخدم آخر.');
          setLoading(false);
          return;
        }
      } catch (fallbackError) {
        console.error('خطأ في التحقق من اسم المستخدم من مجموعة users:', fallbackError);
        // يمكن المتابعة حتى لو فشل التحقق
      }
    }

    try {
      const userCredential = await createUserWithEmailAndPassword(auth, email.trim(), password);
      const user = userCredential.user;

      console.log('تم إنشاء حساب بنجاح:', user);

      if (username.trim()) {
        await updateProfile(user, { displayName: username.trim() });
        console.log('تم تحديث اسم المستخدم:', username.trim());
      }

      await sendEmailVerification(user);
      console.log('تم إرسال بريد التحقق.');

      // حفظ بيانات المستخدم في Firestore
      try {
        await setDoc(doc(db, 'users', user.uid), {
          name: username.trim() || '',
          username: username.trim() || null,
          email: user.email,
          phone: phone.trim() || null,
          role: 'محامي',
          createdAt: new Date().toISOString(),
          trialStartDate: new Date().toISOString(),
          subscriptionStatus: 'trial',
          emailVerified: false, // إضافة حقل للتحقق من الايميل
        });

        // حفظ اسم المستخدم في مجموعة usernames لمنع التكرار
        if (username.trim()) {
          try {
            await setDoc(doc(db, 'usernames', username.trim().toLowerCase()), {
              uid: user.uid,
              username: username.trim(),
              createdAt: new Date().toISOString(),
            });
          } catch (usernameError) {
            console.error('خطأ في حفظ اسم المستخدم في مجموعة usernames:', usernameError);
            // لا نوقف العملية إذا فشل حفظ اسم المستخدم
          }
        }

        console.log('تم حفظ بيانات المستخدم في Firestore');
        
        // مسح بيانات القفل عند النجاح
        clearSignupLockout();
        setSignupAttempts(0);
        setSuccessMessage('تم إنشاء الحساب بنجاح! يرجى التحقق من بريدك الإلكتروني لتفعيل حسابك قبل تسجيل الدخول.');
        navigate('/verify-email'); // توجيه لصفحة التحقق
        
      } catch (firestoreError) {
        console.error('خطأ في حفظ بيانات المستخدم في Firestore:', firestoreError);
        // حتى لو فشل حفظ البيانات في Firestore، الحساب تم إنشاؤه في Auth
        // يمكن للمستخدم المحاولة مرة أخرى لاحقاً
        
        // مسح بيانات القفل عند النجاح (حتى لو فشل Firestore)
        clearSignupLockout();
        setSignupAttempts(0);
        setSuccessMessage('تم إنشاء الحساب بنجاح! يرجى التحقق من بريدك الإلكتروني لتفعيل حسابك. قد تحتاج لإكمال الملف الشخصي لاحقاً.');
        navigate('/verify-email'); // توجيه لصفحة التحقق
      }

    } catch (error) {
      console.error('خطأ في إنشاء الحساب:', error.code, error.message);

      // تسجيل المحاولة الفاشلة
      const failureResult = recordSignupFailure(signupAttempts);
      setSignupAttempts(failureResult.attempts);

      if (failureResult.isLocked) {
        setErrorMessage(failureResult.message);
        setLoading(true);
        return;
      }

      let userFacingMessage = 'حدث خطأ أثناء إنشاء الحساب. يرجى المحاولة مرة أخرى.';
      switch (error.code) {
        case 'auth/email-already-in-use':
          userFacingMessage = 'البريد الإلكتروني هذا مستخدم بالفعل. يرجى استخدام بريد إلكتروني آخر أو تسجيل الدخول.';
          break;
        case 'auth/invalid-email':
          userFacingMessage = 'صيغة البريد الإلكتروني غير صحيحة. يرجى إدخال بريد إلكتروني صالح.';
          break;
        case 'auth/weak-password':
          userFacingMessage = 'كلمة المرور ضعيفة جدًا. يجب أن تحتوي على 6 أحرف على الأقل.';
          break;
        case 'auth/too-many-requests':
          userFacingMessage = 'تم حظر إنشاء الحساب مؤقتًا بسبب كثرة المحاولات. يرجى المحاولة لاحقًا.';
          break;
        default:
          userFacingMessage = 'حدث خطأ غير متوقع. يرجى التحقق من اتصال الإنترنت والمحاولة لاحقًا.';
      }
      setErrorMessage(userFacingMessage);
    } finally {
      setLoading(false);
    }
  };

  const handleBack = () => {
    navigate('/login');
  };

  return (
    <div className="signup-container">
      <div className="logo-section">
        <img
          src="/logo.png"
          alt="الأجندة القضائية"
          className="legal-agenda-logo"
        />
      </div>

      <div className="header-form">
        <span className="title">مرحباً بك!</span>
        <span className="subtitle">اختر خياراً للمتابعة</span>
      </div>

      {/* مؤشر الخطوات */}
      <div className="steps-indicator">
        {[1, 2, 3, 4].map((step) => (
          <div
            key={step}
            className={`step-dot ${
              completedSteps.includes(step) ? 'completed' :
              currentStep === step ? 'active' : 'pending'
            }`}
          >
            {completedSteps.includes(step) ? '✓' : step}
          </div>
        ))}
      </div>

      {errorMessage && <div className="error-message">{errorMessage}</div>}
      {successMessage && <div className="success-message">{successMessage}</div>}
      
      {loading && <LoadingSpinner message="جاري إنشاء الحساب..." />}

      <form id="signup-form" onSubmit={handleStepSubmit}>
        {/* الخطوة الأولى: اسم المستخدم */}
        {currentStep === 1 && (
          <div className="step-container active">
            <h3 className="step-title">الخطوة 1: اسم المستخدم</h3>
            <input
              type="text"
              id="signup-username"
              name="username"
              value={username}
              onChange={(e) => {
                const value = e.target.value;
                if (value.length <= 15) {
                  setUsername(value.replace(/[^a-zA-Z0-9\u0600-\u06FF ]/g, ''));
                }
              }}
              disabled={loading}
              placeholder="اسم المستخدم"
              autoComplete="username"
              autoFocus
              required={true}
            />
            <span className="input-requirements">
              (15 حرفًا كحد أقصى، أحرف وأرقام عربية أو إنجليزية، ويمكن أن تحتوي على مسافات)
            </span>
          </div>
        )}

        {/* الخطوة الثانية: البريد الإلكتروني */}
        {currentStep === 2 && (
          <div className="step-container active">
            <h3 className="step-title">الخطوة 2: البريد الإلكتروني</h3>
            <input
              type="email"
              id="signup-email"
              name="email"
              value={email}
              onChange={(e) => setEmail(e.target.value)}
              required={true}
              disabled={loading}
              placeholder="البريد الإلكتروني"
              autoComplete="email"
              autoFocus
            />
            <span className="input-requirements">
              (مثال: <EMAIL>)
            </span>
          </div>
        )}

        {/* الخطوة الثالثة: كلمة المرور */}
        {currentStep === 3 && (
          <div className="step-container active">
            <h3 className="step-title">الخطوة 3: كلمة المرور</h3>
            <input
              type="password"
              id="signup-password"
              name="password"
              value={password}
              onChange={(e) => {
                const value = e.target.value;
                // السماح فقط بالحروف الإنجليزية والعربية والأرقام والمسافات
                const filteredValue = value.replace(/[^a-zA-Z0-9\u0600-\u06FF ]/g, '');
                // تقييد الطول بحد أقصى 15 حرفًا
                if (filteredValue.length <= 15) {
                  setPassword(filteredValue);
                }
              }}
              required={true}
              disabled={loading}
              placeholder="كلمة المرور"
              autoComplete="new-password"
              autoFocus
            />
            <span className="password-requirements">
              (7-15 حرفًا، تتضمن حروفًا كبيرة وصغيرة وأرقامًا، ويمكن أن تحتوي على مسافات)
            </span>
          </div>
        )}

        {/* الخطوة الرابعة: رقم الهاتف */}
        {currentStep === 4 && (
          <div className="step-container active">
            <h3 className="step-title">الخطوة 4: رقم الهاتف (اختياري)</h3>
            <input
              type="tel"
              id="signup-phone"
              name="phone"
              value={phone}
              onChange={(e) => {
                const value = e.target.value;
                if (value.length <= 11) {
                  setPhone(value);
                }
              }}
              disabled={loading}
              placeholder="رقم الهاتف (اختياري)"
              autoComplete="tel"
              autoFocus
            />
            <span className="input-requirements">
              (اختياري، 11 رقمًا كحد أقصى)
            </span>
          </div>
        )}

        <button type="submit" disabled={loading || (currentStep < 4 && !isStepValid(currentStep))}>
          {loading ? 'جاري المعالجة...' :
           currentStep === 4 ? 'إنشاء حساب' : 'متابعة'}
        </button>
      </form>
      
      <div className="button-container">
        {currentStep > 1 && (
          <button
            type="button"
            onClick={prevStep}
            className="prev-btn"
            disabled={loading}
          >
            السابق
          </button>
        )}
      </div>
      
      <div className="signup-link">
        <a href="/login" className="create-account-link">
          لديك حساب بالفعل؟ تسجيل الدخول
        </a>
      </div>
    </div>
  );
}

export default SignUp;