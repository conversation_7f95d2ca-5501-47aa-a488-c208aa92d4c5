import React, { useState, useRef } from 'react';
import { FaCamera, FaSave, FaTimes, FaArrowRight, FaUser } from 'react-icons/fa';
import { useNavigate } from 'react-router-dom';
import TopBar from '../topbar/TopBar';
import styles from './ProfilePage.module.css';
import { db } from '../../config/firebaseConfig';
import { doc, updateDoc } from 'firebase/firestore';
import { getActiveAccount } from '../../services/StorageService';

const ChangePhotoPage = ({ currentUser }) => {
  const [selectedImage, setSelectedImage] = useState(null);
  const [previewUrl, setPreviewUrl] = useState(null);
  const [loading, setLoading] = useState(false);
  const [error, setError] = useState(null);
  const fileInputRef = useRef(null);
  const navigate = useNavigate();
  const activeAccount = getActiveAccount();

  const handleFileSelect = (e) => {
    if (!e.target.files || !e.target.files[0]) return;
    
    const file = e.target.files[0];
    setSelectedImage(file);
    
    // إنشاء معاينة للصورة
    const reader = new FileReader();
    reader.onload = (e) => {
      setPreviewUrl(e.target.result);
    };
    reader.readAsDataURL(file);
  };

  const handleSave = async () => {
    if (!selectedImage) {
      setError('يرجى اختيار صورة أولاً');
      return;
    }

    try {
      setLoading(true);
      setError(null);
      
      // تحويل الصورة إلى Data URL
      const reader = new FileReader();
      reader.onload = async (event) => {
        if (event.target) {
          const photoURL = event.target.result;
          
          if (activeAccount === 'online') {
            // تحديث بيانات المستخدم في Firestore
            try {
              const userRef = doc(db, 'users', currentUser.uid);
              await updateDoc(userRef, {
                photoURL,
                lastUpdateTime: new Date().toISOString()
              });
              
              alert('تم تحديث صورة الملف الشخصي بنجاح. ستتم مزامنتها مع الحساب الأونلاين!');
              navigate('/profile');
            } catch (error) {
              console.error('خطأ في تحديث الصورة في Firestore:', error);
              setError('حدث خطأ في تحديث الصورة. يرجى المحاولة مرة أخرى.');
            }
          } else {
            // تحديث بيانات المستخدم المحلية
            const localData = localStorage.getItem('localUserData_' + currentUser.uid);
            const parsedLocalData = localData ? JSON.parse(localData) : {};
            
            const updatedLocalData = {
              ...parsedLocalData,
              uid: currentUser.uid,
              photoURL,
              lastUpdatedAt: new Date().toISOString()
            };
            
            localStorage.setItem('localUserData_' + currentUser.uid, JSON.stringify(updatedLocalData));
            alert('تم تحديث صورة الملف الشخصي بنجاح في الحساب المحلي.');
            navigate('/profile');
          }
          setLoading(false);
        }
      };
      
      reader.onerror = () => {
        setError('حدث خطأ في قراءة الملف. يرجى المحاولة مرة أخرى.');
        setLoading(false);
      };
      
      reader.readAsDataURL(selectedImage);
    } catch (e) {
      setError('خطأ في تحديث صورة الملف الشخصي: ' + e.message);
      setLoading(false);
    }
  };

  const handleCancel = () => {
    navigate('/profile');
  };

  return (
    <div className={styles.pageWrapper}>
      <TopBar currentUser={currentUser} />
      <div className={styles.mainContainer}>
        <div className={styles.accountManagementSection}>
          <div className={styles.sidebarNavItem} style={{ visibility: 'hidden' }}>
            <div className={styles.sidebarNavIcon}><FaUser /></div>
            <span>المعلومات الشخصية</span>
          </div>
        </div>
        
        <div className={styles.mainContentArea}>
          <div className={styles.pageHeader}>
            <h2 className={styles.pageTitle}>تغيير صورة الملف الشخصي</h2>
          </div>
          
          {error && <div className={styles.errorMessage}>{error}</div>}
          
          <div className={styles.personalInfoSection}>
            <div className={styles.photoUploadContainer}>
              <div className={styles.photoPreviewArea}>
                {previewUrl ? (
                  <img 
                    src={previewUrl} 
                    alt="معاينة الصورة" 
                    className={styles.photoPreview} 
                  />
                ) : (
                  <div className={styles.photoPlaceholder}>
                    <FaCamera size={40} />
                    <p>اختر صورة</p>
                  </div>
                )}
              </div>
              
              <input 
                type="file" 
                ref={fileInputRef}
                style={{ display: 'none' }}
                accept="image/*"
                onChange={handleFileSelect}
              />
              
              <div className={styles.photoActions}>
                <button 
                  className={styles.selectPhotoButton}
                  onClick={() => fileInputRef.current?.click()}
                >
                  اختيار صورة
                </button>
              </div>
              
              <div className={styles.buttonRow}>
                <button 
                  onClick={handleSave} 
                  className={styles.saveButton}
                  disabled={loading || !selectedImage}
                >
                  <FaSave /> حفظ
                </button>
                <button 
                  onClick={handleCancel} 
                  className={styles.cancelButton}
                >
                  <FaTimes /> إلغاء
                </button>
              </div>
            </div>
          </div>
        </div>
      </div>
    </div>
  );
};

export default ChangePhotoPage;