import{r as u,j as t}from"./index-HUlFBKIW.js";const U="_pageWrapper_1agn9_4",X="_mainContainer_1agn9_12",V="_noEvents_1agn9_26",q="_radarContainer_1agn9_38",J="_radarSvg_1agn9_49",K="_radarRingCircle_1agn9_59",Q="_centerGroup_1agn9_67",Z="_centerLabelSvg_1agn9_68",I="_eventGroup_1agn9_77",ee="_eventDotCircle_1agn9_79",te="_clientNameText_1agn9_104",ne="_connectionLine_1agn9_115",ie="_simpleTooltip_1agn9_122",oe="_visible_1agn9_139",f={pageWrapper:U,mainContainer:X,noEvents:V,radarContainer:q,radarSvg:J,radarRingCircle:K,centerGroup:Q,centerLabelSvg:Z,eventGroup:I,eventDotCircle:ee,clientNameText:te,connectionLine:ne,simpleTooltip:ie,visible:oe},k=(y=new Set)=>{const g=["#1976d2","#f06292","#4caf50","#ff9800","#ab47bc","#26c6da","#ef5350","#ffca28"];let x;if(y.size>=g.length){const a=g.filter(D=>!y.has(D));a.length>0?x=a[Math.floor(Math.random()*a.length)]:x=g[Math.floor(Math.random()*g.length)]}else do x=g[Math.floor(Math.random()*g.length)];while(y.has(x));return y.add(x),x},se=({casesList:y})=>{const[g,x]=u.useState([]),[a,D]=u.useState({visible:!1,event:null,position:{top:0,left:0}}),[L,W]=u.useState({width:800,height:700}),b=u.useRef(null),_=u.useRef(null),B=u.useRef(null),G=new Date;u.useEffect(()=>{const s=()=>{if(_.current){const c=_.current.offsetWidth,e=window.innerWidth;let i,d;e<=480?(i=c*1.3,d=i*1.4):e<=768?(i=c*1.1,d=i*1.2):(i=Math.min(c,800),d=i*.875),W({width:i,height:d})}},l=setTimeout(()=>{s()},100);return window.addEventListener("resize",s),window.addEventListener("load",s),s(),()=>{clearTimeout(l),window.removeEventListener("resize",s),window.removeEventListener("load",s)}},[]);const A=u.useMemo(()=>{const s=[],l=new Map,c=new Set;return y.forEach(e=>{const i=e.fullCaseNumber||"غير محدد",d=e.clientName||"غير محدد";if(e.lastReport&&e.lastReportDate&&!isNaN(new Date(e.lastReportDate).getTime())){const o=`${i}-defer-${e.id}`,r=l.get(o)||k(new Set([...Array.from(l.values()),...Array.from(c)]));l.set(o,r),s.push({type:"تأجيل",date:new Date(e.lastReportDate).toISOString().split("T")[0],reasons:e.lastReport,clientName:d,caseNumber:i,courtLocation:e.courtLocation||"غير محدد",eventId:o,color:r,positionAngle:Math.random()*360})}if(e.lastAction&&e.lastActionDate&&!isNaN(new Date(e.lastActionDate).getTime())){const o=`${i}-action-${e.id}`;let r;if(e.linkedReport){const h=`${i}-defer-${e.id}`,w=s.find(j=>j.eventId===h&&j.type==="تأجيل");w&&(r=w.color,l.set(o,r))}r||(r=k(new Set([...Array.from(l.values()),...Array.from(c)])),c.add(r),l.set(o,r)),s.push({type:"إجراء",description:e.lastAction,deadline:new Date(e.lastActionDate).toISOString().split("T")[0],priority:"عادي",timestamp:e.lastActionDate,clientName:d,caseNumber:i,courtLocation:e.courtLocation||"غير محدد",eventId:o,color:r,linkedDeferralId:e.linkedReport?`${i}-defer-${e.id}`:"",linkedActionId:"",positionAngle:Math.random()*360})}}),s.sort((e,i)=>{const d=new Date(e.type==="تأجيل"?e.date:e.deadline),o=new Date(i.type==="تأجيل"?i.date:i.deadline);return d-o})},[y]);u.useEffect(()=>{x(A),D({visible:!1,event:null,position:{top:0,left:0}})},[A]);const Y=(s,l)=>{var w;if(!l||!b.current||!_.current)return;if(a.visible&&((w=a.event)==null?void 0:w.eventId)===s.eventId){D({visible:!1,event:null,position:{top:0,left:0}});return}const c=b.current.getBoundingClientRect(),e=_.current.getBoundingClientRect(),i=l.getBoundingClientRect();let d=(i.left+i.right)/2-e.left,o=(i.top+i.bottom)/2-e.top;const r=200,h=100;d+r>c.width&&(d=c.width-r-10),d<0&&(d=10),o+h>c.height&&(o=i.top-e.top-h-10),o<0&&(o=i.bottom-e.top+10),o<0&&(o=10),o+h>c.height&&(o=c.height-h-10),D({visible:!0,event:s,position:{left:Math.max(10,Math.min(d,c.width-r-10)),top:Math.max(10,Math.min(o,c.height-h-10))}})},F=()=>{D({visible:!1,event:null,position:{top:0,left:0}})},O=()=>{const{width:s,height:l}=L,c=`0 0 ${s} ${l}`,e=s/2,i=l/2+30,o=Math.min(s,l)/2- -60,r=10,h=o/(r+.5),w=1,j=g.map(n=>{const p=new Date(n.type==="تأجيل"?n.date:n.deadline),v=new Date(G),M=new Date(v.getFullYear(),v.getMonth(),v.getDate()),m=new Date(p.getFullYear(),p.getMonth(),p.getDate())-M,R=Math.floor(m/(1e3*60*60*24))+w;return{...n,daysDiff:R,eventDate:p}}).filter(n=>Math.abs(n.daysDiff)<=r),N=new Map,z=t.jsx("defs",{children:t.jsxs("pattern",{id:"dots-pattern",x:"0",y:"0",width:"50",height:"50",patternUnits:"userSpaceOnUse",children:[t.jsx("rect",{x:"0",y:"0",width:"50",height:"50",fill:"transparent"}),t.jsx("circle",{cx:"10",cy:"10",r:"1",fill:"#aaaaaa",opacity:"0.4"}),t.jsx("circle",{cx:"40",cy:"15",r:"1",fill:"#aaaaaa",opacity:"0.4"}),t.jsx("circle",{cx:"20",cy:"35",r:"1",fill:"#aaaaaa",opacity:"0.4"}),t.jsx("circle",{cx:"45",cy:"40",r:"1",fill:"#aaaaaa",opacity:"0.4"})]})},"dot-pattern"),H=t.jsx("g",{children:t.jsx("rect",{x:"0",y:"0",width:s,height:l,fill:"url(#dots-pattern)"})},"bg-group"),E=[];for(let n=-10;n<=r;n++){if(n===0)continue;const p=Math.abs(n)*h;E.push(t.jsx("circle",{cx:e,cy:i,r:p,className:f.radarRingCircle},`ring-${n}`))}const T=[];j.forEach(n=>{const p=n.daysDiff,v=Math.abs(p)*h,C=n.positionAngle*Math.PI/180,m=e+v*Math.cos(C),S=i+v*Math.sin(C);N.set(n.eventId,{x:m,y:S});const R=n.type==="تأجيل"?8:6;T.push(t.jsxs("g",{className:f.eventGroup,transform:`translate(${m}, ${S})`,children:[t.jsx("circle",{cx:"0",cy:"0",r:R,fill:n.color,className:f.eventDotCircle,onClick:P=>Y(n,P.target)}),t.jsx("text",{x:"0",y:-(R+5),className:f.clientNameText,children:n.clientName})]},n.eventId))});const $=[];return j.forEach(n=>{const p=N.get(n.eventId);if(!p)return;const v=(M,C)=>{const m=N.get(M);m&&$.push(t.jsx("line",{x1:p.x,y1:p.y,x2:m.x,y2:m.y,stroke:n.color,className:f.connectionLine},`${C}-${n.eventId}`))};n.type==="إجراء"&&n.linkedDeferralId&&v(n.linkedDeferralId,"conn-deferral"),n.type==="إجراء"&&n.linkedActionId&&v(n.linkedActionId,"conn-action")}),t.jsxs("div",{className:f.radarContainer,ref:_,style:{backgroundColor:"transparent"},children:[t.jsxs("svg",{ref:b,width:"100%",height:l,viewBox:c,preserveAspectRatio:"xMidYMid meet",className:f.radarSvg,children:[z,H,t.jsx("g",{children:$}),t.jsx("g",{children:E}),t.jsx("g",{transform:`translate(${e}, ${i})`,className:f.centerGroup,children:t.jsx("text",{x:"0",y:"0",dominantBaseline:"middle",textAnchor:"middle",className:f.centerLabelSvg,children:"اليوم"})}),t.jsx("g",{children:T})]}),a.visible&&a.event&&t.jsx("div",{ref:B,className:`${f.simpleTooltip} ${f.visible}`,style:{position:"absolute",left:`${a.position.left}px`,top:`${a.position.top}px`,backgroundColor:a.event.color},onClick:F,children:a.event.type==="تأجيل"?t.jsxs(t.Fragment,{children:[t.jsxs("div",{children:[a.event.clientName," (",a.event.caseNumber,")"]}),t.jsx("div",{children:a.event.reasons}),t.jsxs("div",{children:["التاريخ: ",a.event.date]})]}):t.jsxs(t.Fragment,{children:[t.jsxs("div",{children:[a.event.clientName," (",a.event.caseNumber,")"]}),t.jsx("div",{children:a.event.description}),t.jsxs("div",{children:["الموعد: ",a.event.deadline]})]})})]})};return t.jsx("div",{className:f.pageWrapper,children:t.jsx("div",{className:f.mainContainer,children:g.length>0?O():t.jsx("div",{className:f.noEvents,children:"لا توجد أحداث لعرضها ضمن النطاق الزمني المحدد"})})})};export{se as default};
