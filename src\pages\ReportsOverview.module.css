/* الأساسيات */
@import '../styles/variables.css';

/* الهيكل العام */
.pageContainer {
  display: flex;
  flex-direction: column;
  min-height: 100vh;
  font-family: var(--font-family-primary);
 
}



/* المحتوى الرئيسي */
.contentContainer {
  padding: 12px;
  margin-top: 15px;
  max-width: 1200px;
  margin-left: auto;
  margin-right: auto;
  width: 100%;
}

.controlsContainer {
  display: flex;
  justify-content: center; /* توسيط المحتوى */
  width: 100%;
  align-items: center;
  margin-bottom: 40px;
  background-color: transparent;
  padding: 0;
  border: none;
  box-shadow: none;
  gap: 8px; /* مسافة صغيرة بين العناصر */
}

/* البحث */
.searchBox {
  position: relative;
  width: 640px; /* تكبير العرض بنسبة 10% (584 * 1.1 = 640) */
}

.searchIcon {
  position: absolute;
  right: 12px;
  top: 50%;
  transform: translateY(-50%);
  color: rgb(0, 0, 0);
  font-size: 16px;
}

.searchInput {
  width: 640px; /* تكبير العرض بنسبة 10% */
  padding: 8px 36px 8px 10px;
  border: 1px solid var(--neutral-200);
  border-radius: 10px; /* حواف دائرية أكثر */
  font-size: 14px;
  background-color: var(--page-background);
  transition: var(--transition-normal);
  height: 52px;
  padding-left: 160px; /* مساحة متوازنة لزر القائمة والأيقونات */
  box-shadow: 
    0 20px 60px var(--shadow-medium),
    0 10px 30px var(--shadow-light); /* نفس shadow الكروت */
}

.searchInput:focus {
  outline: none;
  border-color: var(--primary-color);
  box-shadow: 
    0 25px 70px var(--shadow-medium),
    0 15px 40px var(--shadow-light); /* نفس shadow الكروت عند hover */
  background-color: var(--page-background);
}

.viewIcons {
  position: absolute;
  left: 80px; /* تقريب الأيقونات من زر القائمة */
  top: 50%;
  transform: translateY(-50%);
  display: flex;
  gap: 16px; /* تقليل المسافة بين الأيقونات */
}

.viewIcon {
  font-size: 18px;
  color: #94a3b8;
  cursor: pointer;
  transition: var(--transition);
}

.viewIcon:hover {
  color: #64748b;
}

.activeIcon {
  color: #11192a;
}

/* زر القائمة داخل خانة البحث */
.toggleCasesButton {
  position: absolute;
  left: 8px;
  top: 50%;
  transform: translateY(-50%);
  width: auto;
  height: 32px;
  padding: 0 12px;
  background: linear-gradient(135deg, var(--neutral-700) 0%, var(--neutral-800) 100%);
  color: white;
  border: none;
  border-radius: 8px;
  cursor: pointer;
  display: flex;
  align-items: center;
  justify-content: center;
  font-size: 15px;
  font-weight: 900;
  box-shadow: 0 2px 8px rgba(102, 126, 234, 0.25);
  z-index: 3; /* زيادة z-index ليكون فوق الأيقونات */
  white-space: nowrap;
}

.toggleCasesButton:hover {

  box-shadow: 0 4px 12px rgba(102, 126, 234, 0.35);
  background: linear-gradient(135deg, var(--neutral-600) 0%, var(--neutral-700) 100%);
}

.toggleCasesButton:active {
  box-shadow: 0 2px 10px rgba(102, 126, 234, 0.3);
}







.gridDot {
  width: 4px;
  height: 4px;
  background-color: currentColor;
  border-radius: 50%;
  transition: all 0.3s ease;
}








/* عرض البطاقات - حل نهائي للفجوات */
.casesGrid {
  column-count: 3;
  column-gap: 20px;
  column-fill: balance;
}

/* للشاشات المتوسطة - عمودين */
@media (max-width: 999px) and (min-width: 600px) {
  .casesGrid {
    column-count: 2;
  }

  .toggleCasesButton {
    width: 40px;
    height: 40px;
  }

  .viewIcons {
    left: 70px; /* موقع متوسط للشاشات المتوسطة */
    gap: 14px;
  }

  .viewIcon {
    font-size: 17px;
  }
}

/* للشاشات الصغيرة - عمود واحد */
@media (max-width: 599px) {
  .casesGrid {
    column-count: 1;
  }
}



.cardHeader {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 16px;
  border-bottom: 1px solid var(--neutral-200);
  padding-bottom: 12px;
}

.caseNumber {
  font-size: 23px;
  font-weight: var(--font-weight-medium);
  color: var(--neutral-900);
  display: flex;
  align-items: center;
  gap: 8px;
  margin-bottom: 8px;
}

.caseIcon {
  color: var(--primary-color);
  font-size: 18px;
}

.reportDate {
  font-size: 18px;
  color: var(--neutral-600);
  display: flex;
  align-items: center;
  gap: 6px;
}

.cardBody {
  display: flex;
  flex-direction: column;
  gap: 16px;
}

.infoGroup {
  display: flex;
  align-items: center;
  gap: 10px;
  font-size: 19px;
  margin-bottom: 12px;
}

.infoIcon {
  color: var(--primary-color);
  font-size: 23px;
}

.infoLabel {
  font-weight: var(--font-weight-medium);
  color: var(--secondary-color);
  margin-left: 4px;
}

.infoValue {
  color: var(--neutral-800);
  font-weight: var(--font-weight-normal);
}

.previewContainer {
  margin-top: 12px;
}



.sectionTitle {
  font-size: 21px;
  font-weight: var(--font-weight-medium);
  color: var(--neutral-700);
  margin-bottom: 12px;
  margin-top: 0px;
}



.actionIcon {
  color: var(--secondary-color);
  font-size: 18px;
}

.linkedInfo {
  font-size: 16px;
  color: #555269;
  display: flex;
  align-items: center;
  gap: 4px;
  margin-top: 6px;
  background-color: var(--neutral-100);
  padding: 4px 8px;
  border-radius: var(--radius-sm);
}

.linkIcon {
  font-size: 16px;
}

/* تصميم قوائم الإجراءات والتأجيلات الجديدة */
.actionsSection, .deferralsSection {
  margin-bottom: 16px;
}

.itemsList {
  display: flex;
  flex-direction: column;
  gap: 8px;
}

.actionItem {
  display: flex;
  align-items: flex-start;
  gap: 14px;
  padding: 16px;
  background: linear-gradient(135deg, #f3fff3, #d6f9f9);
  border-radius: 12px;
  border: 1px solid #e2e8f0;
  transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
  margin-bottom: 10px;
  position: relative;
  overflow: hidden;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.06);
}

.actionItem::before {
  content: '';
  position: absolute;
  right: 0;
  top: 0;
  bottom: 0;
  width: 4px;
  transition: width 0.3s ease;
}

.actionItem:hover {
  background: linear-gradient(135deg, #f1f5f9, #e2e8f0);
  border-color: var(--primary-color);
  box-shadow: 
    0 8px 25px rgba(0, 0, 0, 0.1),
    0 4px 10px rgba(59, 130, 246, 0.15);
  transform: translateY(-2px);
}

.actionItem:hover::before {
  width: 6px;
}

.deferralItem {
  display: flex;
  align-items: flex-start;
  gap: 14px;
  padding: 16px;
  background: linear-gradient(135deg, #fef7f0, #fdf2f8);
  border-radius: 12px;
  border: 1px solid #f3e8ff;
  transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
  margin-bottom: 10px;
  position: relative;
  overflow: hidden;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.06);
}

.deferralItem::before {
  content: '';
  position: absolute;
  right: 0;
  top: 0;
  bottom: 0;
  width: 4px;
  background: linear-gradient(180deg, var(--warning-600), #f59e0b);
  transition: width 0.3s ease;
}

.deferralItem:hover {
  background: linear-gradient(135deg, #fdf2f8, #f3e8ff);
  border-color: var(--warning-700);
  box-shadow: 
    0 8px 25px rgba(0, 0, 0, 0.1),
    0 4px 10px rgba(245, 158, 11, 0.15);
  transform: translateY(-2px);
}

.deferralItem:hover::before {
  width: 6px;
}

.itemContent {
  flex: 1;
  display: flex;
  flex-direction: column;
  gap: 4px;
}

/* السطر الأول: وصف الإجراء والتاريخ */
.itemContent > .itemText {
  font-size: 14px;
  color: var(--neutral-600);
  line-height: var(--line-height-normal);
  font-weight: var(--font-weight-normal);
  display: flex;
  justify-content: space-between;
  align-items: center;
  gap: 12px;
}

.itemDate {
  font-size: 13px;
  color: var(--neutral-500);
  font-weight: var(--font-weight-normal);
  flex-shrink: 0;
  margin-top: 0;
}

.deferralIcon {
  color: var(--warning-700);
  font-size: 14px;
  margin-top: 2px;
}

.moreItems {
  font-size: 12px;
  color: var(--neutral-600);
  text-align: center;
  padding: 8px;
  background-color: var(--neutral-50);
  border-radius: var(--radius-sm);
  border: 1px dashed var(--neutral-300);
  font-style: italic;
}

/* حالة عدم وجود قضايا */
.noCases {
  text-align: center;
  font-size: 16px;
  color: var(--neutral-600);
  padding: 2rem;
  background-color: var(--page-background);
  border-radius: 20px;
  border: none;
  overflow: hidden;
}

/* انيميشن */
@keyframes fadeIn {
  from { opacity: 0; transform: translateY(10px); }
  to { opacity: 1; transform: translateY(0); }
}

/* موبايل */
@media (max-width: 768px) {
  .contentContainer {
    padding: 12px;
    margin-top: 15px;
  }

  .searchBox {
    width: 90%;
    max-width: 400px;
  }

  .searchInput {
    width: 100%;
    padding-left: 120px; /* مساحة أقل في الموبايل */
  }

  .viewIcons {
    left: 70px; /* أقرب في الموبايل */
    gap: 12px; /* مسافة أقل بين الأيقونات */
  }

  .viewIcon {
    font-size: 16px; /* أيقونات أصغر في الموبايل */
  }

  .controlsContainer {
    padding: 8px;
    flex-direction: row; /* صف واحد في الموبايل أيضاً */
    gap: 8px;
    justify-content: center;
  }

  .toggleCasesButton {
    width: 50px;
  
  }

 

  .gridDot {
    width: 3.5px;
    height: 3.5px;
  }



  .casesGrid {
    column-count: 1;
    column-gap: 16px;
  }

  .caseCard {
    padding: 1.5rem;
    border-radius: var(--radius-md);
  }

  .caseNumber {
    font-size: 20px;
  }

  .reportDate {
    font-size: 16px;
  }

  .infoGroup {
    font-size: 17px;
  }

  .infoIcon {
    font-size: 21px;
  }

  .sectionTitle {
    font-size: 17px;
  }

  .linkedInfo {
    font-size: 14px;
  }


}

/* حاوية القضايا */
.casesContainer {
  width: 100%;
}

/* زر تحميل المزيد */
.loadMoreButton {
  display: block;
  margin: 20px auto;
  padding: 12px 24px;
  background-color: var(--primary-color);
  color: white;
  border: none;
  border-radius: var(--border-radius);
  cursor: pointer;
  font-size: 14px;
  font-weight: 700;
  transition: var(--transition-normal);
}

.loadMoreButton:hover {
  background-color: var(--primary-dark);
  transform: translateY(-1px);
}

.loadMoreButton:disabled {
  background-color: var(--neutral-400);
  cursor: not-allowed;
  transform: none;
}

.caseCard {
  background-color: var(--page-background);
  border-radius: 20px;
  padding: 2rem;
  box-shadow: 
    0 20px 60px var(--shadow-medium),
    0 10px 30px var(--shadow-light);
  cursor: pointer;
  transition: all var(--transition-normal);
  text-align: right;
  position: relative;
  display: block;
  width: 100%;
  border: none;
  animation: var(--animation-fade-in);
  overflow: hidden;
  margin-bottom: 20px;
  break-inside: avoid;
}

.caseCard:hover {
  transform: translateY(-4px);
  box-shadow: 
    0 25px 70px var(--shadow-medium),
    0 15px 40px var(--shadow-light);
}

/* شارة القضايا المشتركة */
.sharedBadge {
  position: absolute;
  top: 12px;
  left: 12px;
  background: linear-gradient(135deg, var(--primary-color), var(--primary-dark));
  color: white;
  padding: 6px 12px;
  border-radius: var(--radius-sm);
  font-size: 12px;
  font-weight: var(--font-weight-medium);
  display: flex;
  align-items: center;
  gap: 4px;
  box-shadow: var(--shadow-sm);
  z-index: 1;
  transition: all var(--transition-normal);
}

.sharedBadge:hover {
  transform: scale(1.05);
  box-shadow: var(--shadow-md);
}

.cardBorderPending,
.cardBorderReport,
.cardBorderLawsuit,
.cardBorderDefault {
  border: 1px solid var(--neutral-200);
}

.cardBorderPending:hover,
.cardBorderReport:hover,
.cardBorderLawsuit:hover,
.cardBorderDefault:hover {
  border-color: var(--primary-color);
}

/* الحاوية الخاصة بالجدول */
.casesTableContainer {
  width: 100%;
  overflow-x: auto;
  margin: 0 auto;
  padding: 0;
  border-radius: var(--radius-md);
  background-color: var(--page-background);
  box-shadow: var(--shadow-sm);
  border: 1px solid var(--neutral-200);
  overflow: hidden;
}

/* الجدول نفسه */
.casesTable {
  width: 100%;
  border-collapse: collapse;
  font-size: 18px;
  background-color: var(--page-background);
  color: #334155;
}

/* رأس الجدول */
.casesTable thead {
  background-color: var(--neutral-100);
  border-bottom: 1px solid var(--neutral-200);
}

.casesTable th {
  padding: 12px 16px;
  text-align: center;
  font-weight: var(--font-weight-semibold);
  color: var(--neutral-700);
  white-space: nowrap;
}

/* صفوف الجدول */
.casesTable tbody tr {
  border-bottom: 1px solid var(--neutral-200);
  cursor: pointer;
  transition: all var(--transition-normal);
}

.casesTable tbody tr:hover {
  background-color: var(--neutral-100);
}

/* الخلايا */
.casesTable td {
  padding: 12px 16px;
  text-align: center;
  color: var(--neutral-800);
  overflow: hidden;
  text-overflow: ellipsis;
  white-space: nowrap;
}

/* ضبط عرض الأعمدة */
.casesTable td:nth-child(1) {
  width: 35%;
}

.casesTable td:nth-child(2) {
  width: 25%;
}

.casesTable td:nth-child(3) {
  width: 40%;
}

/* تنسيق التاريخ */
.dateText {
  font-size: 16px;
  color: var(--neutral-600);
  display: block;
}



/* تصميم متجاوب للهاتف */
@media (max-width: 600px) {
  .casesTable {
    font-size: 16px;
  }

  .casesTable th,
  .casesTable td {
    padding: 8px 6px;
  }

  .casesTable td:nth-child(1),
  .casesTable td:nth-child(2),
  .casesTable td:nth-child(3) {
    width: auto;
  }

  .casesTable td {
    overflow: hidden;
    text-overflow: ellipsis;
    max-width: 0;
  }

  .dateText {
    font-size: 10px;
  }
}
