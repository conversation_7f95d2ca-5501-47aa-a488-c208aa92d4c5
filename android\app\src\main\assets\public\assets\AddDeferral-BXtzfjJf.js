import{r as n,j as t,e as S,b as g,q as F,w as q,f as k,m as E}from"./index-HUlFBKIW.js";import{g as $,l as A,m as O}from"./index-DhfUDJs-.js";import{S as r}from"./sweetalert2.esm.all-DWO__QVt.js";import{s as e}from"./AddDeferralAction.module-B_E75l-L.js";import"./iconBase-BPj5F03O.js";const W=({currentUser:l,onSave:y,onClose:N,isUnderConsideration:L})=>{const[u,B]=n.useState(""),[o,d]=n.useState([]),[m,v]=n.useState(""),[p,c]=n.useState(null),[h,C]=n.useState([]),[R,f]=n.useState(!1),[P,z]=n.useState(!1),[M,V]=n.useState(""),x=async()=>{if(!l||!l.uid){c("لا يوجد مستخدم مسجل الدخول.");return}f(!0);try{const s=S(g,"deferralTemplates"),a=F(s,q("userId","==",l.uid)),j=(await k(a)).docs.map(w=>({id:w.id,...w.data()}));C(j)}catch(s){c("خطأ في جلب القوالب: "+(s.message||"غير معروف"))}finally{f(!1)}};n.useEffect(()=>{x()},[l]);const b=s=>{d(a=>a.includes(s)?a.filter(i=>i!==s):[...a,s])},T=async()=>{const{value:s}=await r.fire({title:"أدخل سبب التأجيل الجديد",input:"text",inputPlaceholder:"مثال: لتقديم مذكرة دفاع",showCancelButton:!0,confirmButtonText:"إضافة",cancelButtonText:"إلغاء",inputValidator:a=>{if(!a)return"لا يمكنك ترك السبب فارغاً!"},customClass:{popup:e["swal2-popup"],title:e["swal2-title"],htmlContainer:e["swal2-html-container"],input:e["swal2-input"],confirmButton:e["swal2-confirm"],cancelButton:e["swal2-cancel"],actions:e["swal2-actions"]},buttonsStyling:!1});if(s){const a=s.trim();a!==""&&(o.includes(a)||d([...o,a]),r.fire({title:"حفظ القالب",text:"هل تريد حفظ هذا السبب كقالب لاستخدامه لاحقاً؟",icon:"question",showCancelButton:!0,confirmButtonText:"نعم, احفظه",cancelButtonText:"لا, شكراً",customClass:{popup:e["swal2-popup"],title:e["swal2-title"],htmlContainer:e["swal2-html-container"],confirmButton:e["swal2-confirm"],cancelButton:e["swal2-cancel"],actions:e["swal2-actions"]},buttonsStyling:!1}).then(async i=>{if(i.isConfirmed)try{await E(S(g,"deferralTemplates"),{userId:l.uid,category:a,actions:[],createdAt:new Date().toISOString()}),r.fire("تم الحفظ!","تم حفظ القالب بنجاح.","success"),x()}catch{r.fire("خطأ","حدث خطأ أثناء حفظ القالب.","error")}}))}},I=n.useMemo(()=>h.filter(s=>(s.category||s.reason)!=="أخرى").map(s=>{const a=s.category||s.reason;return t.jsx("button",{className:`${e.reasonButton} ${o.includes(a)?e.selected:""}`,onClick:()=>b(a),type:"button",children:a},s.id)}),[h,o]),D=async()=>{try{await y(u,o,m,c)}catch(s){c("خطأ أثناء حفظ التأجيل: "+(s.message||"غير معروف"))}};return t.jsxs("div",{className:e.addReportForm,children:[p&&t.jsx("div",{style:{color:"red",marginBottom:"15px"},children:p}),t.jsxs("div",{className:e.dateReasonSection,children:[t.jsxs("div",{className:e.verticalSection,children:[t.jsxs("div",{className:e.dateField,children:[t.jsx("label",{children:"تاريخ التأجيلة:"}),t.jsx("input",{type:"date",value:u,onChange:s=>B(s.target.value),className:e.actionInput,min:new Date().toISOString().split("T")[0]})]}),t.jsxs("div",{className:e.dateField,children:[t.jsxs("label",{className:e.optionalLabel,children:["وصف التأجيل",t.jsx("span",{className:e.optional,children:"(اختياري)"})]}),t.jsx("textarea",{value:m,onChange:s=>v(s.target.value),className:e.actionInput,placeholder:"أدخل تفاصيل إضافية عن التأجيل...",rows:3})]})]}),t.jsxs("div",{className:e.reasonSection,children:[t.jsx("label",{children:"أسباب التأجيلة:"}),t.jsx("div",{className:e.selectedReasons,children:o.length>0?o.join(", "):t.jsx("span",{className:e.noSelection,children:"لم يتم اختيار أسباب"})}),R?t.jsx("div",{style:{color:"#888",margin:"10px 0"},children:"جاري تحميل القوالب..."}):t.jsxs("div",{className:e.reasonButtons,children:[I,t.jsxs("button",{className:`${e.reasonButton} ${e.addOtherButton}`,onClick:T,type:"button",children:[t.jsx($,{})," أخرى"]})]})]})]}),t.jsxs("div",{className:e.reportFormButtons,children:[t.jsxs("button",{onClick:D,className:e.saveButton,children:[t.jsx(A,{className:e.buttonIcon}),t.jsx("span",{children:"حفظ الإضافة"})]}),t.jsxs("button",{onClick:N,className:e.cancelButton,children:[t.jsx(O,{className:e.buttonIcon}),t.jsx("span",{children:"إلغاء"})]})]})]})};export{W as default};
