import{r as t,j as e}from"./index-HUlFBKIW.js";import{q as h,m as p}from"./index-DhfUDJs-.js";import{s}from"./AddDeferralAction.module-B_E75l-L.js";import"./iconBase-BPj5F03O.js";const y=({onSave:d,onClose:m})=>{const[n,x]=t.useState(""),[l,j]=t.useState(new Date().toISOString().split("T")[0]),[a,r]=t.useState(""),[o,c]=t.useState(null),u=()=>{if(!n||!l||!a){c("يرجى ملء جميع الحقول المطلوبة.");return}d(n,l,"custom","","",a,c)};return e.jsxs("div",{className:s.addActionForm,children:[o&&e.jsx("div",{className:s.error<PERSON><PERSON>t,children:o}),e.jsxs("div",{className:s.actionField,children:[e.jsx("label",{children:"وصف الإجراء:"}),e.jsx("input",{type:"text",value:n,onChange:i=>x(i.target.value),placeholder:"أدخل وصف الإجراء",className:s.actionInput})]}),e.jsxs("div",{className:s.actionField,children:[e.jsx("label",{children:"تاريخ الإجراء:"}),e.jsx("input",{type:"date",value:l,onChange:i=>j(i.target.value),className:s.actionInput,min:new Date().toISOString().split("T")[0]})]}),e.jsxs("div",{className:s.actionField,children:[e.jsx("label",{children:"توقيت الإشعار:"}),e.jsxs("div",{className:s.reminderTags,children:[e.jsxs("div",{className:`${s.reminderTag} ${a==="daily"?s.selectedTag:""}`,onClick:()=>r(a==="daily"?"":"daily"),children:[e.jsx("span",{className:s.tagIcon,children:"🔔"}),e.jsx("span",{className:s.tagText,children:"إشعار يومي"})]}),e.jsxs("div",{className:`${s.reminderTag} ${a==="dayBefore"?s.selectedTag:""}`,onClick:()=>r(a==="dayBefore"?"":"dayBefore"),children:[e.jsx("span",{className:s.tagIcon,children:"⏰"}),e.jsx("span",{className:s.tagText,children:"إشعار قبلها بيوم"})]})]})]}),e.jsxs("div",{className:s.actionFormButtons,children:[e.jsxs("button",{onClick:u,className:s.addActionButton,children:[e.jsx(h,{className:s.buttonIcon}),e.jsx("span",{children:"إضافة تنبيه بإجراء"})]}),e.jsxs("button",{onClick:()=>{m(),c(null)},className:s.cancelButton,children:[e.jsx(p,{className:s.buttonIcon}),e.jsx("span",{children:"إلغاء"})]})]})]})};export{y as default};
