class h{constructor(){this.listeners=new Map,this.cacheKeys={NOTIFICATIONS:"notifications_",CASE_DETAILS:"case_",DASHBOARD_STATS:"dashboard_stats_"}}addListener(e,t){this.listeners.has(e)||this.listeners.set(e,new Set),this.listeners.get(e).add(t)}removeListener(e,t){this.listeners.has(e)&&this.listeners.get(e).delete(t)}notifyListeners(e,t){this.listeners.has(e)&&this.listeners.get(e).forEach(s=>{try{s(t)}catch(i){console.error("Error in cache listener:",i)}})}clearCache(e){Object.keys(localStorage).forEach(s=>{s.includes(e)&&localStorage.removeItem(s)})}clearUserCache(e){[`${this.cacheKeys.NOTIFICATIONS}${e}`,`${this.cacheKeys.CASE_DETAILS}`,`${this.cacheKeys.DASHBOARD_STATS}${e}`].forEach(s=>this.clearCache(s))}updateCache(e,t,s=3e4){const i={data:t,timestamp:Date.now(),ttl:s};localStorage.setItem(e,JSON.stringify(i)),this.notifyListeners(e,t)}getCache(e){try{const t=localStorage.getItem(e);if(!t)return null;const{data:s,timestamp:i,ttl:c}=JSON.parse(t);return Date.now()-i>c?(localStorage.removeItem(e),null):s}catch(t){return console.error("Error reading cache:",t),localStorage.removeItem(e),null}}notifyDataUpdate(e,t,s=null){switch(e){case"TASK_COMPLETED":this.clearCache(`${this.cacheKeys.NOTIFICATIONS}${t}`),s&&this.clearCache(`${this.cacheKeys.CASE_DETAILS}${s}`),this.notifyListeners("notifications_refresh",{userId:t}),this.notifyListeners("case_refresh",{caseId:s});break;case"TASK_CREATED":this.clearCache(`${this.cacheKeys.NOTIFICATIONS}${t}`),this.notifyListeners("notifications_refresh",{userId:t});break;case"CASE_CREATED":this.clearCache(`${this.cacheKeys.NOTIFICATIONS}${t}`),this.clearCache("cases_list"),this.clearCache("dashboard_stats_"),this.notifyListeners("notifications_refresh",{userId:t}),this.notifyListeners("cases_refresh",{userId:t}),this.notifyListeners("dashboard_refresh",{userId:t});break;case"CASE_UPDATED":s&&(this.clearCache(`${this.cacheKeys.CASE_DETAILS}${s}`),this.notifyListeners("case_refresh",{caseId:s}));break}}}const r=new h,n=(a,e)=>{r.notifyDataUpdate("TASK_COMPLETED",a,e)},o=a=>{r.notifyDataUpdate("TASK_CREATED",a)},l=a=>{r.notifyDataUpdate("CASE_CREATED",a)};export{r as cacheManager,l as notifyCaseCreated,n as notifyTaskCompleted,o as notifyTaskCreated};
