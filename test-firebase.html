<!DOCTYPE html>
<html lang="ar" dir="rtl">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>اختبار Firebase</title>
    <style>
        body {
            font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
            max-width: 800px;
            margin: 0 auto;
            padding: 20px;
            background-color: #f5f5f5;
        }
        .container {
            background: white;
            padding: 30px;
            border-radius: 10px;
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
        }
        h1 {
            color: #333;
            text-align: center;
            margin-bottom: 30px;
        }
        .form-group {
            margin-bottom: 20px;
        }
        label {
            display: block;
            margin-bottom: 5px;
            font-weight: bold;
            color: #555;
        }
        input {
            width: 100%;
            padding: 10px;
            border: 1px solid #ddd;
            border-radius: 5px;
            font-size: 16px;
            box-sizing: border-box;
        }
        button {
            background-color: #4CAF50;
            color: white;
            padding: 12px 24px;
            border: none;
            border-radius: 5px;
            cursor: pointer;
            font-size: 16px;
            width: 100%;
            margin-top: 10px;
        }
        button:hover {
            background-color: #45a049;
        }
        button:disabled {
            background-color: #cccccc;
            cursor: not-allowed;
        }
        .log {
            background-color: #f8f9fa;
            border: 1px solid #e9ecef;
            border-radius: 5px;
            padding: 15px;
            margin-top: 20px;
            max-height: 400px;
            overflow-y: auto;
            font-family: 'Courier New', monospace;
            font-size: 14px;
            white-space: pre-wrap;
        }
        .success {
            color: #28a745;
        }
        .error {
            color: #dc3545;
        }
        .info {
            color: #17a2b8;
        }
    </style>
</head>
<body>
    <div class="container">
        <h1>🔥 اختبار حفظ البيانات في Firebase</h1>
        
        <div class="form-group">
            <label for="email">البريد الإلكتروني:</label>
            <input type="email" id="email" placeholder="أدخل بريدك الإلكتروني">
        </div>
        
        <div class="form-group">
            <label for="password">كلمة المرور:</label>
            <input type="password" id="password" placeholder="أدخل كلمة المرور">
        </div>
        
        <button onclick="testFirebase()" id="testBtn">🧪 بدء الاختبار</button>
        
        <div id="log" class="log"></div>
    </div>

    <!-- Firebase SDK -->
    <script type="module">
        import { initializeApp } from 'https://www.gstatic.com/firebasejs/10.7.1/firebase-app.js';
        import { getAuth, signInWithEmailAndPassword } from 'https://www.gstatic.com/firebasejs/10.7.1/firebase-auth.js';
        import { getFirestore, collection, addDoc } from 'https://www.gstatic.com/firebasejs/10.7.1/firebase-firestore.js';

        const firebaseConfig = {
            apiKey: "AIzaSyCM9JU8gQuL6uCuVVMw04p3RxnEF5-dWq8",
            authDomain: "agendicial.firebaseapp.com",
            projectId: "agendicial",
            storageBucket: "agendicial.firebasestorage.app",
            messagingSenderId: "986949638500",
            appId: "1:986949638500:web:39bc42c6ac795503850271",
            measurementId: "G-Q6STWWKFZV"
        };

        // تهيئة Firebase
        const app = initializeApp(firebaseConfig);
        const auth = getAuth(app);
        const db = getFirestore(app);

        function log(message, type = 'info') {
            const logElement = document.getElementById('log');
            const timestamp = new Date().toLocaleTimeString('ar-EG');
            const className = type === 'error' ? 'error' : type === 'success' ? 'success' : 'info';
            logElement.innerHTML += `<span class="${className}">[${timestamp}] ${message}</span>\n`;
            logElement.scrollTop = logElement.scrollHeight;
        }

        window.testFirebase = async function() {
            const email = document.getElementById('email').value;
            const password = document.getElementById('password').value;
            const testBtn = document.getElementById('testBtn');
            
            if (!email || !password) {
                log('❌ يرجى إدخال البريد الإلكتروني وكلمة المرور', 'error');
                return;
            }
            
            testBtn.disabled = true;
            testBtn.textContent = 'جاري الاختبار...';
            document.getElementById('log').innerHTML = '';
            
            try {
                log('🔥 بدء اختبار Firebase...', 'info');
                
                // تسجيل الدخول
                log('🔐 محاولة تسجيل الدخول...', 'info');
                const userCredential = await signInWithEmailAndPassword(auth, email, password);
                const user = userCredential.user;
                
                log('✅ تم تسجيل الدخول بنجاح!', 'success');
                log(`👤 المستخدم: ${user.uid}`, 'info');
                log(`📧 الإيميل: ${user.email}`, 'info');
                log(`✅ الإيميل مُفعّل: ${user.emailVerified}`, 'info');
                
                // الحصول على التوكن
                const token = await user.getIdToken();
                log('🎫 تم الحصول على التوكن بنجاح', 'success');
                
                // بيانات قضية تجريبية
                const testCaseData = {
                    fullCaseNumber: `TEST-${Date.now()}`,
                    caseNumber: 'TEST',
                    caseYear: '2024',
                    clientName: 'عميل تجريبي',
                    opponentName: 'خصم تجريبي',
                    caseDescription: 'قضية تجريبية لاختبار النظام',
                    caseCategory: 'مدني',
                    caseDegree: 'ابتدائي',
                    courtLocation: 'محكمة تجريبية',
                    circleNumber: '1',
                    caseDate: new Date().toISOString().split('T')[0],
                    caseStatus: 'قيد النظر',
                    reportNumber: null,
                    reportLocation: null,
                    deferrals: [],
                    actions: [],
                    history: [],
                    createdAt: new Date().toISOString(),
                    updatedAt: new Date().toISOString(),
                    userId: user.uid,
                    originalCaseId: null,
                    originalCaseDegree: null,
                    originalCaseNumber: null,
                    isTransferredCase: false
                };
                
                log('📄 إنشاء بيانات قضية تجريبية...', 'info');
                
                // محاولة حفظ البيانات
                log('💾 محاولة حفظ القضية في Firestore...', 'info');
                const casesRef = collection(db, 'cases');
                const docRef = await addDoc(casesRef, testCaseData);
                
                log('🎉 تم حفظ القضية بنجاح!', 'success');
                log(`🆔 معرف القضية: ${docRef.id}`, 'success');
                log('✅ الاختبار مكتمل بنجاح!', 'success');
                
            } catch (error) {
                log('❌ خطأ في اختبار Firebase:', 'error');
                log(`نوع الخطأ: ${error.name}`, 'error');
                log(`رمز الخطأ: ${error.code}`, 'error');
                log(`رسالة الخطأ: ${error.message}`, 'error');
                console.error('تفاصيل الخطأ:', error);
            } finally {
                testBtn.disabled = false;
                testBtn.textContent = '🧪 بدء الاختبار';
            }
        };
    </script>
</body>
</html>
