// خدمة إدارة التخزين المحلي والوصول إلى البيانات
import { db } from '../config/firebaseConfig';
import { collection, query, where, getDocs, doc, getDoc, addDoc, updateDoc, writeBatch } from 'firebase/firestore';

// مفاتيح التخزين المحلي
const ACTIVE_ACCOUNT_KEY = 'activeAccount';
const LOCAL_USER_DATA_PREFIX = 'localUserData_';
const LOCAL_CASES_PREFIX = 'localCases_';
const LOCAL_CASE_PREFIX = 'localCase_';
const LOCAL_NOTIFICATIONS_PREFIX = 'localNotifications_';

// الحصول على الحساب النشط (أونلاين أو محلي)
export const getActiveAccount = () => {
  return localStorage.getItem(ACTIVE_ACCOUNT_KEY) || 'online';
};

// تعيين الحساب النشط
export const setActiveAccount = (accountType) => {
  // التأكد من أن القيمة صحيحة
  if (accountType !== 'online' && accountType !== 'local') {
    console.error('قيمة غير صالحة للحساب النشط:', accountType);
    return;
  }

  // حفظ الحساب النشط في التخزين المحلي
  localStorage.setItem(ACTIVE_ACCOUNT_KEY, accountType);
  console.log('تم تغيير الحساب النشط إلى:', accountType);
};

// الحصول على بيانات المستخدم المحلية
export const getLocalUserData = (userId) => {
  const data = localStorage.getItem(`${LOCAL_USER_DATA_PREFIX}${userId}`);
  return data ? JSON.parse(data) : null;
};

// حفظ بيانات المستخدم المحلية
export const saveLocalUserData = (userId, userData) => {
  localStorage.setItem(`${LOCAL_USER_DATA_PREFIX}${userId}`, JSON.stringify(userData));
};

// حذف بيانات المستخدم المحلية
export const deleteLocalUserData = (userId) => {
  localStorage.removeItem(`${LOCAL_USER_DATA_PREFIX}${userId}`);
};

// الحصول على قائمة القضايا المحلية
export const getLocalCases = (userId) => {
  const data = localStorage.getItem(`${LOCAL_CASES_PREFIX}${userId}`);
  return data ? JSON.parse(data) : [];
};

// حفظ قائمة القضايا المحلية
export const saveLocalCases = (userId, cases) => {
  localStorage.setItem(`${LOCAL_CASES_PREFIX}${userId}`, JSON.stringify(cases));
};

// الحصول على بيانات قضية محلية محددة
export const getLocalCase = (userId, caseId) => {
  const data = localStorage.getItem(`${LOCAL_CASE_PREFIX}${userId}_${caseId}`);
  if (data) {
    const parsedData = JSON.parse(data);
    // التأكد من وجود معرف القضية
    if (!parsedData.id) {
      parsedData.id = caseId;
    }
    return parsedData;
  }
  return null;
};

// حفظ بيانات قضية محلية محددة
export const saveLocalCase = (userId, caseId, caseData) => {
  // حفظ القضية الفردية فقط
  localStorage.setItem(`${LOCAL_CASE_PREFIX}${userId}_${caseId}`, JSON.stringify(caseData));
};

// تحديث قائمة القضايا المحلية (يستدعى عند الحاجة فقط)
export const updateLocalCasesList = (userId, caseId, caseData) => {
  const cases = getLocalCases(userId);
  const existingIndex = cases.findIndex(c => c.id === caseId);

  if (existingIndex !== -1) {
    cases[existingIndex] = { ...caseData, id: caseId };
  } else {
    cases.push({ ...caseData, id: caseId });
  }

  saveLocalCases(userId, cases);
};

// حذف قضية محلية محددة
export const deleteLocalCase = (userId, caseId) => {
  localStorage.removeItem(`${LOCAL_CASE_PREFIX}${userId}_${caseId}`);

  // تحديث قائمة القضايا المحلية أيضًا
  const cases = getLocalCases(userId);
  const updatedCases = cases.filter(c => c.id !== caseId);
  saveLocalCases(userId, updatedCases);
};

// الحصول على الإشعارات المحلية
export const getLocalNotifications = (userId) => {
  const data = localStorage.getItem(`${LOCAL_NOTIFICATIONS_PREFIX}${userId}`);
  return data ? JSON.parse(data) : [];
};

// حفظ الإشعارات المحلية
export const saveLocalNotifications = (userId, notifications) => {
  localStorage.setItem(`${LOCAL_NOTIFICATIONS_PREFIX}${userId}`, JSON.stringify(notifications));
};

// دالة موحدة للحصول على بيانات القضايا حسب الحساب النشط
export const getCases = async (userId) => {
  const activeAccount = getActiveAccount();

  if (activeAccount === 'online') {
    try {
      const casesRef = collection(db, 'cases');
      const q = query(casesRef, where('userId', '==', userId));
      const querySnapshot = await getDocs(q);
      const allCases = querySnapshot.docs.map(doc => ({ id: doc.id, ...doc.data() }));
      return allCases.filter(caseItem => !caseItem.isHidden);
    } catch (error) {
      console.error('خطأ في جلب بيانات القضايا من Firestore:', error);
      return [];
    }
  } else {
    console.log('🔍 جلب القضايا من التخزين المحلي للمستخدم:', userId);
    const allCases = getLocalCases(userId);
    console.log('📊 إجمالي القضايا ا��محلية قبل الفلترة:', allCases.length);
    
    // جلب القضايا الخاصة بالمستخدم فقط
    const filteredCases = allCases.filter(caseItem => !caseItem.isHidden);
    console.log('✅ القضايا المفلترة:', filteredCases.length);
    return filteredCases;
  }
};

// دالة للحصول على جميع القضايا بما في ذلك المخفية (للبحث عن القضايا المرتبطة)
export const getAllCases = async (userId) => {
  const activeAccount = getActiveAccount();

  if (activeAccount === 'online') {
    // الحصول على البيانات من Firestore
    try {
      const casesRef = collection(db, 'cases');
      const q = query(casesRef, where('userId', '==', userId));
      const querySnapshot = await getDocs(q);
      return querySnapshot.docs.map(doc => ({ id: doc.id, ...doc.data() }));
    } catch (error) {
      console.error('خطأ في جلب جميع بيانات القضايا من Firestore:', error);
      return [];
    }
  } else {
    // الحصول على البيانات من التخزين المحلي
    return getLocalCases(userId);
  }
};

// دالة موحدة للحصول على بيانات قضية محددة حسب الحساب النشط
export const getCase = async (userId, caseId) => {
  const activeAccount = getActiveAccount();

  if (activeAccount === 'online') {
    // الحصول على البيانات من Firestore
    try {
      const caseRef = doc(db, 'cases', caseId);
      const caseDoc = await getDoc(caseRef);

      if (caseDoc.exists() && caseDoc.data().userId === userId) {
        return { id: caseDoc.id, ...caseDoc.data() };
      } else {
        return null;
      }
    } catch (error) {
      console.error('خطأ في جلب بيانات القضية من Firestore:', error);
      return null;
    }
  } else {
    // الحصول على البيانات من التخزين المحلي
    return getLocalCase(userId, caseId);
  }
};

// دالة موحدة لإضافة قضية جديدة حسب الحساب النشط
export const addCase = async (userId, caseData) => {
  const activeAccount = getActiveAccount();

  if (activeAccount === 'online') {
    console.log('🔥 بدء عملية حفظ القضية في Firebase...');
    console.log('👤 معرف المستخدم:', userId);
    console.log('📄 بيانات القضية:', caseData);

    // التحقق من حالة المصادقة
    const { auth } = await import('../config/firebaseConfig');
    const currentUser = auth.currentUser;

    console.log('🔐 حالة المصادقة:');
    console.log('  - المستخدم الحالي:', currentUser ? 'موجود' : 'غير موجود');
    console.log('  - UID:', currentUser?.uid);
    console.log('  - الإيميل:', currentUser?.email);
    console.log('  - الإيميل مُفعّل:', currentUser?.emailVerified);
    console.log('  - التوكن متاح:', currentUser ? 'نعم' : 'لا');

    if (!currentUser) {
      console.error('❌ المستخدم غير مصادق عليه!');
      throw new Error('المستخدم غير مصادق عليه. يرجى تسجيل الدخول مرة أخرى.');
    }

    if (currentUser.uid !== userId) {
      console.error('❌ عدم تطابق معرف المستخدم!');
      console.error('  - معرف المستخدم المرسل:', userId);
      console.error('  - معرف المستخدم الحالي:', currentUser.uid);
      throw new Error('عدم تطابق معرف المستخدم.');
    }

    try {
      // التحقق من التوكن
      const token = await currentUser.getIdToken();
      console.log('🎫 تم الحصول على التوكن بنجاح:', token ? 'نعم' : 'لا');

      const casesRef = collection(db, 'cases');
      console.log('📁 مرجع مجموعة القضايا تم إنشاؤه');

      const docRef = await addDoc(casesRef, caseData);
      console.log('✅ تم حفظ القضية بنجاح في Firestore!');
      console.log('🆔 معرف القضية الجديد:', docRef.id);

      return { id: docRef.id, ...caseData };
    } catch (error) {
      console.error('❌ خطأ في حفظ القضية في Firestore:');
      console.error('  - نوع الخطأ:', error.name);
      console.error('  - رمز الخطأ:', error.code);
      console.error('  - رسالة الخطأ:', error.message);
      console.error('  - تفاصيل الخطأ:', error);

      // إضافة معلومات إضافية حسب نوع الخطأ
      if (error.code === 'permission-denied') {
        console.error('🚫 خطأ في الصلاحيات - تحقق من Firebase Security Rules');
      } else if (error.code === 'unauthenticated') {
        console.error('🔐 خطأ في المصادقة - المستخدم غير مصادق عليه');
      } else if (error.code === 'network-request-failed') {
        console.error('🌐 خطأ في الشبكة - تحقق من الاتصال بالإنترنت');
      }

      throw error;
    }
  } else {
    const caseId = `local_${Date.now()}`;
    const newCase = { ...caseData, id: caseId, userId };
    console.log('💾 حفظ قضية محلية جديدة:');
    console.log('🆔 معرف القضية:', caseId);
    console.log('👤 معرف المستخدم:', userId);
    console.log('📄 بيانات القضية:', newCase);

    saveLocalCase(userId, caseId, newCase);
    updateLocalCasesList(userId, caseId, newCase);

    // التحقق من الحفظ
    const savedCases = getLocalCases(userId);
    console.log('✅ إجمالي القضايا بعد الحفظ:', savedCases.length);
    console.log('🔍 القضية الجديدة محفوظة؟', savedCases.some(c => c.id === caseId));

    return newCase;
  }
};

// دالة موحدة لتحديث قضية حسب الحساب النشط
export const updateCase = async (userId, caseId, caseData) => {
  const activeAccount = getActiveAccount();

  if (activeAccount === 'online') {
    console.log('🔄 بدء عملية تحديث القضية في Firebase...');
    console.log('👤 معرف المستخدم:', userId);
    console.log('🆔 معرف القضية:', caseId);
    console.log('📄 بيانات التحديث:', caseData);

    // التحقق من حالة المصادقة
    const { auth } = await import('../config/firebaseConfig');
    const currentUser = auth.currentUser;

    console.log('🔐 حالة المصادقة:');
    console.log('  - المستخدم الحالي:', currentUser ? 'موجود' : 'غير موجود');
    console.log('  - UID:', currentUser?.uid);
    console.log('  - الإيميل:', currentUser?.email);
    console.log('  - الإيميل مُفعّل:', currentUser?.emailVerified);

    if (!currentUser) {
      console.error('❌ المستخدم غير مصادق عليه!');
      throw new Error('المستخدم غير مصادق عليه. يرجى تسجيل الدخول مرة أخرى.');
    }

    if (currentUser.uid !== userId) {
      console.error('❌ عدم تطابق معرف المستخدم!');
      console.error('  - معرف المستخدم المرسل:', userId);
      console.error('  - معرف المستخدم الحالي:', currentUser.uid);
      throw new Error('عدم تطابق معرف المستخدم.');
    }

    try {
      // التحقق من التوكن
      const token = await currentUser.getIdToken();
      console.log('🎫 تم الحصول على التوكن بنجاح:', token ? 'نعم' : 'لا');

      const caseRef = doc(db, 'cases', caseId);
      console.log('📁 مرجع القضية تم إنشاؤه');

      // تأكد من إرسال createdBy دائماً
      const caseDoc = await getDoc(caseRef);
      let createdBy = caseData.createdBy;
      if (!createdBy && caseDoc.exists()) {
        createdBy = caseDoc.data().createdBy;
      }
      if (!createdBy) {
        createdBy = userId;
      }

      const updateData = { ...caseData, createdBy, updatedAt: new Date().toISOString() };
      console.log('📝 بيانات التحديث النهائية:', updateData);

      await updateDoc(caseRef, updateData);
      console.log('✅ تم تحديث القضية بنجاح في Firestore!');

      return { id: caseId, ...updateData };
    } catch (error) {
      console.error('❌ خطأ في تحديث القضية في Firestore:');
      console.error('  - نوع الخطأ:', error.name);
      console.error('  - رمز الخطأ:', error.code);
      console.error('  - رسالة الخطأ:', error.message);
      console.error('  - تفاصيل الخطأ:', error);

      // إضافة معلومات إضافية حسب نوع الخطأ
      if (error.code === 'permission-denied') {
        console.error('🚫 خطأ في الصلاحيات - تحقق من Firebase Security Rules');
      } else if (error.code === 'unauthenticated') {
        console.error('🔐 خطأ في المصادقة - المستخدم غير مصادق عليه');
      } else if (error.code === 'not-found') {
        console.error('🔍 القضية غير موجودة في قاعدة البيانات');
      } else if (error.code === 'network-request-failed') {
        console.error('🌐 خطأ في الشبكة - تحقق من الاتصال بالإنترنت');
      }

      throw error;
    }
  } else {
    // تحديث القضية في التخزين المحلي
    console.log('💾 تحديث قضية محلية:');
    console.log('🆔 معرف القضية:', caseId);
    console.log('👤 معرف المستخدم:', userId);
    console.log('📄 بيانات التحديث:', caseData);

    const existingCase = getLocalCase(userId, caseId);
    if (!existingCase) {
      console.error('❌ القضية غير موجودة في التخزين المحلي');
      throw new Error('القضية غير موجودة في التخزين المحلي');
    }

    const updatedCase = { ...existingCase, ...caseData, updatedAt: new Date().toISOString() };
    saveLocalCase(userId, caseId, updatedCase);

    // تحديث قائمة القضايا المحلية أيضاً
    updateLocalCasesList(userId, caseId, updatedCase);

    console.log('✅ تم تحديث القضية المحلية بنجاح');
    return updatedCase;
  }
};

// دالة موحدة لحذف قضية حسب الحساب النشط
export const deleteCase = async (userId, caseId) => {
  const activeAccount = getActiveAccount();

  if (activeAccount === 'online') {
    console.log('🗑️ بدء عملية حذف القضية من Firebase...');
    console.log('👤 معرف المستخدم:', userId);
    console.log('🆔 معرف القضية:', caseId);

    // التحقق من حالة المصادقة
    const { auth } = await import('../config/firebaseConfig');
    const currentUser = auth.currentUser;

    console.log('🔐 حالة المصادقة:');
    console.log('  - المستخدم الحالي:', currentUser ? 'موجود' : 'غير موجود');
    console.log('  - UID:', currentUser?.uid);
    console.log('  - الإيميل:', currentUser?.email);
    console.log('  - الإيميل مُفعّل:', currentUser?.emailVerified);

    if (!currentUser) {
      console.error('❌ المستخدم غير مصادق عليه!');
      throw new Error('المستخدم غير مصادق عليه. يرجى تسجيل الدخول مرة أخرى.');
    }

    if (currentUser.uid !== userId) {
      console.error('❌ عدم تطابق معرف المستخدم!');
      console.error('  - معرف المستخدم المرسل:', userId);
      console.error('  - معرف المستخدم الحالي:', currentUser.uid);
      throw new Error('عدم تطابق معرف المستخدم.');
    }

    try {
      // التحقق من التوكن
      const token = await currentUser.getIdToken();
      console.log('🎫 تم الحصول على التوكن بنجاح:', token ? 'نعم' : 'لا');

      const { deleteDoc } = await import('firebase/firestore');
      const caseRef = doc(db, 'cases', caseId);
      console.log('📁 مرجع القضية تم إنشاؤه');

      await deleteDoc(caseRef);
      console.log('✅ تم حذف القضية بنجاح من Firestore!');

      return true;
    } catch (error) {
      console.error('❌ خطأ في حذف القضية من Firestore:');
      console.error('  - نوع الخطأ:', error.name);
      console.error('  - رمز الخطأ:', error.code);
      console.error('  - رسالة الخطأ:', error.message);
      console.error('  - تفاصيل الخطأ:', error);

      // إضافة معلومات إضافية حسب نوع الخطأ
      if (error.code === 'permission-denied') {
        console.error('🚫 خطأ في الصلاحيات - تحقق من Firebase Security Rules');
      } else if (error.code === 'unauthenticated') {
        console.error('🔐 خطأ في المصادقة - المستخدم غير مصادق عليه');
      } else if (error.code === 'not-found') {
        console.error('🔍 القضية غير موجودة في قاعدة البيانات');
      } else if (error.code === 'network-request-failed') {
        console.error('🌐 خطأ في الشبكة - تحقق من الاتصال بالإنترنت');
      }

      throw error;
    }
  } else {
    // حذف القضية من التخزين المحلي
    console.log('🗑️ حذف قضية محلية:');
    console.log('🆔 معرف القضية:', caseId);
    console.log('👤 معرف المستخدم:', userId);

    const existingCase = getLocalCase(userId, caseId);
    if (!existingCase) {
      console.error('❌ القضية غير موجودة في التخزين المحلي');
      throw new Error('القضية غير موجودة في التخزين المحلي');
    }

    deleteLocalCase(userId, caseId);
    console.log('✅ تم حذف القضية المحلية بنجاح');
    return true;
  }
};

// دالة محسنة لتحديث القضية مع عمليات مجمعة للأداء الأفضل
export const updateCaseOptimized = async (userId, caseId, updates) => {
  const activeAccount = getActiveAccount();

  if (activeAccount === 'online') {
    // استخدام عمليات مجمعة في Firestore للأداء الأفضل
    try {
      const batch = writeBatch(db);
      const caseRef = doc(db, 'cases', caseId);

      // تحسين: تجنب التحديث إذا لم تكن هناك تغييرات
      const existingDoc = await getDoc(caseRef);
      if (existingDoc.exists()) {
        const existingData = existingDoc.data();
        const hasChanges = Object.keys(updates).some(key => {
          const oldValue = existingData[key];
          const newValue = updates[key];

          if (Array.isArray(oldValue) && Array.isArray(newValue)) {
            return JSON.stringify(oldValue) !== JSON.stringify(newValue);
          }
          return oldValue !== newValue;
        });

        if (!hasChanges) {
          console.log('لا توجد تغييرات للحفظ، تخطي العملية');
          return;
        }
      }

      // إضافة العملية للمجموعة
      batch.update(caseRef, {
        ...updates,
        updatedAt: new Date().toISOString(),
      });

      // تنفيذ العمليات المجمعة
      await batch.commit();

      console.log('✅ تم تحديث القضية بنجاح باستخدام العمليات المجمعة');
      return { id: caseId, ...updates };
    } catch (error) {
      console.error('خطأ في تحديث القضية في Firestore:', error);
      throw error;
    }
  } else {
    // استخدام التحديث العادي للتخزين المحلي
    return updateCase(userId, caseId, updates);
  }
};